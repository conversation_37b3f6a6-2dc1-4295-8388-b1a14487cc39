import asyncio
from contextlib import asynccontextmanager
from dotenv import load_dotenv
load_dotenv()
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from src.api.routes import router
import uvicorn
import argparse
from src.utils.logger import setup_logger

# 解析命令行参数
parser = argparse.ArgumentParser(description="Twx API service")
parser.add_argument("--debug", "-d", action="store_true", help="Enable debug logging")
args = parser.parse_args()

# 配置日志
logger = setup_logger(
    debug=args.debug,
    log_file="logs/twx.log",
    module_name="twx"
)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时的初始化
    logger.info("Starting Twx API service...")
    logger.info("Initializing async components...")
    
    # 这里可以添加异步初始化逻辑，例如：
    # - 数据库连接池初始化
    # - Redis连接初始化  
    # - 后台任务启动
    # - 缓存预加载
    
    try:
        # 示例：初始化异步组件
        await init_async_components()
        logger.info("Async components initialized successfully")
        
        yield  # 应用运行期间
        
    finally:
        # 关闭时的清理
        logger.info("Shutting down Twx API service...")
        await cleanup_async_components()
        logger.info("Cleanup completed")

async def init_async_components():
    """初始化异步组件"""
    # 这里可以添加具体的异步初始化逻辑
    await asyncio.sleep(0.1)  # 模拟异步初始化

async def cleanup_async_components():
    """清理异步组件"""
    # 这里可以添加具体的清理逻辑
    await asyncio.sleep(0.1)  # 模拟异步清理

# 创建FastAPI应用实例，使用异步生命周期管理
app = FastAPI(
    title="Twx",
    description="智能 Twitter 监控分析平台",
    version="1.0.0",
    lifespan=lifespan
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # 允许的前端域名
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有 HTTP 方法
    allow_headers=["*"],  # 允许所有 HTTP 头
)

# 加载 API 路由
app.include_router(router)

async def start_server():
    """异步启动服务器"""
    config = uvicorn.Config(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="debug" if args.debug else "info",
        loop="asyncio"
    )
    server = uvicorn.Server(config)
    
    logger.info("Server running at: http://localhost:8000")
    logger.info("API Documentation: http://localhost:8000/docs")
    
    await server.serve()

if __name__ == "__main__":
    try:
        asyncio.run(start_server())
    except KeyboardInterrupt:
        logger.info("Received shutdown signal, stopping server...")