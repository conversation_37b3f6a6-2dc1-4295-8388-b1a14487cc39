import os
import json
import click
import requests
import asyncio
from dotenv import load_dotenv
load_dotenv()

from src.utils.logger import setup_logger

# 配置日志
logger = setup_logger(module_name="cli")

# Base URL for API commands (not used by webshare command)
BASE_URL = "http://127.0.0.1:8000/api"

@click.group()
@click.option("--debug", "-d", is_flag=True, help="Enable debug logging")
def cli(debug):
    """Twx CLI: Interact with Twx API for proxy and account management."""
    if debug:
        logger.setLevel("DEBUG")
        logger.debug("Debug logging enabled")

@cli.command()
@click.option("--account-id", required=True, help="Account ID to query")
def get_account(account_id):
    """View account details."""
    logger.debug(f"Querying account: {account_id}")
    try:
        response = requests.get(f"{BASE_URL}/accounts/{account_id}")
        response.raise_for_status()
        click.echo(f"Account details: {response.json()}")
        logger.debug(f"Account {account_id} queried successfully")
    except requests.RequestException as e:
        click.echo(f"Failed to get account: {e}")
        logger.error(f"Failed to query account {account_id}: {e}")

@cli.command()
@click.option("--api-key", default=None, help="Webshare API key (defaults to WEBSHARE_API_KEY env)")
def webshare(api_key):
    """Fetch proxies from Webshare API."""
    logger.debug("Running webshare command")
    api_key = api_key or os.getenv("WEBSHARE_API_KEY")
    if not api_key:
        click.echo("Error: WEBSHARE_API_KEY not provided or set in environment")
        logger.error("Webshare API key missing")
        return
    
    try:
        from src.config import Config
        from src.proxy.manager import ProxyManager
        
        config = Config()
        proxy_manager = ProxyManager(config.get_sqlite_path())
        
        logger.debug("Fetching proxies directly from Webshare API")
        proxy_list = proxy_manager.fetch_webshare_proxies(api_key)
        
        fetched_count = len(proxy_list)
        click.echo(f"Successfully fetched {fetched_count} proxies")
        logger.debug(f"Webshare command completed: {fetched_count} proxies fetched")
        
    except Exception as e:
        click.echo(f"Failed to fetch proxies: {e}")
        logger.error(f"Webshare command failed: {e}")

@cli.command()
@click.option("--force", is_flag=True, help="Force re-login even if cookies exist")
@click.option("--max-concurrent", default=2, type=int, help="Maximum concurrent account logins")
def import_accounts(force, max_concurrent):
    """Import and Login accounts from config.json and accountsFile, login via API."""
    logger.debug(f"Running import command with force={force}, max_concurrent={max_concurrent}")
    from src.config import Config
    config = Config()
    if not config.accounts:
        click.echo("No accounts found in config.json or accountsFile")
        logger.warning("No accounts found")
        return

    payload = {
        "accounts": [
            {
                "account_id": acc["account_id"],
                "auth_info_1": acc["auth_info_1"],
                "auth_info_2": acc["auth_info_2"],
                "password": acc["password"],
                "totp_secret": acc["totp_secret"],
                "proxy": acc["proxy"]["proxy_url"] if isinstance(acc.get("proxy"), dict) else acc.get("proxy")
            } for acc in config.accounts if acc.get("proxy")
        ],
        "force": force,
        "max_concurrent": max_concurrent
    }

    try:
        logger.debug(f"Importing accounts: {len(payload['accounts'])}")
        response = requests.post(f"{BASE_URL}/accounts/import", json=payload)
        response.raise_for_status()
        result = response.json()
        click.echo(f"Imported {result['imported']} accounts, {result['failed']} failed")
        if result["failed"]:
            click.echo("Check data/accounts/failed.json for details")
        logger.debug(f"Import completed: {result}")
    except requests.RequestException as e:
        click.echo(f"Failed to import accounts: {e}")
        logger.error(f"Import failed: {e}")

@cli.command()
@click.option("--account-id", required=False, help="Account ID to use for authentication (optional, random if not provided)")
@click.option("--screen-name", required=True, help="Screen name of the user to query")
def get_user(account_id, screen_name):
    """Query user information by screen name."""
    logger.debug(f"Querying user: {screen_name} with account: {account_id or 'random'}")
    from src.config import Config
    config = Config()  # Load config.json and accountsFile
    if not config.accounts:
        click.echo("No accounts found in config.json or accountsFile")
        logger.warning("No accounts found")
        return

    try:
        params = {}
        if account_id:
            params["account_id"] = account_id
        response = requests.get(f"{BASE_URL}/users/{screen_name}", params=params)
        response.raise_for_status()
        user = response.json()
        click.echo(f"User information for '{screen_name}':")
        click.echo(f"- ID: {user['id']}")
        click.echo(f"- Name: {user['name']}")
        click.echo(f"- Screen Name: {user['screen_name']}")
        click.echo(f"- Description: {user.get('description', 'N/A')}")
        click.echo(f"- Followers: {user['followers_count']}")
        click.echo(f"- Following: {user['following_count']}")
        logger.debug(f"User {screen_name} queried successfully")
    except requests.RequestException as e:
        click.echo(f"Failed to query user: {e}")
        logger.error(f"Failed to query user {screen_name}: {e}")

@cli.group()
def proxy():
    """Proxy management related commands"""
    pass

@proxy.command()
@click.option("--concurrent", "-c", default=5, type=int, help="Number of concurrent checks")
@click.option("--dry-run", is_flag=True, help="Only check, do not rotate")
def rotate(concurrent, dry_run):
    """Automatically detect failed proxies and rotate to the most similar IP"""
    click.echo("🔄 Starting proxy rotation...")
    from src.proxy.rotator import proxy_rotator
    
    async def run_rotation():
        if dry_run:
            click.echo("🔍 Performing proxy check (dry run)...")
            failed_proxies = await proxy_rotator.detect_failed_proxies()
            
            if not failed_proxies:
                click.echo("✅ All proxies are working fine")
                return
            
            click.echo(f"❌ Found {len(failed_proxies)} failed proxies:")
            for account_id, proxy_url in failed_proxies:
                click.echo(f"   - Account: {account_id}, Proxy: {proxy_url}")
            
            click.echo("\n🔍 Analyzing available replacement proxies...")
            available_proxies = await proxy_rotator.load_available_proxies()
            click.echo(f"📊 Total available proxies: {len(available_proxies)}")
            
            for acc_id, failed_proxy_url in failed_proxies:
                best_proxy = proxy_rotator.find_best_replacement_proxy(
                    failed_proxy_url, available_proxies
                )
                if best_proxy:
                    click.echo(f"   ✅ Account {acc_id}: {failed_proxy_url} -> {best_proxy.proxy_url}")
                else:
                    click.echo(f"   ❌ Account {acc_id}: No suitable replacement proxy found")
        else:
            result = await proxy_rotator.auto_rotate_failed_proxies()
            
            click.echo(f"📊 Proxy rotation result:")
            click.echo(f"   - Accounts checked: {result['total_checked']}")
            click.echo(f"   - Failed proxies: {result['failed_count']}")
            click.echo(f"   - Successfully rotated: {result['rotated_count']}")
            
            if result['failed_rotations']:
                click.echo(f"   - Failed rotations: {len(result['failed_rotations'])}")
                for failure in result['failed_rotations']:
                    click.echo(f"     ❌ {failure['account_id']}: {failure['reason']}")
            
            if result['rotated_count'] > 0:
                click.echo("✅ Proxy rotation completed!")
            else:
                click.echo("ℹ️  No proxies needed rotation")
    
    try:
        asyncio.run(run_rotation())
    except Exception as e:
        click.echo(f"❌ Proxy rotation failed: {e}")
        logger.error(f"Proxy rotation failed: {e}")

@proxy.command()
@click.option("--account-id", help="Specify account ID to check")
def check(account_id):
    """Check proxy status"""
    click.echo("🔍 Checking proxy status...")
    from src.proxy.rotator import proxy_rotator
    async def run_check():
        if account_id:
            # Check single account
            from src.storage.database import Database
            from src.config import Config
            db = Database(Config())
            account = db.get_account(account_id)
            
            if not account:
                click.echo(f"❌ Account {account_id} does not exist")
                return
            
            proxy_url = account.get("proxy")
            if not proxy_url:
                click.echo(f"ℹ️  Account {account_id} has no proxy configured")
                return
            
            click.echo(f"Checking proxy for account {account_id}: {proxy_url}")
            is_working = await proxy_rotator.proxy_service.test_proxy(proxy_url)
            
            if is_working:
                click.echo("✅ Proxy is working")
            else:
                click.echo("❌ Proxy is not working")
        else:
            # Check all proxies
            failed_proxies = await proxy_rotator.detect_failed_proxies()
            
            from src.storage.database import Database
            from src.config import Config
            db = Database(Config())
            all_accounts = db.get_active_accounts()
            
            total_with_proxy = sum(1 for acc in all_accounts if acc.get("proxy"))
            working_count = total_with_proxy - len(failed_proxies)
            
            click.echo(f"📊 Proxy status summary:")
            click.echo(f"   - Total accounts: {len(all_accounts)}")
            click.echo(f"   - Accounts with proxy: {total_with_proxy}")
            click.echo(f"   - Working: {working_count}")
            click.echo(f"   - Failed: {len(failed_proxies)}")
            
            if failed_proxies:
                click.echo("\n❌ Failed proxy list:")
                for acc_id, proxy_url in failed_proxies:
                    click.echo(f"   - {acc_id}: {proxy_url}")
    
    try:
        asyncio.run(run_check())
    except Exception as e:
        click.echo(f"❌ Proxy check failed: {e}")
        logger.error(f"Proxy check failed: {e}")

@proxy.command()
def list_available():
    """List all available proxies"""
    click.echo("📋 Loading available proxy list...")
    asyncio.run(_list_available())

async def _list_available():
    from src.proxy.rotator import proxy_rotator
    try:
        available_proxies = await proxy_rotator.load_available_proxies()
        
        if not available_proxies:
            click.echo("❌ No available proxies found")
            return
        
        # Group by country
        by_country = {}
        for proxy in available_proxies:
            country = proxy.country_code or "Unknown"
            if country not in by_country:
                by_country[country] = []
            by_country[country].append(proxy)
        
        click.echo(f"📊 Found {len(available_proxies)} available proxies")
        click.echo("\n🌍 Distribution by country:")
        
        for country, proxies in sorted(by_country.items()):
            cities = set(p.city_name for p in proxies if p.city_name)
            city_info = f" ({len(cities)} cities)" if cities else ""
            click.echo(f"   {country}: {len(proxies)} proxies{city_info}")
            
            # Show details for first 3 proxies
            for idx, proxy in enumerate(proxies[:3]):
                city_part = f", {proxy.city_name}" if proxy.city_name else ""
                click.echo(f"     - {proxy.ip}:{proxy.port}{city_part}")
            
            if len(proxies) > 3:
                click.echo(f"     ... {len(proxies) - 3} more proxies")
    
    except Exception as e:
        click.echo(f"❌ Failed to load proxy list: {e}")
        logger.error(f"List proxies failed: {e}")

@cli.command()
def reset_accounts():
    """Reset all proxy settings to null in accounts.json"""
    accounts_file = "accounts.json"
    
    if not os.path.exists(accounts_file):
        click.echo(f"❌ {accounts_file} not found")
        return
    
    try:
        with open(accounts_file, 'r', encoding='utf-8') as f:
            accounts = json.load(f)
        
        if not isinstance(accounts, list):
            click.echo(f"❌ {accounts_file} should contain a list of accounts")
            return
        
        reset_count = 0
        for account in accounts:
            if account.get("proxy"):
                account["proxy"] = None
                reset_count += 1
        
        with open(accounts_file, 'w', encoding='utf-8') as f:
            json.dump(accounts, f, indent=2, ensure_ascii=False)
        
        click.echo(f"✅ Reset proxy settings for {reset_count} accounts in {accounts_file}")
        logger.debug(f"Reset {reset_count} proxy settings in {accounts_file}")
        
    except json.JSONDecodeError as e:
        click.echo(f"❌ Invalid JSON in {accounts_file}: {e}")
        logger.error(f"JSON decode error in {accounts_file}: {e}")
    except Exception as e:
        click.echo(f"❌ Failed to reset accounts: {e}")
        logger.error(f"Reset accounts failed: {e}")

if __name__ == "__main__":
    cli()