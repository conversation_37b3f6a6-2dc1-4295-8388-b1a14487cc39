# Twx API 接口文档

本文档提供了 Twx 系统所有 API 接口的详细说明。

## 基础信息

- **基础URL**: `http://localhost:8000`
- **API前缀**: `/api`
- **文档地址**: `http://localhost:8000/docs`
- **数据格式**: JSON

## 认证说明

目前系统未实现认证机制，所有接口均可直接访问。

## 通用参数

大部分接口支持以下通用参数：

- `priority`: 请求优先级，可选值：`low`、`normal`、`high`、`critical`
- `preferred_account`: 首选账户ID（可选）

## 接口分类

## 1. 系统管理接口

### 1.1 主页

```
GET /
```

返回系统主页 HTML。

### 1.2 健康检查

```
GET /health
```

检查系统健康状态。

**响应示例**:
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "components": {
    "database": "healthy",
    "proxy_pool": {
      "status": "healthy",
      "active_proxies": 10
    },
    "system": {
      "cpu_percent": 5.2,
      "memory_usage": 128.5,
      "memory_percent": 2.1
    }
  },
  "timestamp": "2025-07-25T10:30:00Z"
}
```

## 2. 账户管理接口

### 2.1 批量导入账户

```
POST /api/accounts/import
```

批量导入 Twitter 账户并自动登录。

**请求参数**:
```json
{
  "accounts": [
    {
      "account_id": "account_001",
      "auth_info_1": "username",
      "auth_info_2": "<EMAIL>",
      "password": "password123",
      "totp_secret": "ABCD1234EFGH5678",
      "proxy": "**********************:port"
    }
  ],
  "force": false,
  "max_concurrent": 2
}
```

**响应示例**:
```json
{
  "imported": 1,
  "failed": 0
}
```

### 2.2 导入单个账户

```
POST /api/accounts/import_one?force=false
```

导入单个 Twitter 账户。

**请求参数**:
```json
{
  "account_id": "account_001",
  "auth_info_1": "username",
  "auth_info_2": "<EMAIL>",
  "password": "password123",
  "totp_secret": "ABCD1234EFGH5678",
  "proxy": "**********************:port"
}
```

### 2.3 检查账户状态

```
GET /api/accounts/{account_id}/status
```

检查指定账户的状态。

**响应示例**:
```json
{
  "status": "active",
  "last_check": "2025-07-25T10:30:00Z",
  "is_active": true,
  "details": {
    "screen_name": "username",
    "followers_count": 1000,
    "following_count": 500
  }
}
```

## 3. 用户信息接口

### 3.1 获取用户信息

```
GET /api/users/{username}?priority=normal&preferred_account=account_id
```

获取指定用户的详细信息。

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "12345",
    "name": "User Name",
    "screen_name": "username",
    "description": "User bio",
    "followers_count": 1000,
    "following_count": 500,
    "statuses_count": 2000,
    "profile_image_url": "https://...",
    "profile_banner_url": "https://..."
  },
  "request_info": {
    "priority": "normal",
    "preferred_account": "account_id"
  }
}
```

### 3.2 获取用户关注者

```
GET /api/users/{username}/followers?limit=20
```

获取用户的关注者列表。

### 3.3 获取用户关注列表

```
GET /api/users/{username}/following?limit=20
```

获取用户的关注列表。

## 4. 推文解析接口

### 4.1 解析推文URL

```
POST /api/tweet/parse
```

解析 Twitter URL 并返回推文详细信息。

**请求参数**:
```json
{
  "url": "https://twitter.com/username/status/**********",
  "force_refresh": false,
  "priority": "normal"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "**********",
    "text": "推文内容",
    "created_at": "2025-07-25T10:30:00Z",
    "favorite_count": 100,
    "retweet_count": 50,
    "reply_count": 20,
    "quote_count": 10,
    "language": "zh",
    "user": {
      "id": "12345",
      "name": "用户名",
      "screen_name": "username",
      "description": "用户简介",
      "followers_count": 1000,
      "following_count": 500,
      "statuses_count": 2000,
      "profile_image_url": "https://...",
      "profile_banner_url": "https://..."
    },
    "media": [
      {
        "type": "photo",
        "media_url": "https://...",
        "expanded_url": "https://...",
        "url": "https://...",
        "display_url": "pic.twitter.com/...",
        "height": 1080,
        "width": 1920
      }
    ]
  },
  "cached": false,
  "request_info": {
    "priority": "normal",
    "tweet_id": "**********"
  }
}
```

## 5. 监控管理接口

### 5.1 启动用户监控

```
POST /api/monitor/user/{identifier}
```

启动对指定用户的实时监控。

**请求参数**:
```json
{
  "interval": 300,
  "notify_url": "http://your-callback-url",
  "monitor_profile": true,
  "monitor_tweets": true,
  "monitor_relations": true,
  "priority": "normal"
}
```

### 5.2 停止用户监控

```
DELETE /api/monitor/user/{identifier}
```

停止对指定用户的监控。

### 5.3 批量监控用户

```
POST /api/monitor/batch
```

批量启动多个用户的监控。

**请求参数**:
```json
{
  "users": ["user1", "user2", "user3"],
  "config": {
    "interval": 300,
    "notify_url": "http://your-callback-url",
    "monitor_profile": true,
    "monitor_tweets": true,
    "priority": "normal"
  }
}
```

## 6. 媒体处理接口

### 6.1 获取推文媒体

```
GET /api/media/tweets/{tweet_id}?priority=normal
```

获取推文中的媒体资源信息。

**响应示例**:
```json
{
  "media": [
    {
      "type": "photo",
      "media_url": "https://...",
      "expanded_url": "https://...",
      "height": 1080,
      "width": 1920
    }
  ],
  "count": 1,
  "request_info": {
    "priority": "normal"
  }
}
```

### 6.2 下载媒体文件

```
GET /api/media/download/{media_id}?media_type=image
```

下载指定的媒体文件。

## 7. 数据分析接口

### 7.1 用户数据分析

```
GET /api/analytics/user/{identifier}?priority=normal
```

获取用户的详细数据分析。

**请求参数**:
```json
{
  "time_range": "7d",
  "metrics": ["engagement", "growth", "activity"]
}
```

**响应示例**:
```json
{
  "engagement_rate": 0.05,
  "follower_growth": {
    "daily_change": 10,
    "weekly_change": 70,
    "growth_rate": 0.07
  },
  "posting_times": ["09:00", "14:00", "20:00"],
  "top_hashtags": ["#AI", "#tech", "#innovation"],
  "sentiment": {
    "positive": 0.6,
    "neutral": 0.3,
    "negative": 0.1
  },
  "topics": ["Technology", "AI", "Business"],
  "user_metrics": {
    "avg_likes": 50,
    "avg_retweets": 20,
    "avg_replies": 10
  }
}
```

### 7.2 用户数据导出

```
GET /api/analytics/export/user/{identifier}?priority=low
```

导出用户数据。

**请求参数**:
```json
{
  "data_type": "tweets",
  "format": "json"
}
```

## 8. 代理管理接口

### 8.1 获取Webshare代理

```
POST /api/proxies/webshare
```

从 Webshare API 获取代理列表。

**请求参数**:
```json
{
  "api_key": "your_webshare_api_key"
}
```

### 8.2 检查代理状态

```
GET /api/proxies/status?concurrent_limit=5&include_history=false
```

检查所有代理的状态。

### 8.3 代理轮换

```
POST /api/proxies/rotate
```

自动检测失效代理并轮换。

### 8.4 列出可用代理

```
GET /api/proxies/available
```

列出所有可用的代理。

### 8.5 检查代理分配冲突

```
GET /api/proxies/assignments/conflicts
```

检查代理分配是否有冲突。

### 8.6 修复代理分配冲突

```
POST /api/proxies/assignments/fix-conflicts
```

修复代理分配冲突。

## 9. 智能账户管理接口

### 9.1 获取健康度报告

```
GET /api/smart-accounts/health-report
```

获取所有账号的健康度报告。

### 9.2 获取最佳账号

```
GET /api/smart-accounts/best-account?priority=normal&exclude_accounts=account1,account2&preferred_account=account3
```

根据优先级获取最适合的账号。

### 9.3 测试智能请求

```
POST /api/smart-accounts/test-request
```

测试智能账号管理功能。

**请求参数**:
```json
{
  "username": "twitter",
  "request_config": {
    "priority": "normal",
    "max_retries": 3,
    "timeout": 30.0
  }
}
```

### 9.4 获取账户健康度

```
GET /api/smart-accounts/account/{account_id}/health
```

获取指定账号的健康度信息。

### 9.5 重置账户健康度

```
POST /api/smart-accounts/account/{account_id}/reset-health
```

重置指定账号的健康度数据。

### 9.6 获取统计信息

```
GET /api/smart-accounts/stats
```

获取智能管理器的统计信息。

### 9.7 获取优先级指南

```
GET /api/smart-accounts/priority-guide
```

获取请求优先级使用指南。

### 9.8 获取优化建议

```
GET /api/smart-accounts/optimization-suggestions
```

获取系统优化建议。

### 9.9 获取风险报告

```
GET /api/smart-accounts/risk-report
```

获取风险控制报告。

### 9.10 获取账户风险画像

```
GET /api/smart-accounts/account/{account_id}/risk-profile
```

获取指定账号的风险画像。

## 10. 健康检查接口

### 10.1 获取账户列表

```
GET /api/accounts
```

获取所有账户列表。

### 10.2 获取代理列表

```
GET /api/proxies
```

获取所有代理列表。

### 10.3 启动健康检测

```
POST /api/health-check/start
```

启动健康检测任务。

### 10.4 检测单个账户

```
POST /api/accounts/{account_id}/check
```

检测单个账户的健康状态。

### 10.5 检测单个代理

```
POST /api/proxies/{account_id}/check
```

检测单个代理的健康状态。

### 10.6 批量检测账户

```
POST /api/accounts/check-all
```

批量检测所有账户。

### 10.7 批量检测代理

```
POST /api/proxies/check-all
```

批量检测所有代理。

## 错误处理

所有接口均遵循统一的错误响应格式：

```json
{
  "detail": "错误描述",
  "status_code": 400
}
```

常见HTTP状态码：
- `200`: 成功
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

## 优先级说明

请求优先级影响账户选择和请求处理顺序：

- `critical`: 关键请求，使用最佳账户
- `high`: 高优先级请求，优先处理
- `normal`: 普通请求，默认优先级
- `low`: 低优先级请求，可能排队等待

## 使用建议

1. **账户管理**: 建议使用智能账户管理功能，自动选择最佳账户
2. **代理配置**: 定期检查代理状态，及时轮换失效代理
3. **监控设置**: 根据需求合理设置监控间隔，避免过度请求
4. **错误处理**: 实现适当的错误处理和重试机制
5. **性能优化**: 使用缓存机制，避免重复请求相同数据

## 限制说明

- 单次批量操作建议不超过100个项目
- 监控间隔建议不少于60秒
- 并发请求数量受账户数量限制
- 部分接口可能有频率限制，请合理使用

## 更新日志

- v1.0.0: 初版API文档发布