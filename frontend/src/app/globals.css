@import "tailwindcss";

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 250, 250, 250;
  --background-end-rgb: 240, 240, 240;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 20, 20, 20;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
    to bottom,
    rgb(var(--background-start-rgb)),
    rgb(var(--background-end-rgb))
  );
  min-height: 100vh;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #666;
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 文本选择样式 */
::selection {
  background: rgba(0, 122, 255, 0.2);
  color: rgb(var(--foreground-rgb));
}

/* 动画类 */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  .animate-scale {
    animation: scale 0.3s ease-in-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scale {
  from {
    transform: scale(0.95);
  }
  to {
    transform: scale(1);
  }
}

/* 毛玻璃效果 */
.glass {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.7);
}

/* 渐变文本 */
.gradient-text {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-purple-500;
}

/* 卡片悬停效果 */
.hover-card {
  @apply transition-all duration-300 hover:shadow-lg hover:scale-[1.01];
}

/* 按钮悬停效果 */
.hover-button {
  @apply transition-all duration-200 hover:scale-105;
}

/* 图片悬停效果 */
.hover-image {
  @apply transition-transform duration-300 hover:scale-105;
}
