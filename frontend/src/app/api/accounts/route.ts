import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // TODO: 从后端服务获取账户列表
    const response = await fetch('http://localhost:8000/api/accounts');
    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('获取账户列表失败:', error);
    return NextResponse.json(
      { error: '获取账户列表失败' },
      { status: 500 }
    );
  }
} 