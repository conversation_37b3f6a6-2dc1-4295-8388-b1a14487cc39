import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // TODO: 从后端服务获取代理列表
    const response = await fetch('http://localhost:8000/api/proxies');
    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('获取代理列表失败:', error);
    return NextResponse.json(
      { error: '获取代理列表失败' },
      { status: 500 }
    );
  }
} 