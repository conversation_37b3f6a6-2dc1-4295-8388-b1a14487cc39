import { NextResponse } from "next/server";

// 配置常量
const CONFIG = {
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1秒
  TIMEOUT: 10000, // 10秒
  AUTH_TIMEOUT: 5000, // 5秒
};

// 延迟函数
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 带超时的fetch
async function fetchWithTimeout(url: string, options: RequestInit, timeout: number) {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

// 获取认证token
async function getAuthToken(retries = CONFIG.MAX_RETRIES): Promise<string> {
  try {
    const response = await fetchWithTimeout(
      "https://edge.microsoft.com/translate/auth",
      { method: "GET" },
      CONFIG.AUTH_TIMEOUT
    );

    if (!response.ok) {
      throw new Error(`Auth failed with status: ${response.status}`);
    }

    return await response.text();
  } catch (error) {
    if (retries > 0) {
      console.log(`Auth retry remaining: ${retries - 1}`);
      await delay(CONFIG.RETRY_DELAY);
      return getAuthToken(retries - 1);
    }
    throw new Error("Failed to get auth token after retries");
  }
}

// 执行翻译
async function performTranslation(
  text: string,
  from: string,
  to: string,
  token: string,
  retries = CONFIG.MAX_RETRIES
): Promise<string> {
  const url = `https://api.cognitive.microsofttranslator.com/translate?api-version=3.0&from=${from}&to=${to}`;

  try {
    const response = await fetchWithTimeout(
      url,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json; charset=utf-8",
        },
        body: JSON.stringify([{ Text: text }]),
      },
      CONFIG.TIMEOUT
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Translation failed: ${response.status} ${response.statusText}`, errorText);
      throw new Error(`Translation request failed: ${response.status}`);
    }

    const translationData = await response.json();
    const translatedText = translationData[0]?.translations?.[0]?.text;

    if (!translatedText) {
      throw new Error("No translation result");
    }

    return translatedText;
  } catch (error) {
    if (retries > 0) {
      console.log(`Translation retry remaining: ${retries - 1}`);
      await delay(CONFIG.RETRY_DELAY);
      return performTranslation(text, from, to, token, retries - 1);
    }
    throw error;
  }
}

export async function POST(request: Request) {
  try {
    const { text, from, to } = await request.json();

    if (!text || !to || !from) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    // 获取认证token
    const token = await getAuthToken();

    // 执行翻译
    const translatedText = await performTranslation(text, from, to, token);

    return NextResponse.json({ translatedText });
  } catch (error) {
    console.error("Translation error:", error);
    
    // 根据错误类型返回不同的错误信息
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return NextResponse.json(
          { error: "Translation request timed out" },
          { status: 504 }
        );
      }
      if (error.message.includes('auth')) {
        return NextResponse.json(
          { error: "Authentication failed" },
          { status: 401 }
        );
      }
    }
    
    return NextResponse.json(
      { error: "Translation failed" },
      { status: 500 }
    );
  }
}
