


async function main() {
  const langList = [
    { "code": "ja", "value": "日本語" },
    { "code": "en", "value": "English" },
    { "code": "fr", "value": "Français" },
    { "code": "ru", "value": "Русский язык" },
    { "code": "th", "value": "คนไทย" },
    { "code": "zh-CHS", "value": "简体中文" },
    { "code": "zh-CHT", "value": "繁體中文" },
    { "code": "de", "value": "Deutsch" },
    { "code": "pt", "value": "português" },
    { "code": "es", "value": "Español" },
    { "code": "it", "value": "italiano" },
    { "code": "ko", "value": "한국어" }
  ];
  const response = await fetch("https://edge.microsoft.com/translate/auth", {
    "method": "GET"
  });
  const token = await response.text();
  console.log("got token: ", token);
  const transRes = await fetch("https://api.cognitive.microsofttranslator.com/translate?to=ja&api-version=3.0&includeSentenceLength=true", {
    headers: {
      authorization: `Bearer ${ token }`,
      "Content-Type": "application/json; charset=utf-8"
    },
    "body": "[{\"Text\":\"该把这场谈话翻译成汉语，广为传播！\"}]",
    "method": "POST",
    "mode": "cors",
    "credentials": "include"
  });
  const res = await transRes.json();
  console.log(res?.[0]?.translations?.[0]);
}

main().catch(console.error);