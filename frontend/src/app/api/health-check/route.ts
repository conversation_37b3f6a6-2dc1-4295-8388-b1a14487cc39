import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { concurrency } = body;

    // TODO: 调用后端服务开始健康检测
    const response = await fetch('http://localhost:8000/api/health-check/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ concurrency }),
    });

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('启动健康检测失败:', error);
    return NextResponse.json(
      { error: '启动健康检测失败' },
      { status: 500 }
    );
  }
} 