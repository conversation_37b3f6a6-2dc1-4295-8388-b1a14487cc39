import { NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import { Tweet } from '@/types/tweet';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const period = searchParams.get('period') || 'day';

  try {
    // 读取最新的推文文件
    const dataDir = path.join(process.cwd(), '..', 'data', 'hot_tweets');
    const files = await fs.readdir(dataDir);
    
    // 过滤出指定周期的文件
    const periodFiles = files.filter(file => file.startsWith(`hot_tweets_${period}`));
    if (periodFiles.length === 0) {
      return NextResponse.json({ tweets: [] });
    }
    
    // 按时间戳排序，获取最新的文件
    periodFiles.sort().reverse();
    const latestFile = periodFiles[0];
    
    // 读取文件内容
    const filePath = path.join(dataDir, latestFile);
    const fileContent = await fs.readFile(filePath, 'utf-8');
    const data = JSON.parse(fileContent);
    
    return NextResponse.json({ tweets: data });
  } catch (error) {
    console.error('Error reading tweets:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tweets', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
} 