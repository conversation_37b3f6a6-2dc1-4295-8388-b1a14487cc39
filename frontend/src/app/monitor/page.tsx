'use client';

import { useState, useEffect } from 'react';
import AccountList from '@/components/AccountList';
import ProxyList from '@/components/ProxyList';
import PageLayout from '@/components/shared/PageLayout';

interface Account {
  account_id: string;
  auth_info_1: string;
  auth_info_2: string;
  status: string;
  lastCheck: string;
  proxyStatus?: string;
  error?: string;
  proxy?: {
    id: string;
    proxy_url: string;
    country_code: string;
    city_name: string;
  };
}

interface Proxy {
  id: string;
  username: string;
  proxy_address: string;
  port: number;
  status: string;
  lastCheck: string;
  responseTime?: number;
  country_code: string;
  city_name: string;
}

export default function MonitorPage() {
  const [checkingAllAccounts, setCheckingAllAccounts] = useState<boolean>(false);
  const [checkingAllProxies, setCheckingAllProxies] = useState<boolean>(false);
  const [, setAccounts] = useState<Account[]>([]);
  const [, setProxies] = useState<Proxy[]>([]);

  const fetchAccounts = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/accounts');
      if (!response.ok) {
        throw new Error('获取账户列表失败');
      }
      const data = await response.json();
      setAccounts(data);
    } catch (err) {
      console.error('获取账户列表失败:', err);
    }
  };

  const fetchProxies = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/proxies');
      if (!response.ok) {
        throw new Error('获取代理列表失败');
      }
      const data = await response.json();
      setProxies(data);
    } catch (err) {
      console.error('获取代理列表失败:', err);
    }
  };

  // 初始加载数据
  useEffect(() => {
    fetchAccounts();
    fetchProxies();
  }, []);

  const handleAccountCheck = async (accountId: string) => {
    try {
      const response = await fetch(`http://localhost:8000/api/accounts/${accountId}/check`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '检测失败');
      }
    } catch (error) {
      console.error('检测失败:', error);
      alert(error instanceof Error ? error.message : '检测失败，请稍后重试');
    }
  };

  const handleProxyCheck = async (proxyId: string) => {
    try {
      const response = await fetch(`http://localhost:8000/api/proxies/${proxyId}/check`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '检测失败');
      }
    } catch (error) {
      console.error('检测失败:', error);
      alert(error instanceof Error ? error.message : '检测失败，请稍后重试');
    }
  };

  const handleCheckAllAccounts = async () => {
    setCheckingAllAccounts(true);
    try {
      const response = await fetch('http://localhost:8000/api/accounts/check-all', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          concurrency: 5
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '检测失败');
      }
      
      // 等待一段时间后刷新账户列表
      setTimeout(() => {
        fetchAccounts();
      }, 2000);
      
    } catch (error) {
      console.error('检测失败:', error);
      alert(error instanceof Error ? error.message : '检测失败，请稍后重试');
    } finally {
      setCheckingAllAccounts(false);
    }
  };

  const handleCheckAllProxies = async () => {
    setCheckingAllProxies(true);
    try {
      const response = await fetch('http://localhost:8000/api/proxies/check-all', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          concurrency: 5
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '检测失败');
      }
      
      // 等待一段时间后刷新代理列表
      setTimeout(() => {
        fetchProxies();
      }, 2000);
      
    } catch (error) {
      console.error('检测失败:', error);
      alert(error instanceof Error ? error.message : '检测失败，请稍后重试');
    } finally {
      setCheckingAllProxies(false);
    }
  };

  return (
    <PageLayout className="min-h-screen bg-gray-50 dark:bg-gray-900" showFooter={false}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">监控面板</h1>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            实时监控账户和代理的健康状态
          </p>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
            <AccountList
              onCheckHealth={handleAccountCheck}
              onCheckAll={handleCheckAllAccounts}
              checkingAll={checkingAllAccounts}
            />
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
            <ProxyList
              onCheckHealth={handleProxyCheck}
              onCheckAll={handleCheckAllProxies}
              checkingAll={checkingAllProxies}
            />
          </div>
        </div>
      </div>
    </PageLayout>
  );
} 