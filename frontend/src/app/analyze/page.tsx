'use client';

import { useState } from 'react';
import TweetInput from '@/components/TweetInput';
import TweetDisplay from '@/components/TweetDisplay';
import PageLayout from '@/components/shared/PageLayout';
import { TweetData } from '@/types/tweet';

export default function AnalyzePage() {
  const [tweetData, setTweetData] = useState<TweetData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (url: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('http://localhost:8000/api/tweet/parse', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url }),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch tweet data');
      }

      const data = await response.json();
      setTweetData(data.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <PageLayout className="min-h-screen bg-gray-50 dark:bg-gray-900" showFooter={false}>
      <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8 mt-12">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              推文分析工具
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              输入 Twitter 推文链接来分析其内容和元数据
            </p>
          </div>

          <TweetInput onSubmit={handleSubmit} loading={loading} />

          {error && (
            <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-red-600 dark:text-red-400">{error}</p>
            </div>
          )}

          {loading && (
            <div className="mt-8 flex justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            </div>
          )}

          {tweetData && !loading && (
            <div className="mt-8">
              <TweetDisplay tweet={tweetData} />
            </div>
          )}
        </div>
      </div>
    </PageLayout>
  );
} 