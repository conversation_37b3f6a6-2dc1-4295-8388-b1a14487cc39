'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import PageLayout from '@/components/shared/PageLayout';
import { motion } from 'framer-motion';
import {
  ArrowPathIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  ComputerDesktopIcon,
  EyeIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  FireIcon,
  UserGroupIcon,
  ServerIcon
} from '@heroicons/react/24/outline';

interface SystemStats {
  totalAccounts: number;
  activeAccounts: number;
  totalProxies: number;
  activeProxies: number;
  todayTweets: number;
  systemHealth: 'healthy' | 'warning' | 'error';
}

export default function Home() {
  const [stats, setStats] = useState<SystemStats>({
    totalAccounts: 0,
    activeAccounts: 0,
    totalProxies: 0,
    activeProxies: 0,
    todayTweets: 0,
    systemHealth: 'healthy'
  });
  const [loading, setLoading] = useState(true);

  const fetchSystemStats = async () => {
    setLoading(true);
    try {
      // 并行获取系统状态数据
      const [accountsRes, proxiesRes, tweetsRes] = await Promise.all([
        fetch('/api/accounts'),
        fetch('/api/proxies'),
        fetch('/api/tweets?period=day')
      ]);

      const accountsData = accountsRes.ok ? await accountsRes.json() : [];
      const proxiesData = proxiesRes.ok ? await proxiesRes.json() : [];
      const tweetsData = tweetsRes.ok ? await tweetsRes.json() : { tweets: [] };

      // 计算系统健康状态
      const activeAccounts = accountsData.filter((acc: any) => acc.proxyStatus === 'healthy').length;
      const activeProxies = proxiesData.filter((proxy: any) => proxy.status === 'healthy').length;
      const accountHealthRatio = accountsData.length > 0 ? activeAccounts / accountsData.length : 0;
      const proxyHealthRatio = proxiesData.length > 0 ? activeProxies / proxiesData.length : 0;

      let systemHealth: 'healthy' | 'warning' | 'error' = 'healthy';
      if (accountHealthRatio < 0.5 || proxyHealthRatio < 0.5) {
        systemHealth = 'error';
      } else if (accountHealthRatio < 0.8 || proxyHealthRatio < 0.8) {
        systemHealth = 'warning';
      }

      setStats({
        totalAccounts: accountsData.length,
        activeAccounts,
        totalProxies: proxiesData.length,
        activeProxies,
        todayTweets: tweetsData.tweets?.length || 0,
        systemHealth
      });
    } catch (error) {
      console.error('Error fetching system stats:', error);
      setStats(prev => ({ ...prev, systemHealth: 'error' }));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSystemStats();
    // 设置定时刷新
    const interval = setInterval(fetchSystemStats, 30000); // 30秒刷新一次
    return () => clearInterval(interval);
  }, []);

  const getHealthIcon = (health: 'healthy' | 'warning' | 'error') => {
    switch (health) {
      case 'healthy':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
      case 'warning':
        return <ExclamationTriangleIcon className="w-5 h-5 text-yellow-500" />;
      case 'error':
        return <XCircleIcon className="w-5 h-5 text-red-500" />;
    }
  };

  const getHealthText = (health: 'healthy' | 'warning' | 'error') => {
    switch (health) {
      case 'healthy':
        return '系统运行正常';
      case 'warning':
        return '系统存在警告';
      case 'error':
        return '系统存在错误';
    }
  };

  return (
    <PageLayout className="min-h-screen bg-gray-50 dark:bg-gray-900" showFooter={false}>
      {/* Dashboard 头部 */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                系统控制台
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Twitter 监控系统总览和快速操作中心
              </p>
            </div>
            <div className="flex items-center space-x-3">
              {getHealthIcon(stats.systemHealth)}
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {getHealthText(stats.systemHealth)}
              </span>
              <Button
                onClick={fetchSystemStats}
                variant="ghost"
                size="sm"
                className="flex items-center space-x-2"
              >
                <ArrowPathIcon className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                <span>刷新</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 系统状态卡片 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* 账户状态卡片 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <UserGroupIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <span className="text-xs text-gray-500 dark:text-gray-400">账户</span>
            </div>
            <div className="space-y-1">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {stats.activeAccounts}/{stats.totalAccounts}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                活跃账户数量
              </div>
            </div>
          </motion.div>

          {/* 代理状态卡片 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <ServerIcon className="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
              <span className="text-xs text-gray-500 dark:text-gray-400">代理</span>
            </div>
            <div className="space-y-1">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {stats.activeProxies}/{stats.totalProxies}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                活跃代理数量
              </div>
            </div>
          </motion.div>

          {/* 今日推文卡片 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                <FireIcon className="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
              <span className="text-xs text-gray-500 dark:text-gray-400">今日</span>
            </div>
            <div className="space-y-1">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {stats.todayTweets}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                热门推文数量
              </div>
            </div>
          </motion.div>

          {/* 系统健康卡片 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                {getHealthIcon(stats.systemHealth)}
              </div>
              <span className="text-xs text-gray-500 dark:text-gray-400">状态</span>
            </div>
            <div className="space-y-1">
              <div className="text-lg font-semibold text-gray-900 dark:text-white">
                {getHealthText(stats.systemHealth)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                系统运行状态
              </div>
            </div>
          </motion.div>
        </div>

        {/* 快速操作区域 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* 热门趋势卡片 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            whileHover={{ scale: 1.02, y: -2 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 p-6 border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-orange-400 to-red-500 rounded-xl flex items-center justify-center">
                <FireIcon className="w-6 h-6 text-white" />
              </div>
              <span className="text-sm text-gray-500 dark:text-gray-400">实时更新</span>
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">热门趋势</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              查看最新的热门推文，发现趋势内容
            </p>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <ChartBarIcon className="w-4 h-4" />
                <span>实时数据</span>
              </div>
              <Link href="/trends">
                <Button
                  size="sm"
                  className="bg-gradient-to-r from-orange-400 to-red-500 hover:from-orange-500 hover:to-red-600"
                >
                  查看趋势
                </Button>
              </Link>
            </div>
          </motion.div>

          {/* 监控面板卡片 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            whileHover={{ scale: 1.02, y: -2 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 p-6 border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
                <ComputerDesktopIcon className="w-6 h-6 text-white" />
              </div>
              <span className="text-sm text-gray-500 dark:text-gray-400">系统监控</span>
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">监控面板</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              实时监控账户和代理状态，确保系统稳定运行
            </p>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <EyeIcon className="w-4 h-4" />
                <span>健康检查</span>
              </div>
              <Link href="/monitor">
                <Button
                  size="sm"
                  className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600"
                >
                  进入监控
                </Button>
              </Link>
            </div>
          </motion.div>

          {/* 推文分析卡片 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            whileHover={{ scale: 1.02, y: -2 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 p-6 border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                <MagnifyingGlassIcon className="w-6 h-6 text-white" />
              </div>
              <span className="text-sm text-gray-500 dark:text-gray-400">内容分析</span>
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">推文分析</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              深度分析推文内容，获取详细的媒体内容
            </p>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <ChartBarIcon className="w-4 h-4" />
                <span>智能解析</span>
              </div>
              <Link href="/analyze">
                <Button
                  size="sm"
                  className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                >
                  开始分析
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </div>

    </PageLayout>
  );
}
