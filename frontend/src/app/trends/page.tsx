"use client";

import { useState, useEffect, useRef } from "react";
import TweetCard from "@/components/TweetCard";
import { Button } from "@/components/ui/Button";
import PageLayout from "@/components/shared/PageLayout";
import { motion, AnimatePresence } from "framer-motion";
import { Tweet } from "@/types/tweet";
import {
  ArrowPathIcon,
  ClockIcon,
  FireIcon,
  ChartBarIcon,
} from "@heroicons/react/24/outline";
import { asyncSleep } from "@/utils/utils";
import Loading from "@/components/Loading";

export default function TrendsPage() {
  const [period, setPeriod] = useState<"day" | "week" | "month">("day");
  const [allTweets, setAllTweets] = useState<Tweet[]>([]);
  const [displayedTweets, setDisplayedTweets] = useState<Tweet[]>([]);
  const [loading, setLoading] = useState(true);
  const [hasMore, setHasMore] = useState(true);
  const observerTarget = useRef<HTMLDivElement>(null);
  const PAGE_SIZE = 20;

  const fetchTweets = async (selectedPeriod: "day" | "week" | "month") => {
    setLoading(true);
    try {
      const response = await fetch(`/api/tweets?period=${selectedPeriod}`);
      if (!response.ok) {
        throw new Error("Failed to fetch tweets");
      }
      const data = await response.json();
      // await asyncSleep(500);
      const sortedTweets = data.tweets.sort(() =>
        Math.random() > 0.5 ? -1 : 1
      ); //.sort((a: Tweet, b: Tweet) => b.favorite_count - a.favorite_count);
      const limitedTweets = sortedTweets.slice(0, PAGE_SIZE);
      setAllTweets(limitedTweets);
      setDisplayedTweets(data.tweets.slice(0, PAGE_SIZE));
      setHasMore(data.tweets.length > PAGE_SIZE);
    } catch (error) {
      console.error("Error fetching tweets:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTweets(period);
  }, [period]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loading) {
          const currentLength = displayedTweets.length;
          const nextTweets = allTweets.slice(
            currentLength,
            currentLength + PAGE_SIZE
          );
          setDisplayedTweets((prev) => [...prev, ...nextTweets]);
          setHasMore(currentLength + PAGE_SIZE < allTweets.length);
        }
      },
      { threshold: 0.1 }
    );

    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }

    return () => observer.disconnect();
  }, [hasMore, loading, allTweets, displayedTweets]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <PageLayout
      className="min-h-screen bg-gray-50 dark:bg-gray-900"
      showFooter={false}
    >
      {/* 页面头部 */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-orange-400 to-red-500 rounded-xl flex items-center justify-center">
                <FireIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  热门趋势
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  发现最新的热门内容，掌握社交媒体趋势
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
              <ChartBarIcon className="w-5 h-5" />
              <span>实时数据更新</span>
            </div>
          </div>
        </div>
      </div>

      {/* 筛选控制栏 */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                时间范围:
              </span>
              <div className="flex space-x-2">
                <Button
                  variant={period === "day" ? "primary" : "ghost"}
                  size="sm"
                  onClick={() => setPeriod("day")}
                  className="flex items-center space-x-2"
                >
                  <ClockIcon className="w-4 h-4" />
                  <span>今日</span>
                </Button>
                <Button
                  variant={period === "week" ? "primary" : "ghost"}
                  size="sm"
                  onClick={() => setPeriod("week")}
                  className="flex items-center space-x-2"
                >
                  <ClockIcon className="w-4 h-4" />
                  <span>本周</span>
                </Button>
                <Button
                  variant={period === "month" ? "primary" : "ghost"}
                  size="sm"
                  onClick={() => setPeriod("month")}
                  className="flex items-center space-x-2"
                >
                  <ClockIcon className="w-4 h-4" />
                  <span>本月</span>
                </Button>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => fetchTweets(period)}
              className="flex items-center space-x-2"
            >
              <ArrowPathIcon
                className={`w-4 h-4 ${loading ? "animate-spin" : ""}`}
              />
              <span>刷新数据</span>
            </Button>
          </div>
        </div>
      </div>

      {/* 推文内容区域 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-4xl mx-auto">
          <AnimatePresence mode="wait">
            {loading && displayedTweets.length === 0 ? (
              <motion.div
                key="loading"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="flex flex-col items-center justify-center py-20"
              >
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mb-4"></div>
                <p className="text-gray-600 dark:text-gray-400">
                  正在加载热门推文...
                </p>
              </motion.div>
            ) : displayedTweets.length > 0 ? (
              <motion.div
                key="tweets"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                className="space-y-6"
              >
                {displayedTweets.map((tweet, index) => (
                  <TweetCard key={tweet.id} tweet={tweet} index={index} />
                ))}

                {/* 加载更多触发器 */}
                <div
                  ref={observerTarget}
                  className="h-10 flex items-center justify-center"
                >
                  {loading && (
                    <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
                      <span>加载更多...</span>
                    </div>
                  )}
                </div>
              </motion.div>
            ) : (
              <motion.div
                key="empty"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="text-center py-20"
              >
                <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FireIcon className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  暂无热门推文
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mb-6">
                  当前时间段内没有找到热门推文数据
                </p>
                <Button
                  onClick={() => fetchTweets(period)}
                  className="flex items-center space-x-2 bg-gradient-to-r from-orange-400 to-red-500 hover:from-orange-500 hover:to-red-600"
                >
                  <ArrowPathIcon className="w-4 h-4" />
                  <span>重新加载</span>
                </Button>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </PageLayout>
  );
}
