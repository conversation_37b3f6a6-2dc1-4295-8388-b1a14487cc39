import React, { useState } from 'react';

interface CopyableCellProps {
  content: string;
  maxWidth?: string;
  className?: string;
}

export default function CopyableCell({ content, maxWidth = 'max-w-[200px]', className = '' }: CopyableCellProps) {
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipText, setTooltipText] = useState('');
  const [showContentTooltip, setShowContentTooltip] = useState(false);

  const handleDoubleClick = async () => {
    try {
      await navigator.clipboard.writeText(content);
      setTooltipText('已复制！');
      setShowTooltip(true);
      setTimeout(() => {
        setShowTooltip(false);
      }, 2000);
    } catch (err) {
      setTooltipText('复制失败');
      setShowTooltip(true);
      setTimeout(() => {
        setShowTooltip(false);
      }, 2000);
    }
  };

  return (
    <div 
      className={`group relative cursor-pointer ${className}`}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={() => {
        setShowContentTooltip(true);
      }}
      onMouseLeave={() => {
        setShowContentTooltip(false);
      }}
    >
      <div className={`${maxWidth} truncate`}>
        {content}
      </div>
      {/* 复制提示 */}
      <div className={`absolute z-20 ${showTooltip ? 'block' : 'hidden'} bg-gray-900 text-white text-xs rounded py-1 px-2 -top-8 left-1/2 transform -translate-x-1/2 whitespace-nowrap`}>
        {tooltipText}
      </div>
      {/* 内容提示 */}
      <div className={`absolute z-10 ${showContentTooltip ? 'block' : 'hidden'} bg-gray-900 text-white text-xs rounded py-1 px-2 -top-8 left-1/2 transform -translate-x-1/2 whitespace-nowrap`}>
        {content}
      </div>
    </div>
  );
} 