'use client';

import { TweetData } from '@/types/tweet';
import { formatDistanceToNow } from 'date-fns';
import Image from "next/image";
import MediaDisplay from './shared/MediaDisplay';
import TweetInteractions from './shared/TweetInteractions';

interface TweetDisplayProps {
  tweet: TweetData;
}

export default function TweetDisplay({ tweet }: TweetDisplayProps) {
  const tweetUrl = `https://twitter.com/${tweet.user.screen_name}/status/${tweet.id}`;

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
      {/* 用户信息 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="relative w-12 h-12">
            <Image
              src={tweet.user.profile_image_url}
              alt={tweet.user.name}
              fill
              className="rounded-full object-cover"
            />
          </div>
          <div>
            <h2 className="font-bold text-gray-900 hover:text-blue-600 transition-colors">
              {tweet.user.name}
            </h2>
            <p className="text-gray-500 hover:text-blue-500 transition-colors">
              @{tweet.user.screen_name}
            </p>
          </div>
        </div>
      </div>

      {/* 推文内容 */}
      <div className="p-4">
        <p className="text-gray-900 whitespace-pre-wrap">{tweet.text}</p>
        <p className="text-gray-500 text-sm mt-2">
          {formatDistanceToNow(new Date(tweet.created_at), { addSuffix: true })}
        </p>
      </div>

      {/* 媒体内容 */}
      {tweet.media && tweet.media.length > 0 && (
        <div className="border-t border-gray-200 flex justify-center align-center ">
          <MediaDisplay
            media={tweet.media}
            className="w-full"
            maxHeight={600}
            maxWidth="100%"
            features={{
              enableStreamSelection: true,
              enableDownload: true
            }}
          />
        </div>
      )}

      {/* 互动数据 */}
      <div className="p-4 border-t border-gray-200">
        <TweetInteractions
          replyCount={tweet.reply_count}
          retweetCount={tweet.retweet_count}
          favoriteCount={tweet.favorite_count}
          onReply={() => window.open(tweetUrl, '_blank')}
          onRetweet={() => window.open(`https://twitter.com/intent/retweet?tweet_id=${tweet.id}`, '_blank')}
          onFavorite={() => window.open(`https://twitter.com/intent/like?tweet_id=${tweet.id}`, '_blank')}
          tweetUrl={tweetUrl}
        />
      </div>
    </div>
  );
} 