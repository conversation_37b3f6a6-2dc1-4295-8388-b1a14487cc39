import React from 'react';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  text?: string;
  className?: string;
  style?: React.CSSProperties
}

const sizeMap = {
  sm: 24,
  md: 40,
  lg: 64,
};

export default function Loading({
  size = 'md',
  // color = 'linear-gradient(90deg, #6366f1 0%, #a21caf 100%)',
  style = undefined,
  text = '加载中...',
  className = '',
}: LoadingProps) {
  const px = sizeMap[size] || sizeMap.md;
  return (
    <div className={`flex flex-col items-center justify-center ${className}`} style={style}>
      <span
        className="inline-block animate-spin"
        style={{
          width: px,
          height: px,
        }}
      >
        <svg
          width={px}
          height={px}
          viewBox={`0 0 ${px} ${px}`}
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle
            cx={px / 2}
            cy={px / 2}
            r={(px / 2) - 4}
            stroke="#e5e7eb"
            strokeWidth="4"
            className="opacity-30"
          />
          <path
            d={`M${px / 2},${px / 2} m0,-${(px / 2) - 4} a${(px / 2) - 4},${(px / 2) - 4} 0 1,1 0,${px - 8} a${(px / 2) - 4},${(px / 2) - 4} 0 1,1 0,-${px - 8}`}
            stroke="url(#loading-gradient)"
            strokeWidth="4"
            strokeLinecap="round"
            fill="none"
            strokeDasharray={Math.PI * (px - 8)}
            strokeDashoffset={Math.PI * (px - 8) * 0.25}
          />
          <defs>
            <linearGradient id="loading-gradient" x1="0" y1="0" x2={px} y2={px} gradientUnits="userSpaceOnUse">
              <stop stopColor="#6366f1" />
              <stop offset="1" stopColor="#a21caf" />
            </linearGradient>
          </defs>
        </svg>
      </span>
      {text && <span className="mt-2 text-gray-500 text-sm select-none">{text}</span>}
    </div>
  );
} 