'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import CopyableCell from './CopyableCell';
import CompactCell from './ui/CompactCell';
import Loading from './Loading';
import StatusBadge, { StatusType } from './ui/StatusBadge';
import ActionButton from './ui/ActionButton';
import DataCard, { DataCardHeader, DataCardContent } from './ui/DataCard';
import EmptyState from './ui/EmptyState';
import {
  MagnifyingGlassIcon,
  ArrowPathIcon,
  ServerIcon,
  EyeIcon,
  XMarkIcon,
  ClockIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline';

interface Proxy {
  id: string;
  username: string;
  proxy_address: string;
  port: number;
  status: string;
  lastCheck: string;
  responseTime?: number;
  country_code: string;
  city_name: string;
}

interface ProxyListProps {
  onCheckHealth?: (proxyId: string) => void;
  onCheckAll?: () => void;
  checkingAll?: boolean;
}

// 状态转换辅助函数
const getStatusType = (status: string): StatusType => {
  switch (status) {
    case 'healthy': return 'healthy';
    case 'unhealthy': return 'unhealthy';
    default: return 'unknown';
  }
};

// 格式化响应时间
const formatResponseTime = (responseTime?: number): string => {
  if (!responseTime) return '-';
  if (responseTime < 100) return `${responseTime}ms (极快)`;
  if (responseTime < 300) return `${responseTime}ms (快)`;
  if (responseTime < 1000) return `${responseTime}ms (正常)`;
  return `${responseTime}ms (慢)`;
};

// 获取响应时间颜色
const getResponseTimeColor = (responseTime?: number): string => {
  if (!responseTime) return 'bg-gray-200';
  if (responseTime < 100) return 'bg-green-500';
  if (responseTime < 300) return 'bg-blue-500';
  if (responseTime < 1000) return 'bg-yellow-500';
  return 'bg-red-500';
};

// 格式化时间
const formatTime = (timeString: string): string => {
  try {
    const date = new Date(timeString);
    return date.toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return '未知';
  }
};

export default function ProxyList({ onCheckHealth, onCheckAll, checkingAll }: ProxyListProps) {
  const [proxies, setProxies] = useState<Proxy[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [checkingProxies, setCheckingProxies] = useState<Set<string>>(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const itemsPerPage = 10;

  const fetchProxies = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/proxies');
      if (!response.ok) {
        throw new Error('获取代理列表失败');
      }
      const data = await response.json();
      setProxies(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取代理列表失败');
    } finally {
      setLoading(false);
    }
  };

  const updateProxyStatus = async (proxyId: string) => {
    try {
      const response = await fetch(`http://localhost:8000/api/proxies/${proxyId}`);
      if (!response.ok) {
        throw new Error('获取代理状态失败');
      }
      const updatedProxy = await response.json();
      setProxies(prevProxies => 
        prevProxies.map(proxy => 
          proxy.id === proxyId ? updatedProxy : proxy
        )
      );
    } catch (err) {
      console.error('更新代理状态失败:', err);
    }
  };

  useEffect(() => {
    fetchProxies();
    const interval = setInterval(fetchProxies, 5000); // 每5秒刷新一次
    return () => clearInterval(interval);
  }, []);

  const handleCheckHealth = async (e: React.MouseEvent, proxyId: string) => {
    e.stopPropagation(); // 防止触发行点击事件
    setCheckingProxies(prev => new Set(prev).add(proxyId));
    try {
      const response = await fetch(`http://localhost:8000/api/proxies/${proxyId}/check`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '检测失败');
      }

      const updatedProxy = await response.json();
      // 直接使用返回的更新状态
      setProxies(prevProxies =>
        prevProxies.map(proxy =>
          proxy.id === proxyId ? updatedProxy : proxy
        )
      );
    } catch (error) {
      console.error('检测失败:', error);
      alert(error instanceof Error ? error.message : '检测失败，请稍后重试');
    } finally {
      setCheckingProxies(prev => {
        const newSet = new Set(prev);
        newSet.delete(proxyId);
        return newSet;
      });
    }
  };

  const toggleRowExpansion = (accountId: string) => {
    setExpandedRows(prev => {
      const newSet = new Set(prev);
      if (newSet.has(accountId)) {
        newSet.delete(accountId);
      } else {
        newSet.add(accountId);
      }
      return newSet;
    });
  };
  
  // 添加搜索过滤逻辑
  const filteredProxies = proxies.filter(proxy => {
    const searchLower = searchQuery.toLowerCase();
    return (
      proxy.id.toLowerCase().includes(searchLower) ||
      `${proxy.proxy_address}:${proxy.port}`.toLowerCase().includes(searchLower)
    );
  });

  // 更新分页逻辑以使用过滤后的数据
  const totalPages = Math.ceil(filteredProxies.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentProxies = filteredProxies.slice(startIndex, endIndex);

  // 当搜索条件改变时重置页码
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  if (loading) return <Loading />;
  if (error) return (
    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
      <div className="flex items-center">
        <XMarkIcon className="w-5 h-5 text-red-400 mr-2" />
        <span className="text-red-700">{error}</span>
      </div>
    </div>
  );

  // 空状态处理
  if (proxies.length === 0) {
    return (
      <DataCard>
        <EmptyState
          icon={<ServerIcon className="w-8 h-8 text-gray-400" />}
          title="暂无代理数据"
          description="系统中还没有添加任何代理，请先添加代理后再进行管理。"
        />
      </DataCard>
    );
  }

  return (
    <div className="space-y-6">
      {/* 头部控制区域 */}
      <DataCard>
        <DataCardHeader
          title="代理管理"
          subtitle={`共 ${proxies.length} 个代理，活跃 ${proxies.filter(proxy => proxy.status === 'healthy').length} 个`}
          icon={<ServerIcon className="w-6 h-6 text-green-600" />}
          action={
            <ActionButton
              onClick={onCheckAll || (() => {})}
              loading={checkingAll}
              icon={<ArrowPathIcon className="w-4 h-4" />}
              variant="primary"
              size="sm"
            >
              {checkingAll ? '检测中' : '检测所有'}
            </ActionButton>
          }
        />

        <DataCardContent>
          {/* 搜索栏 */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="搜索代理ID或地址..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <XMarkIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              </button>
            )}
          </div>
        </DataCardContent>
      </DataCard>

      {/* 数据展示区域 */}
      {filteredProxies.length === 0 ? (
        <DataCard>
          <EmptyState
            icon={<MagnifyingGlassIcon className="w-8 h-8 text-gray-400" />}
            title="未找到匹配的代理"
            description="请尝试调整搜索条件或清空搜索框查看所有代理。"
            action={
              <ActionButton
                onClick={() => setSearchQuery('')}
                variant="secondary"
                size="sm"
              >
                清空搜索
              </ActionButton>
            }
          />
        </DataCard>
      ) : (
        <DataCard>
          <div className="w-full">
            <table className="w-full table-fixed min-w-0">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="w-[40%] px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    代理信息
                  </th>
                  <th className="w-[20%] px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="w-[20%] px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    性能
                  </th>
                  <th className="w-[15%] px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    位置
                  </th>
                  <th className="w-[15%] px-2 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    {currentProxies.map((proxy, index) => {
                      const isExpanded = expandedRows.has(proxy.id);
                      return (
                        <React.Fragment key={proxy.id}>
                          <motion.tr
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3, delay: index * 0.05 }}
                            className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer"
                            onClick={() => toggleRowExpansion(proxy.id)}
                          >
                            <td className="px-2 py-2">
                              <div className="flex items-center space-x-2">
                                <button className="flex-shrink-0 p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded">
                                  {isExpanded ? (
                                    <svg className="w-3 h-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                    </svg>
                                  ) : (
                                    <svg className="w-3 h-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                    </svg>
                                  )}
                                </button>
                                <div className="min-w-0 flex-1">
                                  <CompactCell
                                    content={proxy.id}
                                    maxLength={isExpanded ? 50 : 18}
                                    className="font-mono text-xs"
                                  />
                                  {!isExpanded && (
                                    <div className="text-xs text-gray-500 dark:text-gray-400 truncate mt-1">
                                      {proxy.proxy_address}:{proxy.port}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </td>
                            <td className="px-2 py-2">
                              <StatusBadge
                                status={getStatusType(proxy.status)}
                                size="sm"
                              />
                              {!isExpanded && proxy.lastCheck && (
                                <div className="text-xs text-gray-500 dark:text-gray-400 truncate mt-1">
                                  {formatTime(proxy.lastCheck)}
                                </div>
                              )}
                            </td>
                            <td className="px-2 py-2">
                              {proxy.responseTime ? (
                                <div>
                                  <div className="flex items-center space-x-1">
                                    <div className="w-8 bg-gray-200 dark:bg-gray-600 rounded-full h-1 flex-shrink-0">
                                      <div
                                        className={`h-1 rounded-full ${getResponseTimeColor(proxy.responseTime)}`}
                                        style={{ width: `${Math.min(100, (1000 - proxy.responseTime) / 10)}%` }}
                                      ></div>
                                    </div>
                                    <span className="text-xs font-medium">{proxy.responseTime}ms</span>
                                  </div>
                                  {!isExpanded && (
                                    <div className="text-xs text-gray-500 dark:text-gray-400 truncate mt-1">
                                      {proxy.responseTime < 300 ? '快' : proxy.responseTime < 1000 ? '正常' : '慢'}
                                    </div>
                                  )}
                                </div>
                              ) : (
                                <span className="text-xs text-gray-400">未测试</span>
                              )}
                            </td>
                            <td className="px-2 py-2">
                              <div className="flex items-center space-x-1">
                                <GlobeAltIcon className="w-3 h-3 text-gray-400 flex-shrink-0" />
                                <div className="text-xs text-gray-700 dark:text-gray-300 truncate">
                                  {proxy.city_name}
                                </div>
                              </div>
                            </td>
                            <td className="px-2 py-2 text-center">
                              <ActionButton
                                onClick={(e) => handleCheckHealth(e, proxy.id)}
                                loading={checkingProxies.has(proxy.id)}
                                icon={<EyeIcon className="w-3 h-3" />}
                                size="xs"
                                variant="secondary"
                                className="w-full min-w-0"
                              >
                                <span className="">检测</span>
                              </ActionButton>
                            </td>
                          </motion.tr>

                          {/* 展开的详细信息行 */}
                          {isExpanded && (
                            <motion.tr
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              className="bg-gray-50 dark:bg-gray-800"
                            >
                              <td colSpan={5} className="px-4 py-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div className="space-y-3">
                                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">代理详情</h4>
                                    <div className="space-y-2">
                                      <div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">代理ID:</span>
                                        <CopyableCell content={proxy.id} maxWidth="max-w-full" className=" text-xs font-mono" />
                                      </div>
                                      <div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">地址:</span>
                                        <CopyableCell content={`${proxy.proxy_address}:${proxy.port}`} maxWidth="max-w-full" className=" text-xs font-mono" />
                                      </div>
                                      <div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">位置:</span>
                                        <span className="ml-2 text-xs">{proxy.city_name}, {proxy.country_code}</span>
                                      </div>
                                    </div>
                                  </div>

                                  <div className="space-y-3">
                                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">状态信息</h4>
                                    <div className="space-y-2">
                                      <div className="flex items-center space-x-2">
                                        <span className="text-xs text-gray-500 dark:text-gray-400">状态:</span>
                                        <StatusBadge status={getStatusType(proxy.status)} size="sm" />
                                      </div>
                                      {proxy.lastCheck && (
                                        <div>
                                          <span className="text-xs text-gray-500 dark:text-gray-400">最后检查:</span>
                                          <span className="ml-2 text-xs">{formatTime(proxy.lastCheck)}</span>
                                        </div>
                                      )}
                                      {proxy.responseTime && (
                                        <div>
                                          <span className="text-xs text-gray-500 dark:text-gray-400">响应时间:</span>
                                          <div className="mt-1 flex items-center space-x-2">
                                            <div className="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                                              <div
                                                className={`h-2 rounded-full ${getResponseTimeColor(proxy.responseTime)}`}
                                                style={{ width: `${Math.min(100, (1000 - proxy.responseTime) / 10)}%` }}
                                              ></div>
                                            </div>
                                            <span className="text-xs font-medium">{proxy.responseTime}ms</span>
                                            <span className="text-xs text-gray-500">({formatResponseTime(proxy.responseTime)})</span>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </td>
                            </motion.tr>
                          )}
                        </React.Fragment>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </DataCard>
      )}

      {/* 分页组件 */}
      {totalPages > 1 && (
        <DataCard>
          <DataCardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  显示第 <span className="font-medium">{startIndex + 1}</span> 到{' '}
                  <span className="font-medium">{Math.min(endIndex, filteredProxies.length)}</span> 条，
                  共 <span className="font-medium">{filteredProxies.length}</span> 条记录
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <ActionButton
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  variant="ghost"
                  size="sm"
                >
                  上一页
                </ActionButton>
                <span className="px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg">
                  {currentPage} / {totalPages}
                </span>
                <ActionButton
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  variant="ghost"
                  size="sm"
                >
                  下一页
                </ActionButton>
              </div>
            </div>
          </DataCardContent>
        </DataCard>
      )}
    </div>
  );
}