'use client';

import { useState } from 'react';

interface HealthCheckPanelProps {
  selectedProxyId?: string;
  selectedAccountId?: string;
}

export default function HealthCheckPanel({ selectedProxyId, selectedAccountId }: HealthCheckPanelProps) {
  const [concurrency, setConcurrency] = useState(5);
  const [isChecking, setIsChecking] = useState(false);

  const startHealthCheck = async () => {
    try {
      setIsChecking(true);
      const response = await fetch('http://localhost:8000/api/health-check/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          concurrency,
          proxy_id: selectedProxyId,
          account_id: selectedAccountId,
        }),
      });
      
      if (!response.ok) {
        throw new Error('健康检测启动失败');
      }
      
      const data = await response.json();
      alert(data.message);
    } catch (error) {
      alert(error instanceof Error ? error.message : '发生未知错误');
    } finally {
      setIsChecking(false);
    }
  };

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">健康检测控制面板</h2>
        <div className="flex items-center space-x-4">
          <div>
            <label htmlFor="concurrency" className="block text-sm font-medium text-gray-700">
              并发数
            </label>
            <input
              type="number"
              id="concurrency"
              min="1"
              max="50"
              value={concurrency}
              onChange={(e) => setConcurrency(Number(e.target.value))}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>
          <button
            onClick={startHealthCheck}
            disabled={isChecking}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {isChecking ? '检测中...' : selectedProxyId || selectedAccountId ? '检测选中项' : '检测全部'}
          </button>
        </div>
        {(selectedProxyId || selectedAccountId) && (
          <div className="mt-4 text-sm text-gray-500">
            当前选中: {selectedProxyId ? `代理 ${selectedProxyId}` : ''} {selectedAccountId ? `账户 ${selectedAccountId}` : ''}
          </div>
        )}
      </div>
    </div>
  );
} 