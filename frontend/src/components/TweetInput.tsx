'use client';

import { useState } from 'react';

interface TweetInputProps {
  onSubmit: (url: string) => void;
  loading: boolean;
}

export default function TweetInput({ onSubmit, loading }: TweetInputProps) {
  const [url, setUrl] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (url.trim()) {
      onSubmit(url.trim());
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4">
        <input
          type="url"
          value={url}
          onChange={(e) => setUrl(e.target.value)}
          placeholder="Enter Twitter post URL"
          className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
          disabled={loading}
        />
        <button
          type="submit"
          disabled={loading || !url.trim()}
          className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
        >
          {loading ? 'Analyzing...' : 'Analyze'}
        </button>
      </div>
      <p className="text-sm text-gray-500" onClick={() => setUrl("https://twitter.com/nocontextfooty/status/1933881320712638829")}>
        Example: https://twitter.com/nocontextfooty/status/1933881320712638829
      </p>
    </form>
  );
} 