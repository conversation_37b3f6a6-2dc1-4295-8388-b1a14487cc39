import React, { forwardRef } from 'react';
import { ButtonProps } from '@/types/ui';
import { cn } from '@/utils/cn';

/**
 * 按钮组件
 * 支持多种变体、尺寸和状态的通用按钮组件
 */
export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      children,
      variant = 'primary',
      size = 'md',
      loading = false,
      disabled = false,
      fullWidth = false,
      icon,
      iconPosition = 'left',
      className,
      ...props
    },
    ref
  ) => {
    const baseStyles = [
      'inline-flex items-center justify-center',
      'font-medium transition-all duration-200',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'relative overflow-hidden',
    ].join(' ');

    const variants = {
      primary: [
        'bg-blue-500 text-white border border-blue-500',
        'hover:bg-blue-600 hover:border-blue-600',
        'focus:ring-blue-500',
        'dark:bg-blue-600 dark:border-blue-600',
        'dark:hover:bg-blue-700 dark:hover:border-blue-700',
      ].join(' '),
      secondary: [
        'bg-gray-100 text-gray-900 border border-gray-200',
        'hover:bg-gray-200 hover:border-gray-300',
        'focus:ring-gray-500',
        'dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600',
        'dark:hover:bg-gray-600 dark:hover:border-gray-500',
      ].join(' '),
      success: [
        'bg-green-500 text-white border border-green-500',
        'hover:bg-green-600 hover:border-green-600',
        'focus:ring-green-500',
      ].join(' '),
      warning: [
        'bg-yellow-500 text-white border border-yellow-500',
        'hover:bg-yellow-600 hover:border-yellow-600',
        'focus:ring-yellow-500',
      ].join(' '),
      error: [
        'bg-red-500 text-white border border-red-500',
        'hover:bg-red-600 hover:border-red-600',
        'focus:ring-red-500',
      ].join(' '),
      ghost: [
        'bg-transparent text-gray-600 border border-transparent',
        'hover:bg-gray-100 hover:text-gray-900',
        'focus:ring-gray-500',
        'dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-100',
      ].join(' '),
    };

    const sizes = {
      xs: 'px-2 py-1 text-xs rounded-md gap-1',
      sm: 'px-3 py-1.5 text-sm rounded-md gap-1.5',
      md: 'px-4 py-2 text-sm rounded-lg gap-2',
      lg: 'px-6 py-3 text-base rounded-lg gap-2',
      xl: 'px-8 py-4 text-lg rounded-xl gap-3',
    };

    const isDisabled = disabled || loading;

    return (
      <button
        ref={ref}
        className={cn(
          baseStyles,
          variants[variant],
          sizes[size],
          fullWidth && 'w-full',
          className
        )}
        disabled={isDisabled}
        {...props}
      >
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          </div>
        )}

        <span className={cn('flex items-center gap-inherit', loading && 'opacity-0')}>
          {icon && iconPosition === 'left' && (
            <span className="flex-shrink-0">{icon}</span>
          )}
          {children}
          {icon && iconPosition === 'right' && (
            <span className="flex-shrink-0">{icon}</span>
          )}
        </span>
      </button>
    );
  }
);

Button.displayName = 'Button';