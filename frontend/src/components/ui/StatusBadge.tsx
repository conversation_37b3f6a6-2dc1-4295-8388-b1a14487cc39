'use client';

import { 
  CheckCircleIcon, 
  XCircleIcon, 
  ExclamationTriangleIcon,
  QuestionMarkCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { 
  CheckCircleIcon as CheckCircleIconSolid,
  XCircleIcon as XCircleIconSolid,
  ExclamationTriangleIcon as ExclamationTriangleIconSolid
} from '@heroicons/react/24/solid';

export type StatusType = 'healthy' | 'unhealthy' | 'no_proxy' | 'unknown' | 'checking';

interface StatusBadgeProps {
  status: StatusType;
  text?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'outline' | 'filled';
  showIcon?: boolean;
  className?: string;
}

const statusConfig = {
  healthy: {
    colors: {
      outline: 'bg-green-50 text-green-700 border-green-200',
      filled: 'bg-green-100 text-green-800'
    },
    icon: CheckCircleIcon,
    iconSolid: CheckCircleIconSolid,
    defaultText: '正常'
  },
  unhealthy: {
    colors: {
      outline: 'bg-red-50 text-red-700 border-red-200',
      filled: 'bg-red-100 text-red-800'
    },
    icon: XCircleIcon,
    iconSolid: XCircleIconSolid,
    defaultText: '异常'
  },
  no_proxy: {
    colors: {
      outline: 'bg-yellow-50 text-yellow-700 border-yellow-200',
      filled: 'bg-yellow-100 text-yellow-800'
    },
    icon: ExclamationTriangleIcon,
    iconSolid: ExclamationTriangleIconSolid,
    defaultText: '无代理'
  },
  unknown: {
    colors: {
      outline: 'bg-gray-50 text-gray-700 border-gray-200',
      filled: 'bg-gray-100 text-gray-800'
    },
    icon: QuestionMarkCircleIcon,
    iconSolid: QuestionMarkCircleIcon,
    defaultText: '未知'
  },
  checking: {
    colors: {
      outline: 'bg-blue-50 text-blue-700 border-blue-200',
      filled: 'bg-blue-100 text-blue-800'
    },
    icon: ClockIcon,
    iconSolid: ClockIcon,
    defaultText: '检测中'
  }
};

const sizeConfig = {
  sm: {
    badge: 'px-2 py-1 text-xs',
    icon: 'w-3 h-3'
  },
  md: {
    badge: 'px-2.5 py-1.5 text-sm',
    icon: 'w-4 h-4'
  },
  lg: {
    badge: 'px-3 py-2 text-base',
    icon: 'w-5 h-5'
  }
};

export default function StatusBadge({
  status,
  text,
  size = 'md',
  variant = 'filled',
  showIcon = true,
  className = ''
}: StatusBadgeProps) {
  const config = statusConfig[status];
  const sizeStyles = sizeConfig[size];
  const Icon = variant === 'filled' ? config.iconSolid : config.icon;
  
  const badgeClasses = [
    'inline-flex items-center font-medium rounded-full',
    sizeStyles.badge,
    config.colors[variant],
    variant === 'outline' ? 'border' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <span className={badgeClasses}>
      {showIcon && (
        <Icon 
          className={`${sizeStyles.icon} ${size === 'sm' ? 'mr-1' : 'mr-1.5'} ${
            status === 'checking' ? 'animate-spin' : ''
          }`} 
        />
      )}
      {text || config.defaultText}
    </span>
  );
}
