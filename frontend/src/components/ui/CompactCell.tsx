'use client';

import { useState } from 'react';
import { ClipboardIcon, CheckIcon } from '@heroicons/react/24/outline';

interface CompactCellProps {
  content: string;
  className?: string;
  maxLength?: number;
  showCopyButton?: boolean;
}

export default function CompactCell({ 
  content, 
  className = '', 
  maxLength = 20,
  showCopyButton = true 
}: CompactCellProps) {
  const [copied, setCopied] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleCopy = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await navigator.clipboard.writeText(content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  const truncatedContent = content.length > maxLength 
    ? `${content.substring(0, maxLength)}...` 
    : content;

  return (
    <div 
      className={`group relative inline-flex items-center space-x-1 ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      title={content}
    >
      <span className="truncate text-sm">
        {truncatedContent}
      </span>
      
      {showCopyButton && (
        <button
          onClick={handleCopy}
          className={`flex-shrink-0 p-0.5 rounded transition-all duration-200 ${
            isHovered || copied
              ? 'opacity-100 bg-gray-100 dark:bg-gray-700'
              : 'opacity-0 group-hover:opacity-100'
          }`}
          title={copied ? '已复制!' : '点击复制'}
        >
          {copied ? (
            <CheckIcon className="w-3 h-3 text-green-600" />
          ) : (
            <ClipboardIcon className="w-3 h-3 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" />
          )}
        </button>
      )}
    </div>
  );
}
