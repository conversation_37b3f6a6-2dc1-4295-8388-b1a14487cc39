"use client";

import { ReactNode } from "react";
import { ArrowPathIcon } from "@heroicons/react/24/outline";

interface ActionButtonProps {
  onClick: (e: React.MouseEvent) => void;
  disabled?: boolean;
  loading?: boolean;
  variant?: "primary" | "secondary" | "danger" | "ghost";
  size?: "xs" | "sm" | "md" | "lg";
  icon?: ReactNode;
  children: ReactNode;
  className?: string;
  fullWidth?: boolean;
}

const variantStyles = {
  primary: {
    base: "bg-blue-600 text-white border-transparent hover:bg-blue-700 focus:ring-blue-500",
    disabled: "bg-gray-400 text-white cursor-not-allowed",
  },
  secondary: {
    base: "bg-gray-100 text-gray-900 border-gray-300 hover:bg-gray-200 focus:ring-gray-500",
    disabled: "bg-gray-100 text-gray-400 cursor-not-allowed",
  },
  danger: {
    base: "bg-red-600 text-white border-transparent hover:bg-red-700 focus:ring-red-500",
    disabled: "bg-gray-400 text-white cursor-not-allowed",
  },
  ghost: {
    base: "bg-transparent text-gray-700 border-gray-300 hover:bg-gray-50 focus:ring-gray-500",
    disabled: "bg-transparent text-gray-400 cursor-not-allowed",
  },
};

const sizeStyles = {
  xs: "px-2 py-1 text-xs",
  sm: "px-3 py-1.5 text-sm",
  md: "px-4 py-2 text-sm",
  lg: "px-6 py-3 text-base",
};

export default function ActionButton({
  onClick,
  disabled = false,
  loading = false,
  variant = "primary",
  size = "sm",
  icon,
  children,
  className = "",
  fullWidth = false,
}: ActionButtonProps) {
  const isDisabled = disabled || loading;
  const variantStyle = variantStyles[variant];

  const buttonClasses = [
    "inline-flex items-center justify-center font-medium rounded-lg border transition-all duration-200",
    "focus:outline-none focus:ring-2 focus:ring-offset-2",
    sizeStyles[size],
    isDisabled ? variantStyle.disabled : variantStyle.base,
    fullWidth ? "w-full" : "",
    className,
  ]
    .filter(Boolean)
    .join(" ");

  return (
    <button onClick={onClick} disabled={isDisabled} className={buttonClasses}>
      {loading ? (
        <>
          <ArrowPathIcon className="w-4 h-4 mr-2 animate-spin" />
          {typeof children === "string" ? "处理中..." : children}
        </>
      ) : (
        <>
          {icon && <span className="mr-2">{icon}</span>}
          {children}
        </>
      )}
    </button>
  );
}
