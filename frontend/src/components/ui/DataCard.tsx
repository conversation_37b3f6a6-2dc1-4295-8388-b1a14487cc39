'use client';

import { ReactNode } from 'react';
import { motion } from 'framer-motion';

interface DataCardProps {
  children: ReactNode;
  className?: string;
  hover?: boolean;
  onClick?: () => void;
  selected?: boolean;
}

export default function DataCard({
  children,
  className = '',
  hover = true,
  onClick,
  selected = false
}: DataCardProps) {
  const cardClasses = [
    'bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700',
    'shadow-sm transition-all duration-200',
    hover ? 'hover:shadow-md hover:border-gray-300 dark:hover:border-gray-600' : '',
    onClick ? 'cursor-pointer' : '',
    selected ? 'ring-2 ring-blue-500 border-blue-500' : '',
    className
  ].filter(Boolean).join(' ');

  const CardComponent = hover ? motion.div : 'div';
  const motionProps = hover ? {
    whileHover: { y: -2 },
    transition: { duration: 0.2 }
  } : {};

  return (
    <CardComponent
      className={cardClasses}
      onClick={onClick}
      {...motionProps}
    >
      {children}
    </CardComponent>
  );
}

interface DataCardHeaderProps {
  title: string;
  subtitle?: string;
  action?: ReactNode;
  icon?: ReactNode;
}

export function DataCardHeader({ title, subtitle, action, icon }: DataCardHeaderProps) {
  return (
    <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
      <div className="flex items-center space-x-3">
        {icon && (
          <div className="flex-shrink-0">
            {icon}
          </div>
        )}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {title}
          </h3>
          {subtitle && (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {subtitle}
            </p>
          )}
        </div>
      </div>
      {action && (
        <div className="flex-shrink-0">
          {action}
        </div>
      )}
    </div>
  );
}

interface DataCardContentProps {
  children: ReactNode;
  className?: string;
}

export function DataCardContent({ children, className = '' }: DataCardContentProps) {
  return (
    <div className={`p-4 ${className}`}>
      {children}
    </div>
  );
}

interface DataCardFooterProps {
  children: ReactNode;
  className?: string;
}

export function DataCardFooter({ children, className = '' }: DataCardFooterProps) {
  return (
    <div className={`px-4 py-3 bg-gray-50 dark:bg-gray-700/50 rounded-b-xl border-t border-gray-200 dark:border-gray-700 ${className}`}>
      {children}
    </div>
  );
}
