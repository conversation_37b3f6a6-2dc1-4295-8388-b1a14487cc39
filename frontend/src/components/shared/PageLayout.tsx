'use client';

import { ReactNode } from 'react';
import Link from 'next/link';
import Navigation from '@/components/Navigation';
import { FireIcon } from '@heroicons/react/24/outline';

interface PageLayoutProps {
  children: ReactNode;
  className?: string;
  showFooter?: boolean;
}

export default function PageLayout({ 
  children, 
  className = "min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",
  showFooter = true 
}: PageLayoutProps) {
  return (
    <div className={className}>
      <Navigation />
      
      <main className="flex-1">
        {children}
      </main>

      {showFooter && (
        <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                <div className="flex items-center space-x-2 mb-4">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                    <FireIcon className="w-5 h-5 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white">Twitter Monitor</h3>
                </div>
                <p className="text-gray-600 dark:text-gray-400">
                  专业的 Twitter 数据监控和分析平台，助您掌握社交媒体趋势。
                </p>
              </div>
              <div>
                <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-4">功能</h4>
                <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <li>热门推文监控</li>
                  <li>账户状态管理</li>
                  <li>推文内容分析</li>
                  <li>实时数据更新</li>
                </ul>
              </div>
              <div>
                <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-4">快速链接</h4>
                <ul className="space-y-2 text-sm">
                  <li>
                    <Link href="/trends" className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                      热门趋势
                    </Link>
                  </li>
                  <li>
                    <Link href="/monitor" className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                      监控面板
                    </Link>
                  </li>
                  <li>
                    <Link href="/analyze" className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                      推文分析
                    </Link>
                  </li>
                </ul>
              </div>
            </div>
            <div className="border-t border-gray-200 dark:border-gray-700 mt-8 pt-8 text-center">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                © 2024 Twitter Monitor. 专业的社交媒体数据分析平台.
              </p>
            </div>
          </div>
        </footer>
      )}
    </div>
  );
}
