"use client";

import { useState } from "react";
import Image from "next/image";
import { TweetMedia } from "@/types/tweet";
import ImagePreview from "./ImagePreview";

interface MediaDisplayProps {
  media: TweetMedia | TweetMedia[];
  onDownload?: (url: string) => void;
  className?: string;
  features?: {
    enableStreamSelection?: boolean;
    enableDownload?: boolean;
  };
  maxHeight?: number;
  maxWidth?: string | number;
}

export default function MediaDisplay({
  media,
  onDownload,
  className = "",
  features = {
    enableStreamSelection: false,
    enableDownload: false,
  },
  maxHeight = 600,
  maxWidth = '100%',
}: MediaDisplayProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [selectedStream, setSelectedStream] = useState<string | null>(null);
  const [videoLoaded, setVideoLoaded] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [previewIndex, setPreviewIndex] = useState(0);

  const mediaArray = Array.isArray(media) ? media : [media];
  const isMultiple = mediaArray.length > 1;
  const displayMedia = mediaArray.slice(0, 4);

  const handleVideoPlay = (item: TweetMedia) => {
    console.log(item?.streams)
    if (item?.streams?.length && item?.streams?.length > 0) {
      setSelectedStream(item.streams[0].url);
      setIsPlaying(true);
    }
  };

  const handleDownload = (url: string) => {
    if (onDownload) {
      onDownload(url);
    } else {
      window.open(url, "_blank");
    }
  };

  const handleImageClick = (index: number) => {
    setPreviewIndex(index);
    setShowPreview(true);
  };

  // 获取媒体比例
  const getAspectRatio = (mediaItem: TweetMedia) => {
    // 视频优先用 video_info.aspect_ratio
    if (
      mediaItem.type === "video" &&
      mediaItem.video_info?.aspect_ratio &&
      Array.isArray(mediaItem.video_info.aspect_ratio)
    ) {
      const [w, h] = mediaItem.video_info.aspect_ratio;
      return w / h;
    }
    // 图片用 width/height
    if (mediaItem.width && mediaItem.height) {
      return mediaItem.width / mediaItem.height;
    }
    // 默认 9:16
    return 9 / 16;
  };

  return (
    <>
      <div
        className={`relative w-full rounded-lg overflow-hidden group ${className}`}
        style={{
          maxHeight,
          minHeight: maxHeight,
          maxWidth,
          display: 'flex',
          flexWrap: 'wrap',
          gap: '8px',
        }}
      >
        {displayMedia.map((item, index) => (
          <div
            key={index}
            style={{
              width: displayMedia.length === 1 ? '100%' : `calc(50% - 4px)`,
              height:
                displayMedia.length <= 2
                  ? maxHeight
                  : maxHeight / 2,
              position: 'relative',
              cursor: item.type === 'photo' ? 'pointer' : 'default',
            }}
            onClick={item.type === 'photo' ? () => handleImageClick(index) : undefined}
          >
            {item.type === "photo" && (
              <Image
                src={item.media_url}
                alt={item.display_url}
                fill
                className="object-cover rounded-xl shadow w-full h-full"
                sizes="100vw"
                priority
              />
            )}
            {item.type === "video" && (
              <>
                {!isPlaying ? (
                  <div className="relative w-full h-full bg-black" >
                    <Image
                      src={item.media_url}
                      alt="Video thumbnail"
                      fill
                      className="object-cover rounded-sm h-full w-full"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      priority
                    />
                    <div className="w-full h-full absolute inset-0 bg-black/50 flex items-center justify-center opacity-0 opacity-100 transition-opacity duration-300">
                      <button
                        onClick={() => handleVideoPlay(item)}
                        className="bg-white text-gray-900 p-4 rounded-full hover:bg-gray-100 transition-colors"
                      >
                        <svg
                          className="w-8 h-8"
                          fill="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path d="M8 5v14l11-7z" />
                        </svg>
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className={`flex flex-col w-full h-full ${features.enableStreamSelection ? "pb-[40px]" : ''} ${displayMedia.length === 1 ? 'p-4' : ''}`}>
                    <div className="relative w-full h-full ">
                      <div className="relative w-full h-full">
                        {!videoLoaded && (
                          <Image
                            src={item.media_url}
                            alt="Video thumbnail"
                            fill
                            className="object-cover h-full w-full"
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            priority
                          />
                        )}
                        <video
                          src={selectedStream || ""}
                          controls
                          autoPlay
                          poster={item.media_url}
                          className={`w-full h-full object-contain rounded-md ${videoLoaded ? "opacity-100" : "opacity-0"
                            }`}
                          style={{ transition: "opacity 0.3s ease-in-out" }}
                          onEnded={() => setIsPlaying(false)}
                          onLoadedData={() => setVideoLoaded(true)}
                        />
                      </div>
                    </div>
                    <div className={`flex items-center justify-between mt-2 ${displayMedia.length === 1 ? "" : 'pr-2'}`}>
                      {features.enableStreamSelection &&
                        item.streams &&
                        item.streams.length > 1 && (
                          <div className="flex flex-wrap gap-2">
                            {item.streams.map((stream, idx) => (
                              <button
                                key={idx}
                                onClick={() => setSelectedStream(stream.url)}
                                className={`px-2 py-1 rounded text-sm transition-colors cursor-pointer ${selectedStream === stream.url
                                  ? "bg-blue-500 text-white"
                                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                                  }`}
                              >
                                {stream.bitrate / 1000}k
                              </button>
                            ))}
                          </div>
                        )}
                      {features.enableDownload && (
                        <button
                          onClick={() => handleDownload(selectedStream || "")}
                          className="flex items-center cursor-pointer space-x-1 text-gray-500 hover:text-blue-500 transition-colors"
                        >
                          <svg
                            className="w-4 h-4"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" />
                          </svg>
                          <span className="text-sm">下载</span>
                        </button>
                      )}
                    </div>
                  </div>
                )}
              </>
            )}
            {item.type === "animated_gif" && (
              <Image
                src={item.media_url}
                alt="GIF"
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                priority
              />
            )}
          </div>
        ))}
      </div>
      {showPreview && mediaArray[0].type === "photo" && (
        <ImagePreview
          media={mediaArray}
          initialIndex={previewIndex}
          onClose={() => setShowPreview(false)}
        />
      )}
    </>
  );
}
