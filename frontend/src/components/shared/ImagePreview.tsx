import { useEffect, useState, useRef } from "react";
import Image from "next/image";
import { TweetMedia } from "@/types/tweet";

interface ImagePreviewProps {
  media: TweetMedia[];
  initialIndex?: number;
  onClose: () => void;
}

export default function ImagePreview({
  media,
  initialIndex = 0,
  onClose,
}: ImagePreviewProps) {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "ArrowLeft") {
        setCurrentIndex((prev) => (prev > 0 ? prev - 1 : prev));
      } else if (e.key === "ArrowRight") {
        setCurrentIndex((prev) => (prev < media.length - 1 ? prev + 1 : prev));
      } else if (e.key === "Escape") {
        onClose();
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [media.length, onClose]);
  // 点击遮罩关闭，点击图片不关闭
  const handleMaskClick = (e: React.MouseEvent<HTMLDivElement>) => {
    console.log(e.target);
    if (e.target === containerRef.current) {
      onClose();
    }
  };

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center select-none"
      onClick={handleMaskClick}
    >
      {/* <div className="relative max-w-[1360px] max-h-[960px] w-full h-full">
          <Image
            src={media[currentIndex].media_url}
            alt={`Image ${currentIndex + 1}`}
            fill
            className="w-full h-auto object-co select-none"
            onClick={(e) => e.stopPropagation()}
          />
        </div> */}
      {/* 关闭按钮 */}
      <button
        onClick={onClose}
        className="absolute top-8 right-8 text-white hover:text-gray-300 z-10"
      >
        <svg
          className="w-8 h-8"
          style={{
            transform: "scale(1.2)",
          }}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>

      <div className="relative max-w-[90vw] max-h-[90vh] w-full h-full flex items-center justify-center">
        <div className="relative w-full h-full">
          <Image
            src={media[currentIndex].media_url}
            alt={`Image ${currentIndex + 1}`}
            fill
            className="w-full h-auto select-none object-contain"
            onClick={(e) => e.stopPropagation()}
          />
        </div>
        {/* 左右按钮始终可见 */}
        {media.length > 1 && (
          <>
            <button
              onClick={() =>
                setCurrentIndex((prev) => (prev > 0 ? prev - 1 : prev))
              }
              className="absolute left-4 top-1/2 -translate-y-1/2 text-white bg-black/40 hover:bg-black/70 rounded-full p-2 z-10 disabled:opacity-50"
              disabled={currentIndex === 0}
            >
              <svg
                className="w-10 h-10"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
            <button
              onClick={() =>
                setCurrentIndex((prev) =>
                  prev < media.length - 1 ? prev + 1 : prev
                )
              }
              className="absolute right-4 top-1/2 -translate-y-1/2 text-white bg-black/40 hover:bg-black/70 rounded-full p-2 z-10 disabled:opacity-50"
              disabled={currentIndex === media.length - 1}
            >
              <svg
                className="w-10 h-10"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 text-white bg-black/40 rounded px-3 py-1 text-sm z-10">
              {currentIndex + 1} / {media.length}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
