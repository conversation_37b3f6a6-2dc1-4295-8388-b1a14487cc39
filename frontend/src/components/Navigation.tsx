'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import {
  ComputerDesktopIcon,
  MagnifyingGlassIcon,
  FireIcon,
  ChartBarIcon,
  HomeIcon
} from '@heroicons/react/24/outline';

export default function Navigation() {
  const pathname = usePathname();

  const isActive = (path: string) => pathname === path;

  return (
    <nav className="sticky top-0 z-50 bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-700/50 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-4">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                <FireIcon className="w-5 h-5 text-white" />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Twx
              </h1>
            </Link>
          </div>
          <div className="flex items-center space-x-2">
            <Link href="/">
              <Button
                variant={isActive('/') ? 'primary' : 'ghost'}
                size="sm"
                className="flex items-center space-x-2"
              >
                <HomeIcon className="w-4 h-4" />
                <span className="hidden sm:inline">首页</span>
              </Button>
            </Link>
            <Link href="/trends">
              <Button
                variant={isActive('/trends') ? 'primary' : 'ghost'}
                size="sm"
                className="flex items-center space-x-2"
              >
                <ChartBarIcon className="w-4 h-4" />
                <span className="hidden sm:inline">热门趋势</span>
              </Button>
            </Link>
            <Link href="/monitor">
              <Button
                variant={isActive('/monitor') ? 'primary' : 'ghost'}
                size="sm"
                className="flex items-center space-x-2"
              >
                <ComputerDesktopIcon className="w-4 h-4" />
                <span className="hidden sm:inline">监控面板</span>
              </Button>
            </Link>
            <Link href="/analyze">
              <Button
                variant={isActive('/analyze') ? 'primary' : 'ghost'}
                size="sm"
                className="flex items-center space-x-2"
              >
                <MagnifyingGlassIcon className="w-4 h-4" />
                <span className="hidden sm:inline">推文分析</span>
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </nav>
  );
}