'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import CopyableCell from './CopyableCell';
import CompactCell from './ui/CompactCell';
import Loading from './Loading';
import StatusBadge, { StatusType } from './ui/StatusBadge';
import ActionButton from './ui/ActionButton';
import DataCard, { DataCardHeader, DataCardContent, DataCardFooter } from './ui/DataCard';
import EmptyState from './ui/EmptyState';
import {
  MagnifyingGlassIcon,
  ArrowPathIcon,
  UserGroupIcon,
  EyeIcon,
  ServerIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface Account {
  account_id: string;
  auth_info_1: string;
  auth_info_2: string;
  status: string;
  lastCheck: string;
  proxyStatus?: string;
  error?: string;
  proxy?: {
    id: string;
    proxy_url: string;
    country_code: string;
    city_name: string;
  };
}

interface AccountListProps {
  onCheckHealth?: (accountId: string) => void;
  onCheckAll?: () => void;
  checkingAll?: boolean;
}

// 状态转换辅助函数
const getStatusType = (status: string): StatusType => {
  switch (status) {
    case 'healthy': return 'healthy';
    case 'unhealthy': return 'unhealthy';
    case 'no_proxy': return 'no_proxy';
    default: return 'unknown';
  }
};

// 格式化时间
const formatTime = (timeString: string): string => {
  try {
    const date = new Date(timeString);
    return date.toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return '未知';
  }
};

export default function AccountList({ onCheckHealth, onCheckAll, checkingAll }: AccountListProps) {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [checkingAccounts, setCheckingAccounts] = useState<Set<string>>(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const itemsPerPage = 10;

  const fetchAccounts = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/accounts');
      if (!response.ok) {
        throw new Error('获取账户列表失败');
      }
      const data = await response.json();
      setAccounts(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取账户列表失败');
    } finally {
      setLoading(false);
    }
  };

  const updateAccountStatus = async (accountId: string) => {
    try {
      const response = await fetch(`http://localhost:8000/api/accounts/${accountId}`);
      if (!response.ok) {
        throw new Error('获取账户状态失败');
      }
      const updatedAccount = await response.json();
      setAccounts(prevAccounts => 
        prevAccounts.map(account => 
          account.account_id === accountId ? updatedAccount : account
        )
      );
    } catch (err) {
      console.error('更新账户状态失败:', err);
    }
  };

  useEffect(() => {
    fetchAccounts();
    const interval = setInterval(fetchAccounts, 5000); // 每5秒刷新一次
    return () => clearInterval(interval);
  }, []);

  const handleCheckHealth = async (e: React.MouseEvent, accountId: string) => {
    e.stopPropagation(); // 防止触发行点击事件
    setCheckingAccounts(prev => new Set(prev).add(accountId));
    try {
      const response = await fetch(`http://localhost:8000/api/accounts/${accountId}/check`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '检测失败');
      }

      const updatedAccount = await response.json();
      // 直接使用返回的更新状态
      setAccounts(prevAccounts =>
        prevAccounts.map(account =>
          account.account_id === accountId ? updatedAccount : account
        )
      );
    } catch (error) {
      console.error('检测失败:', error);
      alert(error instanceof Error ? error.message : '检测失败，请稍后重试');
    } finally {
      setCheckingAccounts(prev => {
        const newSet = new Set(prev);
        newSet.delete(accountId);
        return newSet;
      });
    }
  };

  const toggleRowExpansion = (accountId: string) => {
    setExpandedRows(prev => {
      const newSet = new Set(prev);
      if (newSet.has(accountId)) {
        newSet.delete(accountId);
      } else {
        newSet.add(accountId);
      }
      return newSet;
    });
  };

  // 添加搜索过滤逻辑
  const filteredAccounts = accounts.filter(account => {
    const searchLower = searchQuery.toLowerCase();
    return (
      account.account_id.toLowerCase().includes(searchLower) ||
      account.auth_info_1.toLowerCase().includes(searchLower) ||
      account.auth_info_2.toLowerCase().includes(searchLower)
    );
  });

  // 更新分页逻辑以使用过滤后的数据
  const totalPages = Math.ceil(filteredAccounts.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentAccounts = filteredAccounts.slice(startIndex, endIndex);

  // 当搜索条件改变时重置页码
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  if (loading) return <Loading />;
  if (error) return (
    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
      <div className="flex items-center">
        <XMarkIcon className="w-5 h-5 text-red-400 mr-2" />
        <span className="text-red-700">{error}</span>
      </div>
    </div>
  );

  // 空状态处理
  if (accounts.length === 0) {
    return (
      <DataCard>
        <EmptyState
          icon={<UserGroupIcon className="w-8 h-8 text-gray-400" />}
          title="暂无账户数据"
          description="系统中还没有添加任何账户，请先添加账户后再进行管理。"
        />
      </DataCard>
    );
  }

  return (
    <div className="space-y-6">
      {/* 头部控制区域 */}
      <DataCard>
        <DataCardHeader
          title="账户管理"
          subtitle={`共 ${accounts.length} 个账户，活跃 ${accounts.filter(a => a.status === 'healthy').length} 个`}
          icon={<UserGroupIcon className="w-6 h-6 text-blue-600" />}
          action={
            <ActionButton
              onClick={onCheckAll || (() => {})}
              loading={checkingAll}
              icon={<ArrowPathIcon className="w-4 h-4" />}
              variant="primary"
              size="sm"
            >
              {checkingAll ? '检测中' : '检测所有'}
            </ActionButton>
          }
        />

        <DataCardContent>
          {/* 搜索栏 */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="搜索账户ID、用户名或邮箱..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <XMarkIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              </button>
            )}
          </div>
        </DataCardContent>
      </DataCard>

      {/* 数据展示区域 */}
      {filteredAccounts.length === 0 ? (
        <DataCard>
          <EmptyState
            icon={<MagnifyingGlassIcon className="w-8 h-8 text-gray-400" />}
            title="未找到匹配的账户"
            description="请尝试调整搜索条件或清空搜索框查看所有账户。"
            action={
              <ActionButton
                onClick={() => setSearchQuery('')}
                variant="secondary"
                size="sm"
              >
                清空搜索
              </ActionButton>
            }
          />
        </DataCard>
      ) : (
        <DataCard>
          <div className="w-full">
            <table className="w-full table-fixed min-w-0">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="w-[45%] px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    账户信息
                  </th>
                  <th className="w-[25%] px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="w-[15%] px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    代理
                  </th>
                  <th className="w-[15%] px-2 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    {currentAccounts.map((account, index) => {
                      const isExpanded = expandedRows.has(account.account_id);
                      return (
                        <React.Fragment key={account.account_id}>
                          <motion.tr
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3, delay: index * 0.05 }}
                            className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer"
                            onClick={() => toggleRowExpansion(account.account_id)}
                          >
                            <td className="px-2 py-2">
                              <div className="flex items-center space-x-2">
                                <button className="flex-shrink-0 p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded">
                                  {isExpanded ? (
                                    <svg className="w-3 h-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                    </svg>
                                  ) : (
                                    <svg className="w-3 h-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                    </svg>
                                  )}
                                </button>
                                <div className="min-w-0 flex-1">
                                  <CompactCell
                                    content={account.account_id}
                                    maxLength={isExpanded ? 50 : 35}
                                    className="font-mono text-xs"
                                  />
                                  {!isExpanded && (
                                    <div className="text-xs text-gray-500 dark:text-gray-400 truncate mt-1">
                                      {account.auth_info_1}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </td>
                            <td className="px-2 py-2">
                              <StatusBadge
                                status={getStatusType(account.status)}
                                size="sm"
                              />
                              {!isExpanded && account.lastCheck && (
                                <div className="text-xs text-gray-500 dark:text-gray-400 truncate mt-1">
                                  {formatTime(account.lastCheck)}
                                </div>
                              )}
                            </td>
                            <td className="px-2 py-2">
                              {account.proxy ? (
                                <div>
                                  <StatusBadge
                                    status={getStatusType(account.proxyStatus || 'unknown')}
                                    size="sm"
                                    showIcon={false}
                                  />
                                  {!isExpanded && (
                                    <div className="text-xs text-gray-500 dark:text-gray-400 truncate mt-1">
                                      {account.proxy.city_name}
                                    </div>
                                  )}
                                </div>
                              ) : (
                                <span className="text-xs text-gray-400">无</span>
                              )}
                            </td>
                            <td className="px-2 py-2 text-center">
                              <ActionButton
                                onClick={(e) => handleCheckHealth(e, account.account_id)}
                                loading={checkingAccounts.has(account.account_id)}
                                icon={<EyeIcon className="w-3 h-3" />}
                                size="xs"
                                variant="secondary"
                                className="w-full min-w-0"
                              >
                                <span className="">检测</span>
                              </ActionButton>
                            </td>
                          </motion.tr>

                          {/* 展开的详细信息行 */}
                          {isExpanded && (
                            <motion.tr
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              className="bg-gray-50 dark:bg-gray-800"
                            >
                              <td colSpan={4} className="px-4 py-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div className="space-y-3">
                                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">账户详情</h4>
                                    <div className="space-y-2">
                                      <div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">账户ID:</span>
                                        <CopyableCell content={account.account_id} maxWidth="max-w-full" className="text-xs font-mono" />
                                      </div>
                                      <div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">用户名:</span>
                                        <CopyableCell content={account.auth_info_1} maxWidth="max-w-full" className="text-xs" />
                                      </div>
                                      <div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">邮箱:</span>
                                        <CopyableCell content={account.auth_info_2} maxWidth="max-w-full" className="text-xs" />
                                      </div>
                                    </div>
                                  </div>

                                  <div className="space-y-3">
                                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">状态信息</h4>
                                    <div className="space-y-2">
                                      <div className="flex items-center space-x-2">
                                        <span className="text-xs text-gray-500 dark:text-gray-400">状态:</span>
                                        <StatusBadge status={getStatusType(account.status)} size="sm" />
                                      </div>
                                      {account.lastCheck && (
                                        <div>
                                          <span className="text-xs text-gray-500 dark:text-gray-400">最后检查:</span>
                                          <span className="ml-2 text-xs">{formatTime(account.lastCheck)}</span>
                                        </div>
                                      )}
                                      {account.error && (
                                        <div>
                                          <span className="text-xs text-gray-500 dark:text-gray-400">错误信息:</span>
                                          <div className="mt-1 text-xs text-red-500 dark:text-red-400 bg-red-50 dark:bg-red-900/20 p-2 rounded">
                                            {account.error}
                                          </div>
                                        </div>
                                      )}
                                      {account.proxy && (
                                        <div>
                                          <span className="text-xs text-gray-500 dark:text-gray-400">代理信息:</span>
                                          <div className="mt-1 space-y-1">
                                            <div className="flex items-center space-x-2">
                                              <StatusBadge status={getStatusType(account.proxyStatus || 'unknown')} size="sm" showIcon={false} />
                                              <span className="text-xs">{account.proxy.city_name}, {account.proxy.country_code}</span>
                                            </div>
                                            <CopyableCell content={account.proxy.proxy_url} maxWidth="max-w-full" className="text-xs font-mono" />
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </td>
                            </motion.tr>
                          )}
                        </React.Fragment>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </DataCard>
      )}

      {/* 分页组件 */}
      {totalPages > 1 && (
        <DataCard>
          <DataCardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  显示第 <span className="font-medium">{startIndex + 1}</span> 到{' '}
                  <span className="font-medium">{Math.min(endIndex, filteredAccounts.length)}</span> 条，
                  共 <span className="font-medium">{filteredAccounts.length}</span> 条记录
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <ActionButton
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  variant="ghost"
                  size="sm"
                >
                  上一页
                </ActionButton>
                <span className="px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg">
                  {currentPage} / {totalPages}
                </span>
                <ActionButton
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  variant="ghost"
                  size="sm"
                >
                  下一页
                </ActionButton>
              </div>
            </div>
          </DataCardContent>
        </DataCard>
      )}
    </div>
  );
}