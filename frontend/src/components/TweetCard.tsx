'use client';

import { Tweet } from '@/types/tweet';
import { formatDistanceToNow } from 'date-fns';
import Image from "next/image";
import MediaDisplay from './shared/MediaDisplay';
import TweetInteractions from './shared/TweetInteractions';

interface TweetCardProps {
  tweet: Tweet;
  index: number;
}

export default function TweetCard({ tweet, index }: TweetCardProps) {
  const tweetUrl = `https://twitter.com/${tweet.user.screen_name}/status/${tweet.id}`;
  const createdAt = new Date(tweet.created_at);
  const relativeTime = formatDistanceToNow(createdAt, { addSuffix: true });
  const fullTime = createdAt.toLocaleString();

  return (
    <div key={index} className="bg-white border border-gray-200 rounded-lg overflow-hidden transition-shadow duration-200 hover:shadow-md w-full max-w-2xl mx-auto mb-4">
      {/* 用户信息 */}
      <div className="flex items-center gap-3 px-4 pt-4 pb-2">
        <div className="relative w-10 h-10 flex-shrink-0">
          <Image
            src={tweet.user.profile_image_url}
            alt={tweet.user.name}
            fill
            className="rounded-full object-cover border border-gray-200"
          />
        </div>
        <div className="flex flex-col min-w-0">
          <span className="font-bold text-gray-900 text-base leading-tight hover:text-blue-600 cursor-pointer transition-colors">{tweet.user.name}</span>
          <span className="text-xs text-gray-500 leading-tight hover:text-blue-500 cursor-pointer transition-colors">@{tweet.user.screen_name}</span>
        </div>
        <span className="ml-auto text-xs text-gray-400" title={fullTime}>{relativeTime}</span>
        <button className="ml-2 p-1 rounded-full hover:bg-gray-100 transition-colors"><svg width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><circle cx="5" cy="12" r="2"/><circle cx="12" cy="12" r="2"/><circle cx="19" cy="12" r="2"/></svg></button>
      </div>

      {/* 推文内容 */}
      <div className="px-4 pb-2 text-gray-900 text-[15px] leading-relaxed whitespace-pre-wrap">
        {tweet.text}
      </div>

      {/* 媒体内容 */}
      {tweet.media && tweet.media.length > 0 && (
        <div className="px-4 pb-2">
          <MediaDisplay
            media={tweet.media}
            className="w-full"
            maxHeight={600}
            maxWidth="100%"
            features={{
              enableStreamSelection: false,
              enableDownload: false
            }}
          />
        </div>
      )}

      {/* 互动数据 */}
      <div className="px-4 pt-2 pb-3 border-t border-gray-100 flex items-center gap-6">
        <TweetInteractions
          replyCount={tweet.reply_count}
          retweetCount={tweet.retweet_count}
          favoriteCount={tweet.favorite_count}
          onReply={() => window.open(tweetUrl, '_blank')}
          onRetweet={() => window.open(`https://twitter.com/intent/retweet?tweet_id=${tweet.id}`, '_blank')}
          onFavorite={() => window.open(`https://twitter.com/intent/like?tweet_id=${tweet.id}`, '_blank')}
          tweetUrl={tweetUrl}
        />
      </div>
    </div>
  );
}
