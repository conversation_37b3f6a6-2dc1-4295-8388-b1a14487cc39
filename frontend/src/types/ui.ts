/**
 * UI组件相关类型定义
 */

import { ReactNode, HTMLAttributes, ButtonHTMLAttributes } from 'react';
import { ComponentSize, ComponentVariant } from './common';

/**
 * 基础组件属性接口
 */
export interface BaseComponentProps {
  className?: string;
  children?: ReactNode;
  id?: string;
  'data-testid'?: string;
}

/**
 * 按钮组件属性接口
 */
export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement>, BaseComponentProps {
  variant?: ComponentVariant;
  size?: ComponentSize;
  loading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  icon?: ReactNode;
  iconPosition?: 'left' | 'right';
}

/**
 * 卡片组件属性接口
 */
export interface CardProps extends HTMLAttributes<HTMLDivElement>, BaseComponentProps {
  variant?: 'default' | 'outlined' | 'elevated';
  padding?: ComponentSize;
  hoverable?: boolean;
  clickable?: boolean;
}

/**
 * 输入框组件属性接口
 */
export interface InputProps extends Omit<HTMLAttributes<HTMLInputElement>, 'size'>, BaseComponentProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
  size?: ComponentSize;
  variant?: 'default' | 'outlined' | 'filled';
  label?: string;
  placeholder?: string;
  error?: string;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  startIcon?: ReactNode;
  endIcon?: ReactNode;
}

/**
 * 模态框组件属性接口
 */
export interface ModalProps extends BaseComponentProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  size?: ComponentSize;
  closable?: boolean;
  maskClosable?: boolean;
  footer?: ReactNode;
  centered?: boolean;
}

/**
 * 加载组件属性接口
 */
export interface LoadingProps extends BaseComponentProps {
  size?: ComponentSize;
  variant?: 'spinner' | 'dots' | 'pulse';
  text?: string;
  overlay?: boolean;
}

/**
 * 提示组件属性接口
 */
export interface TooltipProps extends BaseComponentProps {
  content: ReactNode;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  trigger?: 'hover' | 'click' | 'focus';
  delay?: number;
  arrow?: boolean;
}

/**
 * 标签组件属性接口
 */
export interface TagProps extends BaseComponentProps {
  variant?: ComponentVariant;
  size?: ComponentSize;
  closable?: boolean;
  onClose?: () => void;
  icon?: ReactNode;
}

/**
 * 头像组件属性接口
 */
export interface AvatarProps extends BaseComponentProps {
  src?: string;
  alt?: string;
  size?: ComponentSize | number;
  shape?: 'circle' | 'square';
  fallback?: ReactNode;
  online?: boolean;
}

/**
 * 分页组件属性接口
 */
export interface PaginationProps extends BaseComponentProps {
  current: number;
  total: number;
  pageSize: number;
  onChange: (page: number, pageSize: number) => void;
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  showTotal?: (total: number, range: [number, number]) => ReactNode;
  size?: ComponentSize;
}

/**
 * 表格列定义接口
 */
export interface TableColumn<T = unknown> {
  key: string;
  title: ReactNode;
  dataIndex?: keyof T;
  width?: number | string;
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: unknown, record: T, index: number) => ReactNode;
  className?: string;
}

/**
 * 表格组件属性接口
 */
export interface TableProps<T = unknown> extends BaseComponentProps {
  columns: TableColumn<T>[];
  dataSource: T[];
  loading?: boolean;
  pagination?: false | PaginationProps;
  rowKey?: string | ((record: T) => string);
  onRow?: (record: T, index: number) => HTMLAttributes<HTMLTableRowElement>;
  scroll?: { x?: number | string; y?: number | string };
  size?: ComponentSize;
}

/**
 * 导航菜单项接口
 */
export interface MenuItem {
  key: string;
  label: ReactNode;
  icon?: ReactNode;
  path?: string;
  children?: MenuItem[];
  disabled?: boolean;
  badge?: string | number;
}

/**
 * 导航组件属性接口
 */
export interface NavigationProps extends BaseComponentProps {
  items: MenuItem[];
  mode?: 'horizontal' | 'vertical';
  theme?: 'light' | 'dark';
  collapsed?: boolean;
  selectedKeys?: string[];
  onSelect?: (key: string) => void;
}

/**
 * 面包屑项接口
 */
export interface BreadcrumbItem {
  title: ReactNode;
  path?: string;
  icon?: ReactNode;
}

/**
 * 面包屑组件属性接口
 */
export interface BreadcrumbProps extends BaseComponentProps {
  items: BreadcrumbItem[];
  separator?: ReactNode;
}

/**
 * 空状态组件属性接口
 */
export interface EmptyProps extends BaseComponentProps {
  image?: ReactNode;
  title?: ReactNode;
  description?: ReactNode;
  action?: ReactNode;
  size?: ComponentSize;
}

/**
 * 错误边界组件属性接口
 */
export interface ErrorBoundaryProps extends BaseComponentProps {
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

/**
 * 响应式断点类型
 */
export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

/**
 * 响应式值类型
 */
export type ResponsiveValue<T> = T | Partial<Record<Breakpoint, T>>;

/**
 * 间距类型
 */
export type Spacing = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12 | 16 | 20 | 24 | 32 | 40 | 48 | 56 | 64;

/**
 * 颜色类型
 */
export type Color = 
  | 'primary' 
  | 'secondary' 
  | 'success' 
  | 'warning' 
  | 'error' 
  | 'info' 
  | 'gray' 
  | 'white' 
  | 'black';

/**
 * 阴影类型
 */
export type Shadow = 'none' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

/**
 * 圆角类型
 */
export type BorderRadius = 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
