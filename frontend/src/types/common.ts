/**
 * 通用类型定义文件
 * 包含项目中常用的通用类型和工具类型
 */

/**
 * API响应状态
 */
export type ApiStatus = 'idle' | 'loading' | 'success' | 'error';

/**
 * 通用API响应接口
 */
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp?: string;
}

/**
 * 分页参数接口
 */
export interface PaginationParams {
  page: number;
  limit: number;
  offset?: number;
}

/**
 * 分页响应接口
 */
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * 加载状态接口
 */
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
  lastUpdated?: string;
}

/**
 * 组件尺寸类型
 */
export type ComponentSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

/**
 * 组件变体类型
 */
export type ComponentVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'ghost';

/**
 * 主题模式类型
 */
export type ThemeMode = 'light' | 'dark' | 'system';

/**
 * 设备类型
 */
export type DeviceType = 'mobile' | 'tablet' | 'desktop';

/**
 * 排序方向
 */
export type SortDirection = 'asc' | 'desc';

/**
 * 排序参数接口
 */
export interface SortParams {
  field: string;
  direction: SortDirection;
}

/**
 * 过滤参数接口
 */
export interface FilterParams {
  [key: string]: string | number | boolean | string[] | number[] | undefined;
}

/**
 * 搜索参数接口
 */
export interface SearchParams extends PaginationParams {
  query?: string;
  sort?: SortParams;
  filters?: FilterParams;
}

/**
 * 工具类型：使所有属性可选
 */
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * 工具类型：使指定属性必需
 */
export type RequiredBy<T, K extends keyof T> = T & Required<Pick<T, K>>;

/**
 * 工具类型：深度可选
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * 工具类型：深度必需
 */
export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

/**
 * 工具类型：提取数组元素类型
 */
export type ArrayElement<T> = T extends (infer U)[] ? U : never;

/**
 * 工具类型：提取Promise返回类型
 */
export type PromiseType<T> = T extends Promise<infer U> ? U : never;

/**
 * 工具类型：函数参数类型
 */
export type FunctionParams<T> = T extends (...args: infer P) => unknown ? P : never;

/**
 * 工具类型：函数返回类型
 */
export type FunctionReturn<T> = T extends (...args: unknown[]) => infer R ? R : never;
