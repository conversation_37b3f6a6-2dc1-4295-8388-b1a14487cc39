/**
 * 推文用户信息接口
 */
export interface TweetUser {
  id: string;
  name: string;
  screen_name: string;
  followers_count: number;
  following_count: number;
  statuses_count: number;
  profile_image_url: string;
  profile_banner_url?: string;
  description: string;
}

/**
 * 媒体流信息接口
 */
export interface MediaStream {
  url: string;
  bitrate: number;
  content_type: string;
  [prop: string]: unknown;
}

/**
 * 视频信息接口
 */
export interface VideoInfo {
  duration_millis: number;
  aspect_ratio: [number, number];
}

/**
 * 媒体类型枚举
 */
export type MediaType = 'photo' | 'video' | 'animated_gif';

/**
 * 推文媒体信息接口
 */
export interface TweetMedia {
  type: MediaType;
  url: string;
  display_url: string;
  media_url: string;
  expanded_url: string;
  original_url?: string;
  height?: number;
  width?: number;
  video_info?: VideoInfo;
  streams?: MediaStream[];
}

/**
 * 时间周期类型
 */
export type TimePeriod = 'day' | 'week' | 'month';

/**
 * 推文基础接口
 */
export interface BaseTweet {
  id: string;
  text: string;
  created_at: string;
  favorite_count: number;
  retweet_count: number;
  reply_count: number;
  quote_count: number;
  language: string;
}

/**
 * 完整推文信息接口
 */
export interface Tweet extends BaseTweet {
  user: TweetUser;
  media: TweetMedia[];
}

/**
 * 热门推文数据接口
 */
export interface HotTweetsData {
  period: TimePeriod;
  timestamp: string;
  tweets: Tweet[];
}

/**
 * API返回的推文数据接口（保持向后兼容）
 * @deprecated 建议使用 Tweet 接口
 */
export interface TweetData extends BaseTweet {
  user: TweetUser;
  media: TweetMedia[];
}

/**
 * 简化的媒体接口（用于特定组件）
 */
export interface SimpleMedia {
  type: MediaType;
  media_url: string;
  display_url?: string;
  streams?: Pick<MediaStream, 'url' | 'bitrate'>[];
}