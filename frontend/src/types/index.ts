/**
 * 类型定义统一导出文件
 * 提供项目中所有类型的统一入口
 */

// 推文相关类型
export type {
  TweetUser,
  MediaStream,
  VideoInfo,
  MediaType,
  TweetMedia,
  TimePeriod,
  BaseTweet,
  Tweet,
  HotTweetsData,
  TweetData,
  SimpleMedia,
} from './tweet';

// 通用类型
export type {
  ApiStatus,
  ApiResponse,
  PaginationParams,
  PaginatedResponse,
  LoadingState,
  ComponentSize,
  ComponentVariant,
  ThemeMode,
  DeviceType,
  SortDirection,
  SortParams,
  FilterParams,
  SearchParams,
  PartialBy,
  RequiredBy,
  DeepPartial,
  DeepRequired,
  ArrayElement,
  PromiseType,
  FunctionParams,
  FunctionReturn,
} from './common';

// UI组件类型
export type {
  BaseComponentProps,
  ButtonProps,
  CardProps,
  InputProps,
  ModalProps,
  LoadingProps,
  TooltipProps,
  TagProps,
  AvatarProps,
  PaginationProps,
  TableColumn,
  TableProps,
  MenuItem,
  NavigationProps,
  BreadcrumbItem,
  BreadcrumbProps,
  EmptyProps,
  ErrorBoundaryProps,
  Breakpoint,
  ResponsiveValue,
  Spacing,
  Color,
  Shadow,
  BorderRadius,
} from './ui';

// 重新导出常用类型的别名
export type { Tweet as TweetType } from './tweet';
export type { ApiResponse as APIResponse } from './common';
export type { ButtonProps as ButtonComponentProps } from './ui';

/**
 * 项目特定的复合类型
 */

// 账户相关类型
export interface Account {
  account_id: string;
  auth_info_1: string;
  auth_info_2: string;
  status: 'active' | 'inactive' | 'banned' | 'suspended';
  lastCheck: string;
  proxyStatus?: 'connected' | 'disconnected' | 'error';
  error?: string;
  proxy?: {
    id: string;
    proxy_url: string;
    country_code: string;
    city_name: string;
  };
}

// 代理相关类型
export interface Proxy {
  id: string;
  username: string;
  proxy_address: string;
  port: number;
  status: 'active' | 'inactive' | 'error';
  lastCheck: string;
  responseTime?: number;
  country_code: string;
  city_name: string;
}

// 健康检查相关类型
export interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  responseTime?: number;
  error?: string;
  details?: Record<string, unknown>;
}

// 监控数据类型
export interface MonitoringData {
  accounts: Account[];
  proxies: Proxy[];
  healthChecks: HealthCheckResult[];
  lastUpdated: string;
}

// 分析结果类型
export interface AnalysisResult {
  tweetId: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  keywords: string[];
  topics: string[];
  engagement: {
    score: number;
    prediction: 'high' | 'medium' | 'low';
  };
  metadata: {
    language: string;
    wordCount: number;
    hashtagCount: number;
    mentionCount: number;
    urlCount: number;
  };
}

// 应用配置类型
export interface AppConfig {
  api: {
    baseUrl: string;
    timeout: number;
    retryAttempts: number;
  };
  ui: {
    theme: ThemeMode;
    language: string;
    pageSize: number;
  };
  features: {
    enableAnalytics: boolean;
    enableNotifications: boolean;
    enableAutoRefresh: boolean;
  };
}

// 用户偏好设置类型
export interface UserPreferences {
  theme: ThemeMode;
  language: string;
  notifications: {
    email: boolean;
    push: boolean;
    inApp: boolean;
  };
  dashboard: {
    refreshInterval: number;
    defaultView: 'grid' | 'list';
    showPreview: boolean;
  };
}

// 错误类型
export interface AppError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  timestamp: string;
  stack?: string;
}

// 导航路由类型
export interface Route {
  path: string;
  name: string;
  component: string;
  meta?: {
    title?: string;
    description?: string;
    requiresAuth?: boolean;
    roles?: string[];
  };
}

// 表单验证类型
export interface ValidationRule {
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: RegExp;
  message: string;
  validator?: (value: unknown) => boolean | Promise<boolean>;
}

export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'checkbox' | 'radio';
  placeholder?: string;
  defaultValue?: unknown;
  options?: Array<{ label: string; value: unknown }>;
  rules?: ValidationRule[];
  disabled?: boolean;
  hidden?: boolean;
}

// 事件类型
export interface AppEvent {
  type: string;
  payload?: Record<string, unknown>;
  timestamp: string;
  source?: string;
}

// 通知类型
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
}
