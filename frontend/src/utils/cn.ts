/**
 * 类名合并工具函数
 * 用于合并和处理 Tailwind CSS 类名
 */

type ClassValue =
  | string
  | number
  | boolean
  | undefined
  | null
  | { [key: string]: boolean | undefined | null }
  | ClassValue[];

/**
 * 合并类名的工具函数
 * 支持条件类名和重复类名去除
 *
 * @param inputs - 类名输入，支持字符串、对象、数组等多种格式
 * @returns 合并后的类名字符串
 *
 * @example
 * cn('px-2 py-1', 'text-sm', { 'bg-blue-500': isActive })
 * cn('px-2', condition && 'py-1', ['text-sm', 'rounded'])
 */
export function cn(...inputs: ClassValue[]): string {
  const classes: string[] = [];

  for (const input of inputs) {
    if (!input) continue;

    if (typeof input === 'string') {
      classes.push(input);
    } else if (typeof input === 'number') {
      classes.push(String(input));
    } else if (Array.isArray(input)) {
      const nested = cn(...input);
      if (nested) classes.push(nested);
    } else if (typeof input === 'object') {
      for (const [key, value] of Object.entries(input)) {
        if (value) classes.push(key);
      }
    }
  }

  return classes.join(' ');
}

/**
 * 创建变体类名的工具函数
 * 用于根据变体和尺寸生成对应的类名
 * 
 * @param base - 基础类名
 * @param variants - 变体映射对象
 * @param variant - 当前变体
 * @param sizes - 尺寸映射对象
 * @param size - 当前尺寸
 * @param className - 额外的类名
 * @returns 合并后的类名字符串
 */
export function createVariantClasses<
  V extends string,
  S extends string
>(
  base: string,
  variants: Record<V, string>,
  variant: V,
  sizes?: Record<S, string>,
  size?: S,
  className?: string
): string {
  return cn(
    base,
    variants[variant],
    sizes && size && sizes[size],
    className
  );
}

/**
 * 响应式类名生成器
 * 根据断点生成响应式类名
 * 
 * @param value - 响应式值对象或单一值
 * @param prefix - 类名前缀
 * @returns 响应式类名字符串
 * 
 * @example
 * responsive({ xs: 'block', md: 'flex' }, 'display')
 * // 返回: 'block md:flex'
 */
export function responsive<T extends string>(
  value: T | Partial<Record<'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl', T>>,
  prefix?: string
): string {
  if (typeof value === 'string') {
    return prefix ? `${prefix}-${value}` : value;
  }

  const breakpoints = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'] as const;
  const classes: string[] = [];

  breakpoints.forEach((breakpoint) => {
    const breakpointValue = value[breakpoint];
    if (breakpointValue) {
      const className = prefix ? `${prefix}-${breakpointValue}` : breakpointValue;
      if (breakpoint === 'xs') {
        classes.push(className);
      } else {
        classes.push(`${breakpoint}:${className}`);
      }
    }
  });

  return classes.join(' ');
}

/**
 * 条件类名生成器
 * 根据条件生成类名
 * 
 * @param condition - 条件
 * @param trueClass - 条件为真时的类名
 * @param falseClass - 条件为假时的类名
 * @returns 条件类名字符串
 */
export function conditional(
  condition: boolean,
  trueClass: string,
  falseClass?: string
): string {
  return condition ? trueClass : falseClass || '';
}

/**
 * 状态类名生成器
 * 根据组件状态生成对应的类名
 * 
 * @param states - 状态对象
 * @returns 状态类名字符串
 * 
 * @example
 * stateClasses({
 *   hover: 'hover:bg-blue-500',
 *   focus: 'focus:ring-2',
 *   disabled: 'disabled:opacity-50'
 * })
 */
export function stateClasses(states: Record<string, string>): string {
  return Object.values(states).join(' ');
}

/**
 * 主题类名生成器
 * 根据主题模式生成对应的类名
 * 
 * @param lightClass - 浅色主题类名
 * @param darkClass - 深色主题类名
 * @returns 主题类名字符串
 */
export function themeClasses(lightClass: string, darkClass: string): string {
  return `${lightClass} dark:${darkClass}`;
}

/**
 * 动画类名生成器
 * 生成常用的动画类名
 * 
 * @param animation - 动画类型
 * @param duration - 动画持续时间
 * @param delay - 动画延迟时间
 * @returns 动画类名字符串
 */
export function animationClasses(
  animation: 'fade' | 'slide' | 'scale' | 'bounce' | 'spin',
  duration: 'fast' | 'normal' | 'slow' = 'normal',
  delay?: 'short' | 'medium' | 'long'
): string {
  const animations = {
    fade: 'transition-opacity',
    slide: 'transition-transform',
    scale: 'transition-transform',
    bounce: 'animate-bounce',
    spin: 'animate-spin',
  };

  const durations = {
    fast: 'duration-150',
    normal: 'duration-300',
    slow: 'duration-500',
  };

  const delays = {
    short: 'delay-75',
    medium: 'delay-150',
    long: 'delay-300',
  };

  return cn(
    animations[animation],
    durations[duration],
    delay && delays[delay]
  );
}

/**
 * 间距类名生成器
 * 生成统一的间距类名
 * 
 * @param type - 间距类型
 * @param value - 间距值
 * @param direction - 间距方向
 * @returns 间距类名字符串
 */
export function spacingClasses(
  type: 'p' | 'm',
  value: number | string,
  direction?: 't' | 'r' | 'b' | 'l' | 'x' | 'y'
): string {
  const prefix = direction ? `${type}${direction}` : type;
  return `${prefix}-${value}`;
}

/**
 * 网格类名生成器
 * 生成CSS Grid相关的类名
 * 
 * @param cols - 列数
 * @param rows - 行数
 * @param gap - 间隙
 * @returns 网格类名字符串
 */
export function gridClasses(
  cols?: number | 'auto' | 'none',
  rows?: number | 'auto' | 'none',
  gap?: number | string
): string {
  const classes: string[] = ['grid'];

  if (cols) {
    classes.push(`grid-cols-${cols}`);
  }

  if (rows) {
    classes.push(`grid-rows-${rows}`);
  }

  if (gap) {
    classes.push(`gap-${gap}`);
  }

  return classes.join(' ');
}

/**
 * Flexbox类名生成器
 * 生成Flexbox相关的类名
 * 
 * @param direction - flex方向
 * @param justify - 主轴对齐
 * @param align - 交叉轴对齐
 * @param wrap - 是否换行
 * @param gap - 间隙
 * @returns Flexbox类名字符串
 */
export function flexClasses(
  direction?: 'row' | 'col' | 'row-reverse' | 'col-reverse',
  justify?: 'start' | 'end' | 'center' | 'between' | 'around' | 'evenly',
  align?: 'start' | 'end' | 'center' | 'baseline' | 'stretch',
  wrap?: boolean,
  gap?: number | string
): string {
  const classes: string[] = ['flex'];

  if (direction) {
    classes.push(`flex-${direction}`);
  }

  if (justify) {
    classes.push(`justify-${justify}`);
  }

  if (align) {
    classes.push(`items-${align}`);
  }

  if (wrap) {
    classes.push('flex-wrap');
  }

  if (gap) {
    classes.push(`gap-${gap}`);
  }

  return classes.join(' ');
}
