# Twitter Hot Tweets Viewer

一个优雅的 Twitter 热门推文查看器，采用 Apple 风格设计，使用 Next.js 13+ 和 Tailwind CSS 构建。

## 功能特点

- 🎨 优雅的 Apple 风格 UI 设计
- 📱 完全响应式布局
- ⚡ 实时数据更新
- 🌓 支持深色模式
- 🎭 流畅的动画效果
- 📊 多时间范围支持（今日、本周、本月）

## 技术栈

- Next.js 13+ (App Router)
- TypeScript
- Tailwind CSS
- Framer Motion
- Heroicons
- React Intersection Observer

## 快速开始

1. 安装依赖：
```bash
npm install
```

2. 启动开发服务器：
```bash
npm run dev
```

3. 在浏览器中访问 [http://localhost:3000](http://localhost:3000)

## 项目结构

```
frontend/
├── src/
│   ├── app/                 # Next.js 应用路由
│   │   ├── api/            # API 路由
│   │   └── page.tsx        # 主页面
│   ├── components/         # React 组件
│   │   ├── ui/            # 基础 UI 组件
│   │   └── TweetCard.tsx  # 推文卡片组件
│   └── types/             # TypeScript 类型定义
├── public/                # 静态资源
└── package.json          # 项目配置
```

## 开发指南

### 添加新组件

1. 在 `src/components` 目录下创建新组件
2. 使用 TypeScript 定义组件接口
3. 使用 Tailwind CSS 进行样式设计
4. 添加必要的动画效果

### 样式指南

- 使用 Tailwind CSS 类名进行样式设计
- 遵循 Apple 设计语言
- 保持组件样式的一致性
- 使用预定义的动画类

### 性能优化

- 使用 React.memo 优化组件重渲染
- 实现图片懒加载
- 使用 Intersection Observer 优化列表渲染
- 保持组件体积小巧

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT
