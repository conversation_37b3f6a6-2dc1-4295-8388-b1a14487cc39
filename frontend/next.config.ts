import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  images: {
    domains: ['pbs.twimg.com', 't.co', 'pic.x.com','pic.twitter.com'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 't.co',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'pbs.twimg.com',
        pathname: '/**',
      },
    ],
  },
};

export default nextConfig;
