# Twx

![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Python](https://img.shields.io/badge/python-3.8+-blue.svg)
![FastAPI](https://img.shields.io/badge/FastAPI-0.115.11-green.svg)

智能 Twitter 监控分析平台，支持多账户管理、实时监控、媒体处理和数据分析。

## 核心特性

- **智能账户管理**：多账户并行，健康度监控，智能路由
- **实时监控**：用户资料、推文内容、关注关系监控
- **数据分析**：推文解析、媒体下载、情感分析、数据导出
- **代理管理**：自动检测、轮换，支持 Webshare 等服务
- **多数据库支持**：SQLite、PostgreSQL、JSON 存储

## 快速开始

### 环境要求
- Python 3.8+
- pip

### 安装步骤

1. **克隆仓库**
```bash
git clone https://github.com/your-username/Twx.git
cd Twx
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置数据库**
```bash
# 创建必要目录
mkdir -p data/accounts data/cookies data/sqlite data/json logs

# 配置数据库类型（选择其一）
echo "DATABASE_TYPE=sqlite" >> .env  # 推荐新手
echo "DATABASE_TYPE=postgresql" >> .env  # 生产环境
echo "DATABASE_TYPE=json" >> .env  # 备份/导出
```

4. **启动服务**
```bash
python main.py
```

5. **访问服务**
- API 服务：http://localhost:8000
- API 文档：http://localhost:8000/docs
- 健康检查：http://localhost:8000/health

## API 使用示例

### 导入账户
```bash
curl -X POST "http://localhost:8000/api/accounts/import_one" \
  -H "Content-Type: application/json" \
  -d '{
    "account_id": "your_account_id",
    "auth_info_1": "your_username",
    "auth_info_2": "your_email",
    "password": "your_password"
  }'
```

### 解析推文
```bash
curl -X POST "http://localhost:8000/api/tweet/parse" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://twitter.com/username/status/**********",
    "priority": "normal"
  }'
```

### 获取用户信息
```bash
curl "http://localhost:8000/api/users/twitter?priority=normal"
```

### 启动用户监控
```bash
curl -X POST "http://localhost:8000/api/monitor/user/twitter" \
  -H "Content-Type: application/json" \
  -d '{
    "interval": 300,
    "notify_url": "http://your-callback-url",
    "monitor_profile": true,
    "monitor_tweets": true,
    "priority": "normal"
  }'
```

完整 API 文档请查看：[docs/API.md](docs/API.md)

## 许可证

MIT License