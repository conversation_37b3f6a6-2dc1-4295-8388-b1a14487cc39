这是我的一个基于 twikit 开发的推特数据抓取、分析的项目。源码在 src, 我觉得项目具体实现稍微有点复杂了不够简洁,请仔细阅读源码,然后分析现有代码,看下有什么可以精简,优化的方面. 请将文档输出到 docs/claude.simple.md, 尽可能详细。

--------------------------------------------------------------------------------------------------------

当初我开发这个项目的目的是：
- 利用 twikit 库功能，通过账号，密码，邮箱，OTOP，等信息登录 twitter，拿到登录后的 cookie，相当于拿到了请求 API 的权证
- 批量利用账户信息登录多个 twitter 账户，拿到他们的权证（cookie）
- 每个账户配置一个代理，从而避免被推特官方发现是机器人批量操作。**代理有可能会失效，所以需要给配置代理的账户轮换一个新的（同地理位置、国家）的代理。**
- 账户也有可能失效，需要有效机制来检测、记录账户是否失效、失效情况。
- 通过批量权证，从而对外提供一些 API 服务，比如获取某人的所有 tweets、查看某条 tweet 的详细信息，查看某人的信息、或者搜索等

我觉得目前代码库有点复杂了，并且没有落实到我上述的核心目的点。
请结合以上目的，帮我分析现有代码库，结合项目源码分析。将分析输出到 docs/core.md

--------------------------------------------------------------------------------------------------------
理清思路：当前项目的主要流程，目标，初始化方法。

1. git 项目到本地  
  1.1 配置 FERNET_KEY 数据库加密方法  
  1.2 配置 WEBSHARE_API_KEY 获取 webshare 的代理(可选)
  1.3 配置 数据库相关逻辑(如果不配置，默认用 sqlite)
  1.4 配置 accounts.json 填写账户数据

2. conda or uv or pip 安装依赖 -r requirements.txt

3. 运行项目

```python
python main.py 启动后端服务
```

4. 同 cli.py 导入账户、代理信息

```python
python cli.py <import-accounts>
```

python cli.py  webshare
导入 webshare 代理列表 且 写入数据库

python applyProxy.py
给accounts.json分配代理,检测无效代理替换

----------------------------------------------------------------------------------------

@src/api/twitter_api_facade.py smart_execute 函数是所有调用的入口。请帮我阅读源码，然后分析问题。所有代码的改动都不应该原有功能。改动应该简洁。
1. 代码中有试错逻辑，如果某个账户请求失败了，且还在重试范围内，那么将切换账户,代理重试。目前失败账户有降权逻辑，但是没有持久化，下次请求可能还是先选的失败账户。请帮我解决这个问题。
2. 可以为smart_execute及其其他依赖函数、调用流程涉及函数 log 的地方加上更加人性化的友好的颜色展示。
