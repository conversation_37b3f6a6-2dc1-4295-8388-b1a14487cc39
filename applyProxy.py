import asyncio
from typing import Dict, Optional, List
from collections import defaultdict
from pathlib import Path

from src.utils.logger import setup_logger
from src.proxy.service import ProxyService, ProxyTestConfig, ProxyInfo

# 配置日志
logger = setup_logger(module_name="apply_proxy")

# 文件路径常量
ACCOUNTS_FILE = Path("accounts.json")
ACCOUNTS2PROXY_FILE = Path("accounts2proxy.json")

# 代理检测配置
CONCURRENCY_LIMIT = 5
def convert_raw_proxy_to_info(proxy: Dict) -> ProxyInfo:
    """将原始代理字典转换为ProxyInfo对象"""
    proxy_url = (f"http://{proxy['username']}:{proxy['password']}@"
                f"{proxy['proxy_address']}:{proxy['port']}")
    
    return ProxyInfo(
        id=str(proxy['id']),
        proxy_url=proxy_url,
        ip=proxy['proxy_address'],
        port=proxy['port'],
        username=proxy['username'],
        password=proxy['password'],
        country_code=proxy.get('country_code', ''),
        city_name=proxy.get('city_name', ''),
        asn_name=proxy.get('asn_name', ''),
        asn_number=proxy.get('asn_number', 0),
        state=proxy.get('state', ''),
        is_valid=proxy.get('valid', True),
        raw_data=proxy
    )

def assign_proxy(
    account: Dict,
    valid_proxies: List[ProxyInfo],
    used_proxy_ids: set,
    proxy_usage_count: Dict[str, int],
    max_reuse: int
) -> Optional[ProxyInfo]:
    """为账户分配可用代理
    
    Args:
        account: 账户信息
        valid_proxies: 已验证的有效代理列表
        used_proxy_ids: 已使用的代理ID集合
        proxy_usage_count: 代理使用计数
        max_reuse: 代理最大复用次数（0表示不复用）
    """
    current_proxy_id = account.get("proxy", {}).get("id") if account.get("proxy") is not None else None
    
    if current_proxy_id:
        # 检查现有代理是否在有效代理列表中
        current_proxy = next((p for p in valid_proxies if p.id == current_proxy_id), None)
        if current_proxy:
            logger.debug(f"Account {account['account_id']} keeps existing proxy {current_proxy_id}")
            return current_proxy
        logger.info(f"Existing proxy {current_proxy_id} for {account['account_id']} is invalid")

    # 首先尝试未使用的代理
    unused_proxies = [p for p in valid_proxies if p.id not in used_proxy_ids]
    if unused_proxies:
        selected_proxy = unused_proxies[0]  # 选择第一个可用的
        logger.info(f"Assigned new proxy {selected_proxy.id} to {account['account_id']}")
        return selected_proxy

    # 如果允许复用且没有未使用的代理，尝试复用现有代理
    if max_reuse > 0:
        reusable_proxies = [
            p for p in valid_proxies 
            if p.id in used_proxy_ids 
            and proxy_usage_count[p.id] < max_reuse
        ]
        if reusable_proxies:
            selected_proxy = reusable_proxies[0]
            logger.info(f"Reused proxy {selected_proxy.id} for {account['account_id']} (usage: {proxy_usage_count[selected_proxy.id] + 1})")
            return selected_proxy

    logger.debug(f"No valid proxies found for {account['account_id']}")
    raise ValueError(f"No valid proxies for account {account['account_id']}")
error_count = 0
async def process_account(
    account: Dict,
    valid_proxies: List[ProxyInfo],
    used_proxy_ids: set,
    proxy_usage_count: Dict[str, int],
    max_reuse: int,
    semaphore: asyncio.Semaphore,
    shared_lock: asyncio.Lock
) -> None:
    """处理单个账户的代理分配"""
    no_proxy_err = "No valid proxies for account"
    async with semaphore:
        try:
            async with shared_lock:
                proxy = assign_proxy(account, valid_proxies, used_proxy_ids, proxy_usage_count, max_reuse)
                if proxy:
                    account["proxy"] = {
                        "id": proxy.id,
                        "proxy_url": proxy.proxy_url,
                        "country_code": proxy.country_code,
                        "city_name": proxy.city_name,
                        "asn_name": proxy.asn_name,
                        "asn_number": proxy.asn_number,
                    }
                    used_proxy_ids.add(proxy.id)
                    proxy_usage_count[proxy.id] += 1
                else:
                    logger.error(f"No valid proxies found for {account['account_id']}")
        except ValueError as e:
            if no_proxy_err in str(e):
                global error_count
                error_count = error_count + 1
                if error_count <= 1:
                    logger.error(str(e))
            else:
                logger.error(str(e))

async def main():
    """主函数"""
    # 一个代理对应一个账户，代理失效则替换**最佳**代理
    max_reuse = 0
    if max_reuse > 0:
        logger.info(f"Proxy reuse enabled, max reuse per proxy: {max_reuse}")
    
    # 初始化代理服务
    test_config = ProxyTestConfig(concurrency_limit=CONCURRENCY_LIMIT)
    proxy_service = ProxyService(test_config=test_config)
    
    # 加载数据（一次性加载并缓存）
    accounts = proxy_service.load_json(ACCOUNTS_FILE)
    
    # 预加载并测试所有代理的有效性
    logger.info("预加载并测试代理有效性...")
    _all_proxies = await proxy_service.load_all_available_proxies()
    all_proxies = [p for p in _all_proxies if p.country_code == 'US']
    not_us_proxies = [p for p in _all_proxies if p.country_code != 'US']
    logger.info(f"已加载 {len(all_proxies)} 个代理({len(not_us_proxies)}个非US代理)，开始批量测试...")
    
    # 批量测试代理有效性（并发）
    valid_proxies = await proxy_service.batch_test_proxies(all_proxies, concurrency_limit=CONCURRENCY_LIMIT)
    logger.info(f"测试完成，有效代理数量: {len(valid_proxies)}")
    
    # 初始化代理使用计数
    proxy_usage_count = defaultdict(int)
    used_proxy_ids = set()
    for account in accounts:
        if account.get("proxy"):
            proxy_id = account["proxy"]["id"]
            used_proxy_ids.add(proxy_id)

    # 创建并发限制和共享锁
    semaphore = asyncio.Semaphore(CONCURRENCY_LIMIT)
    shared_lock = asyncio.Lock()
    tasks = [
        process_account(
            account, valid_proxies, used_proxy_ids, 
            proxy_usage_count, max_reuse, semaphore, shared_lock
        )
        for account in accounts
    ]
    await asyncio.gather(*tasks)

    # 保存更新后的账户数据
    proxy_service.save_json(ACCOUNTS_FILE, accounts)
    
    # 使用ProxyService创建并保存账户到代理的映射
    proxy_service.update_accounts2proxy_json(accounts)
    
    # 打印代理使用统计
    if max_reuse > 0:
        logger.info("\nProxy usage statistics:")
        for proxy_id, count in proxy_usage_count.items():
            logger.info(f"Proxy {proxy_id}: used by {count} accounts")

if __name__ == "__main__":
    asyncio.run(main())