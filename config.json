{"accounts": [], "accountsFile": "./accounts.json", "proxy_pool": [], "database": {"type": "sqlite", "sqlite": {"path": "data/sqlite/twx.db"}, "postgresql": {"host": "localhost", "port": 5432, "database": "twx", "username": "postgres", "password": "", "pool_size": 10, "max_overflow": 20}}, "json_storage": {"enabled": true, "path": "data/json", "backup_enabled": true}, "backup": {"path": "data/backups/"}}