[{"id": "1950094761819590929", "text": "🚨 Top AI Trends This Week:\n\nMeta's Llama 3.1 shows major accuracy gains\n\nElon Musk slams ChatGPT's memory as “surveillance”\n\nAmazon's Project Nile aims to redefine generative AI\n\n🔗 At DecentralGPT, you can test multiple LLMs in one place – free, fast, decentralized.\n\nExplore https://t.co/PmfGcjnUEI", "created_at": "<PERSON><PERSON> 29 07:23:16 +0000 2025", "favorite_count": 221, "retweet_count": 220, "reply_count": 157, "quote_count": 0, "language": "en", "user": {"id": "1726080863979610112", "name": "DecentralGPT", "screen_name": "DecentralGPT", "description": "DecentralGPT is a decentralized AI large language model inference network. Telegram: https://t.co/l8VG8405Ov \nhttps://t.co/sig9VNtbfh", "followers_count": 142462, "following_count": 83, "statuses_count": 571, "profile_image_url": "https://pbs.twimg.com/profile_images/1792923773248675840/pV_eR6_h_normal.jpg", "profile_banner_url": "https://pbs.twimg.com/profile_banners/1726080863979610112/1752834520"}, "media": [{"type": "photo", "media_url": "https://pbs.twimg.com/media/GxAg1I3bUAA4BAs.jpg", "expanded_url": "https://x.com/DecentralGPT/status/1950094761819590929/photo/1", "original_url": {"height": 1069, "width": 1900, "focus_rects": [{"x": 0, "y": 5, "w": 1900, "h": 1064}, {"x": 0, "y": 0, "w": 1069, "h": 1069}, {"x": 0, "y": 0, "w": 938, "h": 1069}, {"x": 65, "y": 0, "w": 535, "h": 1069}, {"x": 0, "y": 0, "w": 1900, "h": 1069}]}, "url": "https://t.co/PmfGcjnUEI", "display_url": "pic.x.com/PmfGcjnUEI", "height": 1069, "width": 1900, "video_info": {}, "streams": []}]}]