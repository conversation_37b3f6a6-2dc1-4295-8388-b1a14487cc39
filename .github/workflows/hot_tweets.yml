name: Hot Tweets Collector

on:
  schedule:
    # 每2天运行一次，在 UTC 时间 00:00 运行
    - cron: '0 0 */2 * *'
  workflow_dispatch:  # 允许手动触发

jobs:
  collect-hot-tweets:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0  # 获取完整的 git 历史
    
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Create data directory
      run: |
        mkdir -p data/hot_tweets
        chmod -R 777 data/hot_tweets
    
    - name: Run hot tweets collector
      run: python examples/basic/get_hot_tweets.py --debug || true
    
    - name: Configure Git
      run: |
        git config --global user.name 'GitHub Action'
        git config --global user.email '<EMAIL>'
    
    - name: Check for changes
      id: git-check
      run: |
        git add data/hot_tweets/
        git status
        git diff --quiet && git diff --staged --quiet || git commit -m "更新: $(date '+%Y-%m-%d') 的数据"
    
    - name: Commit and push changes
      uses: ad-m/github-push-action@master
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        branch: ${{ github.ref }}
