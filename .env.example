# Twx 环境变量配置示例
# 复制此文件为 .env 并根据需要修改配置

# 加密密钥（必须是32位URL安全的base64编码字符串）
FERNET_KEY=your_fernet_key_here

# Webshare API密钥（可选）
WEBSHARE_API_KEY=your_webshare_api_key_here

# 重试配置
MAX_RETRIES=10
MAX_DELAY=10
MAX_SWITCHES=10
# ===== 数据库配置 =====
# 数据库类型选择 (sqlite, postgresql, json)
DATABASE_TYPE=sqlite

# PostgreSQL 配置方式1: 使用完整的 DATABASE_URL
# DATABASE_URL=postgresql://username:password@localhost:5432/twx

# PostgreSQL 配置方式2: 使用单独的环境变量
# POSTGRES_HOST=localhost
# POSTGRES_PORT=5432
# POSTGRES_DB=twx
# POSTGRES_USER=postgres
# POSTGRES_PASSWORD=your_password

# SQLite 配置
# SQLITE_PATH=data/sqlite/twx.db

# JSON 存储配置
# JSON_STORAGE_ENABLED=true
# JSON_STORAGE_PATH=data/json
# JSON_BACKUP_ENABLED=true