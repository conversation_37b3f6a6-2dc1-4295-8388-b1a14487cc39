version: '3.8'

services:
  twitter-monitor:
    build: .
    container_name: twitter-monitor
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./.env:/app/.env
      - ./config.json:/app/config.json
      - ./accounts.json:/app/accounts.json
    environment:
      - FERNET_KEY=${FERNET_KEY}
      - WEBSHARE_API_KEY=${WEBSHARE_API_KEY}