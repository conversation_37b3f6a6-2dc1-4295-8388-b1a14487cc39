import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
from src.api.helpers import (
    convert_tweet_to_dict,
    convert_user_to_dict,
    collect_user_state,
    detect_state_changes
)
from src.utils.logger import setup_logger

logger = setup_logger(module_name="test_crawler")

@pytest.fixture
def mock_tweet():
    """创建模拟的Tweet对象"""
    tweet = MagicMock()
    tweet.id = "1234567890"
    tweet.text = "这是一条测试推文"
    tweet.created_at = "2024-01-01T00:00:00Z"
    tweet.favorite_count = 10
    tweet.retweet_count = 5
    tweet.reply_count = 3
    tweet.user.id = "user123"
    tweet.entities = {"hashtags": [], "urls": []}
    return tweet

@pytest.fixture
def mock_user():
    """创建模拟的User对象"""
    user = MagicMock()
    user.id = "user123"
    user.screen_name = "testuser"
    user.name = "Test User"
    user.description = "这是一个测试用户"
    user.followers_count = 100
    user.following_count = 50
    user.created_at = "2023-01-01T00:00:00Z"
    user.location = "测试城市"
    user.url = "https://example.com"
    return user

@pytest.fixture
def mock_client():
    """创建模拟的Twitter客户端"""
    client = AsyncMock()
    return client

@pytest.fixture
def mock_config():
    """创建模拟的配置对象"""
    config = MagicMock()
    config.monitor_tweets = True
    config.monitor_relations = True
    config.monitor_mentions = True
    config.monitor_likes = True
    config.monitor_retweets = True
    return config

def test_convert_tweet_to_dict(mock_tweet):
    """测试推文转换为字典的功能"""
    result = convert_tweet_to_dict(mock_tweet)
    
    assert isinstance(result, dict)
    assert result["id"] == "1234567890"
    assert result["text"] == "这是一条测试推文"
    assert result["created_at"] == "2024-01-01T00:00:00Z"
    assert result["favorite_count"] == 10
    assert result["retweet_count"] == 5
    assert result["reply_count"] == 3
    assert result["user_id"] == "user123"
    assert "entities" in result

def test_convert_user_to_dict(mock_user):
    """测试用户信息转换为字典的功能"""
    result = convert_user_to_dict(mock_user)
    
    assert isinstance(result, dict)
    assert result["id"] == "user123"
    assert result["screen_name"] == "testuser"
    assert result["name"] == "Test User"
    assert result["description"] == "这是一个测试用户"
    assert result["followers_count"] == 100
    assert result["following_count"] == 50
    assert result["created_at"] == "2023-01-01T00:00:00Z"
    assert result["location"] == "测试城市"
    assert result["url"] == "https://example.com"

@pytest.mark.asyncio
async def test_collect_user_state(mock_client, mock_config, mock_user, mock_tweet):
    """测试收集用户状态的功能"""
    user_id = "user123"
    
    # 设置模拟返回值
    mock_client.get_user_by_id.return_value = mock_user
    mock_client.get_user_tweets.return_value = [mock_tweet] * 3
    mock_client.get_user_following.return_value = [mock_user] * 2
    mock_client.get_user_followers.return_value = [mock_user] * 2
    
    result = await collect_user_state(user_id, mock_config, mock_client)
    
    assert isinstance(result, dict)
    assert "profile" in result
    assert "tweets" in result
    assert "following" in result
    assert "followers" in result
    assert "mentions" in result
    assert "likes" in result
    assert "retweets" in result
    assert "timestamp" in result
    
    assert len(result["tweets"]) == 3
    assert len(result["following"]) == 2
    assert len(result["followers"]) == 2
    assert isinstance(result["timestamp"], str)

def test_detect_state_changes():
    """测试状态变化检测功能"""
    last_state = {
        "profile": {
            "name": "Old Name",
            "description": "Old Description",
            "location": "Old Location",
            "url": "https://old.example.com"
        },
        "tweets": {"1", "2", "3"},
        "following": {"user1", "user2"},
        "followers": {"follower1", "follower2"},
        "mentions": {"mention1"},
        "likes": {"like1"},
        "retweets": {"retweet1"}
    }
    
    current_state = {
        "profile": {
            "name": "New Name",
            "description": "Old Description",
            "location": "New Location",
            "url": "https://new.example.com"
        },
        "tweets": {"2", "3", "4"},
        "following": {"user2", "user3"},
        "followers": {"follower2", "follower3"},
        "mentions": {"mention1", "mention2"},
        "likes": {"like1", "like2"},
        "retweets": {"retweet1", "retweet2"}
    }
    
    changes = detect_state_changes(last_state, current_state)
    
    assert isinstance(changes, dict)
    assert "profile" in changes
    assert changes["profile"]["name"]["old"] == "Old Name"
    assert changes["profile"]["name"]["new"] == "New Name"
    assert changes["profile"]["location"]["old"] == "Old Location"
    assert changes["profile"]["location"]["new"] == "New Location"
    assert changes["profile"]["url"]["old"] == "https://old.example.com"
    assert changes["profile"]["url"]["new"] == "https://new.example.com"
    
    assert "new_tweets" in changes
    assert "4" in changes["new_tweets"]
    
    assert "following" in changes
    assert "user3" in changes["following"]["new"]
    assert "user1" in changes["following"]["removed"]
    
    assert "followers" in changes
    assert "follower3" in changes["followers"]["new"]
    assert "follower1" in changes["followers"]["removed"]
    
    assert "new_mentions" in changes
    assert "mention2" in changes["new_mentions"]
    
    assert "new_likes" in changes
    assert "like2" in changes["new_likes"]
    
    assert "new_retweets" in changes
    assert "retweet2" in changes["new_retweets"]

@pytest.mark.asyncio
async def test_collect_user_state_error_handling(mock_client, mock_config):
    """测试收集用户状态时的错误处理"""
    user_id = "user123"
    mock_client.get_user_by_id.side_effect = Exception("API Error")
    
    result = await collect_user_state(user_id, mock_config, mock_client)
    
    assert isinstance(result, dict)
    assert result["profile"] is None
    assert len(result["tweets"]) == 0
    assert len(result["following"]) == 0
    assert len(result["followers"]) == 0
    assert len(result["mentions"]) == 0
    assert len(result["likes"]) == 0
    assert len(result["retweets"]) == 0
    assert "timestamp" in result

def test_detect_state_changes_no_changes():
    """测试没有状态变化时的情况"""
    state = {
        "profile": {
            "name": "Test Name",
            "description": "Test Description",
            "location": "Test Location",
            "url": "https://example.com"
        },
        "tweets": {"1", "2", "3"},
        "following": {"user1", "user2"},
        "followers": {"follower1", "follower2"},
        "mentions": {"mention1"},
        "likes": {"like1"},
        "retweets": {"retweet1"}
    }
    
    changes = detect_state_changes(state, state.copy())
    assert changes is None
