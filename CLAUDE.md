# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Twx is an intelligent Twitter monitoring and analysis platform that supports multi-account management, real-time monitoring, media processing, and data analytics. It consists of:

- **Backend API**: FastAPI-based REST API with intelligent account management and proxy rotation
- **Frontend**: Next.js React application for data visualization and management 
- **Core Systems**: Twitter data collection, account health monitoring, proxy management, and multi-database support

## Architecture

### Backend Structure (`src/`)
- `api/`: FastAPI routes, middleware, and API facade
  - `routes/`: Modular route handlers (accounts, tweets, monitoring, etc.)
  - `twitter_api_facade.py`: Main interface for Twitter operations
  - `account_health_manager.py`: Monitors account status and health
  - `risk_control.py`: Rate limiting and safety controls
- `proxy/`: Proxy management system with rotation and health checks
- `storage/`: Database abstraction layer supporting SQLite/PostgreSQL/JSON
- `utils/`: Logging, analytics, decorators, and utilities

### Frontend Structure (`frontend/src/`)
- `app/`: Next.js app router pages (monitor, analyze, trends)
- `components/`: React components for tweets, accounts, UI elements
- `types/`: TypeScript type definitions
- `utils/`: Frontend utilities and helpers

### Data Storage (`data/`)
- `sqlite/`: SQLite database files
- `cookies/`: Twitter session cookies by account
- `accounts/`: Account management data
- `tweets/`, `search_results/`, `hot_tweets/`: Twitter data exports
- `checkpoints/`, `states/`: Monitoring state persistence

## Development Commands

### Backend (Python)
```bash
# Install dependencies
pip install -r requirements.txt

# Start development server
python main.py

# Start with debug logging
python main.py --debug

# Run tests
pytest

# Run tests with coverage
pytest --cov=src --cov-report=term-missing
```

### Frontend (Next.js)
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies  
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Run linting
npm run lint
```

### Docker
```bash
# Build and run with Docker Compose
docker-compose up --build
```

## Configuration

### Environment Setup
- Copy `accounts.example.json` to `accounts.json` and configure Twitter accounts
- Set database type in `config.json` (sqlite/postgresql/json)
- Configure proxy settings in `webshare_proxy.json`
- Create required directories: `data/accounts`, `data/cookies`, `data/sqlite`, `logs`

### Database Configuration
The system supports three storage backends configured in `config.json`:
- **SQLite**: Default, good for development (`data/sqlite/twx.db`)
- **PostgreSQL**: Recommended for production
- **JSON**: File-based storage for backups and exports

### API Configuration
- Default port: 8000
- CORS enabled for `http://localhost:3000` (frontend)
- FastAPI docs available at `/docs`
- Health check endpoint at `/health`

## Key Components

### Account Management
- Multi-account support with health monitoring
- Automatic account rotation and failure handling
- Cookie-based session management
- Smart account selection based on health and usage

### Proxy System (`src/proxy/`)
- Automatic proxy rotation with health checks
- Support for Webshare and other proxy services
- Failure detection and automatic failover
- Proxy pool management and optimization

### Twitter Operations
- Tweet parsing and media extraction
- User monitoring and profile tracking
- Search functionality with filters
- Hot tweets discovery and trend analysis
- Rate limiting and request management

### Data Analysis
- Tweet sentiment analysis and metrics
- Media download and processing
- Export capabilities (JSON, SQLite)
- Real-time monitoring with checkpoints

## Testing

- Test files located in `tests/`
- Use pytest for running tests
- Coverage reporting configured
- Test configuration in `pytest.ini`

## Important Notes

- The system uses Chinese comments and documentation throughout
- Account credentials stored in `accounts.json` should be kept secure
- Proxy configuration is essential for reliable operation
- Database migrations handled automatically
- Logging configured with both file and console output
- Rate limiting implemented to avoid Twitter API restrictions