"""
代理轮换器 (Proxy Rotator)

实现智能代理轮换功能：
- 检测失效代理
- 基于地理位置匹配最相近的代理
- 自动更新账户代理配置
- 同步数据库和JSON文件
"""

import asyncio
import sqlite3
import ipaddress
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from dataclasses import dataclass

from src.utils.logger import setup_logger
from src.config import Config
from src.storage.database import Database
from src.proxy.service import ProxyService, ProxyInfo, ProxyTestConfig

logger = setup_logger(module_name="proxy_rotator")

@dataclass 
class ProxyScore:
    """代理匹配评分"""
    proxy_info: ProxyInfo
    score: float
    match_reason: str

class ProxyRotator:
    """代理轮换器"""
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or Config()
        self.db = Database(self.config)
        self.db_path = self.config.get_sqlite_path()
        
        # 初始化代理服务
        test_config = ProxyTestConfig(timeout=10, max_retries=3)
        self.proxy_service = ProxyService(config=self.config, test_config=test_config)
        
        logger.info("ProxyRotator initialized")
    
    async def detect_failed_proxies(self) -> List[Tuple[str, str]]:
        """
        检测失效的代理
        
        Returns:
            List[Tuple[str, str]]: (account_id, proxy_url) 的失效代理列表
        """
        logger.info("开始检测失效代理...")
        
        failed_proxies = []
        accounts = self.db.get_active_accounts()
        
        for account in accounts:
            account_id = account["account_id"]
            proxy_url = account.get("proxy")
            
            if not proxy_url:
                logger.debug(f"账户 {account_id} 没有配置代理")
                continue
                
            # 使用ProxyService测试代理
            is_working = await self.proxy_service.test_proxy(proxy_url)
            if not is_working:
                failed_proxies.append((account_id, proxy_url))
                logger.error(f"检测到失效代理 - 账户: {account_id}, 代理: {proxy_url}")
        
        logger.info(f"检测完成，发现 {len(failed_proxies)} 个失效代理")
        return failed_proxies
    
    async def load_available_proxies(self) -> List[ProxyInfo]:
        """
        从 webshare_proxy.json 和数据库加载可用代理
        
        Returns:
            List[ProxyInfo]: 可用代理列表
        """
        # 使用ProxyService加载所有可用代理
        return await self.proxy_service.load_all_available_proxies()
    
    def find_best_replacement_proxy(
        self, 
        failed_proxy_url: str, 
        available_proxies: List[ProxyInfo],
        exclude_proxies: Optional[List[str]] = None
    ) -> Optional[ProxyInfo]:
        """
        为失效代理找到最佳替换代理（支持一对一绑定）
        
        优先级顺序：
        1. 同IP段 (C类网络)
        2. 同城市
        3. 同国家
        4. 使用次数最少的可用代理
        
        Args:
            failed_proxy_url: 失效的代理URL
            available_proxies: 可用代理列表
            exclude_proxies: 要排除的代理URL列表
            
        Returns:
            Optional[ProxyInfo]: 最佳替换代理，如果没有找到则返回None
        """
        if not available_proxies:
            logger.warning("没有可用代理进行替换")
            return None
            
        exclude_proxies = exclude_proxies or []
        
        # 获取所有已分配的代理（从数据库查询）
        assigned_proxies = self._get_assigned_proxies()
        exclude_proxies.extend(assigned_proxies)
        
        # 解析失效代理的信息
        failed_info = self._parse_proxy_info(failed_proxy_url)
        if not failed_info:
            logger.error(f"无法解析失效代理信息: {failed_proxy_url}")
            return None
        
        logger.info(f"为失效代理寻找替换: IP={failed_info.get('ip')}, 国家={failed_info.get('country')}, 城市={failed_info.get('city')}")
        
        # 计算每个代理的匹配分数
        scored_proxies = []
        for proxy in available_proxies:
            if proxy.proxy_url in exclude_proxies or proxy.proxy_url == failed_proxy_url:
                continue
                
            score = self._calculate_proxy_score(failed_info, proxy)
            if score.score > 0:
                scored_proxies.append(score)
        
        if not scored_proxies:
            logger.warning("没有找到合适的替换代理")
            return None
        
        # 按分数排序，选择最佳匹配
        scored_proxies.sort(key=lambda x: x.score, reverse=True)
        best_match = scored_proxies[0]
        
        logger.info(f"找到最佳替换代理: {best_match.proxy_info.proxy_url}")
        logger.info(f"匹配原因: {best_match.match_reason}, 分数: {best_match.score}")
        
        return best_match.proxy_info
    
    def _get_assigned_proxies(self) -> List[str]:
        """获取所有已分配的代理列表"""
        assigned_proxies = self.proxy_service.get_assigned_proxies_from_database()
        return list(assigned_proxies)
    
    def _parse_proxy_info(self, proxy_url: str) -> Optional[Dict[str, Any]]:
        """解析代理URL信息"""
        try:
            # 查找数据库中的信息
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT ip, country_code, city_name, proxy_list 
                    FROM proxies 
                    WHERE proxy_url = ?
                """, (proxy_url,))
                row = cursor.fetchone()
                
                if row:
                    info = {
                        'ip': row['ip'],
                        'country': row['country_code'],
                        'city': row['city_name']
                    }
                    
                    # 尝试从原始数据获取更多信息
                    if row['proxy_list']:
                        try:
                            raw_data = json.loads(row['proxy_list'])
                            info['state'] = raw_data.get('state', '')
                        except:
                            pass
                    
                    return info
            
            # 如果数据库中没有，尝试从proxy_url解析IP
            if '@' in proxy_url:
                ip_part = proxy_url.split('@')[1].split(':')[0]
            else:
                ip_part = proxy_url.split(':')[0]
            
            return {'ip': ip_part, 'country': '', 'city': ''}
            
        except Exception as e:
            logger.error(f"解析代理信息失败: {e}")
            return None
    
    def _calculate_proxy_score(self, failed_info: Dict[str, Any], proxy: ProxyInfo) -> ProxyScore:
        """
        计算代理匹配分数
        
        分数计算规则：
        - 同IP段 (C类): +100分
        - 同城市: +50分  
        - 同国家: +20分
        - 使用次数少: +10分
        - 基础分: +1分
        """
        score = 1.0  # 基础分
        reasons = []
        
        failed_ip = failed_info.get('ip', '')
        failed_country = failed_info.get('country', '').upper()
        failed_city = failed_info.get('city', '').lower()
        
        proxy_ip = proxy.ip
        proxy_country = proxy.country_code.upper()
        proxy_city = proxy.city_name.lower()
        
        # 检查IP段匹配 (C类网络)
        if failed_ip and proxy_ip:
            try:
                failed_network = ipaddress.IPv4Network(f"{failed_ip}/24", strict=False)
                proxy_addr = ipaddress.IPv4Address(proxy_ip)
                if proxy_addr in failed_network:
                    score += 100
                    reasons.append("同IP段(C类)")
            except:
                pass
        
        # 检查城市匹配
        if failed_city and proxy_city and failed_city == proxy_city:
            score += 50
            reasons.append("同城市")
        
        # 检查国家匹配
        elif failed_country and proxy_country and failed_country == proxy_country:
            score += 20
            reasons.append("同国家")
        
        # 使用次数少的代理优先
        if proxy.use_count == 0:
            score += 10
            reasons.append("未使用")
        elif proxy.use_count < 5:
            score += 5
            reasons.append("使用次数少")
        
        match_reason = ", ".join(reasons) if reasons else "可用代理"
        
        return ProxyScore(
            proxy_info=proxy,
            score=score,
            match_reason=match_reason
        )
    
    async def rotate_proxy_for_account(self, account_id: str, new_proxy: ProxyInfo) -> bool:
        """
        为指定账户轮换代理（支持一对一绑定）
        
        Args:
            account_id: 账户ID
            new_proxy: 新的代理信息
            
        Returns:
            bool: 是否成功轮换
        """
        try:
            logger.info(f"开始为账户 {account_id} 轮换代理: {new_proxy.proxy_url}")
            
            # 先获取现有账户信息
            existing_account = self.db.get_account(account_id)
            if not existing_account:
                logger.error(f"账户 {account_id} 不存在")
                return False
                
            old_proxy = existing_account.get("proxy")
            
            # 从proxy manager导入相关功能
            from src.proxy.manager import ProxyManager
            proxy_manager = ProxyManager(self.db_path)
            
            # 释放旧代理分配
            if old_proxy:
                proxy_manager.release_proxy_from_account(account_id)
                logger.info(f"已释放账户 {account_id} 的旧代理: {old_proxy}")
            
            # 分配新代理
            if not proxy_manager.assign_proxy_to_account(new_proxy.proxy_url, account_id):
                logger.error(f"无法分配新代理 {new_proxy.proxy_url} 给账户 {account_id}")
                return False
            
            # 更新数据库中的账户代理，保持其他信息不变
            await self.db.save_account(
                account_id=account_id,
                auth_info_1=existing_account.get("auth_info_1", ""),
                auth_info_2=existing_account.get("auth_info_2", ""),
                password=existing_account.get("password", ""),
                totp_secret=existing_account.get("totp_secret", ""),
                proxy=new_proxy.proxy_url,
                cookies=existing_account.get("cookies"),
                status=existing_account.get("status", "active")
            )
            
            # 更新代理使用次数
            self._update_proxy_usage(new_proxy.proxy_url)
            
            # 更新本地JSON文件
            self._update_accounts_json(account_id, new_proxy.proxy_url)
            
            logger.info(f"✅ 账户 {account_id} 代理轮换成功: {old_proxy} -> {new_proxy.proxy_url}")
            return True
            
        except Exception as e:
            logger.error(f"账户 {account_id} 代理轮换失败: {e}")
            return False
    
    def _update_proxy_usage(self, proxy_url: str):
        """更新代理使用次数"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE proxies 
                    SET use_count = use_count + 1, last_used = CURRENT_TIMESTAMP, updated = ? 
                    WHERE proxy_url = ?
                """, (datetime.utcnow().isoformat(), proxy_url))
                conn.commit()
        except Exception as e:
            logger.error(f"更新代理使用次数失败: {e}")
    
    def _update_accounts_json(self, account_id: str, new_proxy_url: str):
        """更新本地JSON文件中的账户代理配置"""
        # 使用ProxyService的JSON更新功能
        try:
            # 首先找到新代理的详细信息
            all_proxies = self.proxy_service.load_all_available_proxies()
            proxy_info = next((p for p in all_proxies if p.proxy_url == new_proxy_url), None)
            
            if proxy_info:
                self.proxy_service.update_accounts_json(account_id, proxy_info)
                
                # 更新accounts2proxy.json
                accounts = self.proxy_service.load_json(self.proxy_service.accounts_file)
                self.proxy_service.update_accounts2proxy_json(accounts)
            else:
                logger.warning(f"找不到代理信息: {new_proxy_url}")
                
        except Exception as e:
            logger.error(f"更新JSON文件失败: {e}")
    
    async def auto_rotate_failed_proxies(self) -> Dict[str, Any]:
        """
        自动轮换所有失效的代理
        
        Returns:
            Dict[str, Any]: 轮换结果统计
        """
        logger.info("开始自动代理轮换...")
        
        # 检测失效代理
        failed_proxies = await self.detect_failed_proxies()
        
        if not failed_proxies:
            logger.info("没有发现失效代理")
            return {
                "total_checked": 0,
                "failed_count": 0,
                "rotated_count": 0,
                "failed_rotations": []
            }
        
        # 加载可用代理
        available_proxies = await self.load_available_proxies()
        
        # 执行轮换
        rotated_count = 0
        failed_rotations = []
        used_proxies = []  # 跟踪已使用的代理，避免重复分配
        
        for account_id, failed_proxy_url in failed_proxies:
            logger.info(f"处理账户 {account_id} 的失效代理...")
            
            # 找到最佳替换代理
            best_proxy = self.find_best_replacement_proxy(
                failed_proxy_url, 
                available_proxies,
                exclude_proxies=used_proxies
            )
            
            if best_proxy:
                success = await self.rotate_proxy_for_account(account_id, best_proxy)
                if success:
                    rotated_count += 1
                    used_proxies.append(best_proxy.proxy_url)
                    logger.info(f"✅ 账户 {account_id} 代理轮换成功")
                else:
                    failed_rotations.append({
                        "account_id": account_id,
                        "failed_proxy": failed_proxy_url,
                        "reason": "轮换操作失败"
                    })
            else:
                failed_rotations.append({
                    "account_id": account_id, 
                    "failed_proxy": failed_proxy_url,
                    "reason": "没有找到合适的替换代理"
                })
                logger.warning(f"❌ 账户 {account_id} 没有找到合适的替换代理")
        
        result = {
            "total_checked": len(self.db.get_active_accounts()),
            "failed_count": len(failed_proxies),
            "rotated_count": rotated_count,
            "failed_rotations": failed_rotations
        }
        
        logger.info(f"代理轮换完成: 检查 {result['total_checked']} 个账户, "
                   f"发现 {result['failed_count']} 个失效代理, "
                   f"成功轮换 {result['rotated_count']} 个")
        
        return result

# 创建全局实例
proxy_rotator = ProxyRotator()