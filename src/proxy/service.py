"""
代理服务 (Proxy Service)

统一的代理管理服务，提供：
- 代理测试和验证
- 代理数据加载和管理
- 账户-代理分配逻辑
- JSON文件同步
- 配置管理

避免代码重复，为 applyProxy.py 和 rotator.py 提供共同服务
"""

import asyncio
import json
import random
import aiohttp
import sqlite3
import time
from typing import Dict, List, Optional, Set, Any
from dataclasses import dataclass, field
from pathlib import Path

from src.utils.logger import setup_logger
from src.config import Config
from src.storage.database import Database

logger = setup_logger(module_name="proxy_service")

@dataclass
class ProxyTestConfig:
    """代理测试配置"""
    max_retries: int = 5
    timeout: int = 10
    concurrency_limit: int = 5
    test_urls: List[str] = field(default_factory=lambda: [
        "http://httpbin.org/ip",
        "https://api.ipify.org?format=json",
        "https://api.myip.com",
        "https://wtfismyip.com/json",
        "https://api.iplocation.net/?cmd=get-ip",
        "https://ip-api.com/json",
        "https://ipwho.is",
        "https://get.geojs.io/v1/ip.json",
        "https://ipapi.co/json",
        "https://jsonip.com",
        "https://ifconfig.me/ip",
        "http://icanhazip.com",
        "https://ipinfo.io/ip",
        "https://checkip.amazonaws.com",
        "https://api.ipify.org"
    ])

@dataclass
class ProxyInfo:
    """标准化的代理信息结构"""
    id: str
    proxy_url: str
    ip: str
    port: int
    username: str
    password: str
    country_code: str
    city_name: str = ""
    asn_name: str = ""
    asn_number: int = 0
    state: str = ""
    is_valid: bool = True
    use_count: int = 0
    raw_data: Optional[Dict[str, Any]] = None

class ProxyService:
    """统一的代理管理服务"""
    
    def __init__(self, config: Optional[Config] = None, test_config: Optional[ProxyTestConfig] = None):
        self.config = config or Config()
        self.db = Database(self.config)
        self.test_config = test_config or ProxyTestConfig()
        
        # 文件路径配置
        self.webshare_file = Path("webshare_proxy.json")
        self.accounts_file = Path("accounts.json")
        self.accounts2proxy_file = Path("accounts2proxy.json")
        
        # 缓存机制
        self._proxy_cache: Optional[List[ProxyInfo]] = None
        self._cache_timestamp: float = 0
        self._cache_ttl: float = 300  # 5分钟缓存
        self._cache_lock = asyncio.Lock()  # 缓存锁
        
        logger.info("ProxyService initialized")
    
    def _is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        return (
            self._proxy_cache is not None and 
            time.time() - self._cache_timestamp < self._cache_ttl
        )
    
    def _update_cache(self, proxies: List[ProxyInfo]) -> None:
        """更新缓存"""
        self._proxy_cache = proxies
        self._cache_timestamp = time.time()
        logger.debug(f"Proxy cache updated with {len(proxies)} proxies")
    
    def clear_cache(self) -> None:
        """清除缓存"""
        self._proxy_cache = None
        self._cache_timestamp = 0
        logger.debug("Proxy cache cleared")
    
    async def test_proxy(self, proxy_url: str, session: Optional[aiohttp.ClientSession] = None) -> bool:
        """测试代理是否可用
        
        Args:
            proxy_url: 代理URL
            session: 可选的aiohttp会话，如果不提供则创建新的
            
        Returns:
            bool: 代理是否可用
        """
        async def _test_with_session(session: aiohttp.ClientSession) -> bool:
            for attempt in range(1, self.test_config.max_retries + 1):
                try:
                    timeout = aiohttp.ClientTimeout(total=self.test_config.timeout)
                    test_url = self.test_config.test_urls[0]
                    async with session.get(test_url, proxy=proxy_url, timeout=timeout) as response:
                        if response.status == 200:
                            logger.debug(f"Proxy {proxy_url} test successful")
                            return True
                        logger.warning(f"Proxy {proxy_url} failed with status {response.status}, attempt {attempt}")
                        
                except Exception as e:
                    logger.debug(f"Proxy {proxy_url} test error, attempt {attempt}: {e}")
                
                if attempt < self.test_config.max_retries:
                    await asyncio.sleep(1)  # 重试间隔
            
            return False
        
        if session:
            return await _test_with_session(session)
        else:
            async with aiohttp.ClientSession() as new_session:
                return await _test_with_session(new_session)
    
    def build_proxy_url(self, proxy_data: Dict[str, Any]) -> str:
        """构建代理URL
        
        Args:
            proxy_data: 包含代理信息的字典
            
        Returns:
            str: 完整的代理URL
        """
        if 'username' in proxy_data and 'password' in proxy_data:
            return (f"http://{proxy_data['username']}:{proxy_data['password']}@"
                   f"{proxy_data['proxy_address']}:{proxy_data['port']}")
        else:
            return f"http://{proxy_data['proxy_address']}:{proxy_data['port']}"
    
    def load_webshare_proxies(self) -> List[ProxyInfo]:
        """从webshare_proxy.json加载代理列表
        
        Returns:
            List[ProxyInfo]: 代理信息列表
        """
        proxies = []
        
        if not self.webshare_file.exists():
            logger.warning(f"Webshare proxy file {self.webshare_file} not found")
            return proxies
        
        try:
            with open(self.webshare_file, 'r', encoding='utf-8') as f:
                webshare_data = json.load(f)
            
            for item in webshare_data:
                if item.get("valid", False):
                    proxy_info = ProxyInfo(
                        id=str(item['id']),
                        proxy_url=self.build_proxy_url(item),
                        ip=item['proxy_address'],
                        port=item['port'],
                        username=item['username'],
                        password=item['password'],
                        country_code=item.get('country_code', ''),
                        city_name=item.get('city_name', ''),
                        asn_name=item.get('asn_name', ''),
                        asn_number=item.get('asn_number', 0),
                        state=item.get('state', ''),
                        is_valid=item.get('valid', True),
                        raw_data=item
                    )
                    proxies.append(proxy_info)
            
            logger.info(f"Loaded {len(proxies)} proxies from {self.webshare_file}")
            
        except Exception as e:
            logger.error(f"Failed to load webshare proxies: {e}")
        
        return proxies
    
    def load_database_proxies(self) -> List[ProxyInfo]:
        """从数据库加载代理列表
        
        Returns:
            List[ProxyInfo]: 代理信息列表
        """
        proxies = []
        
        try:
            with sqlite3.connect(self.config.get_sqlite_path()) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT id, proxy_url, ip, port, username, password, 
                           country_code, city_name, status, use_count, proxy_list 
                    FROM proxies 
                    WHERE status = 'active'
                """)
                
                for row in cursor.fetchall():
                    # 尝试解析原始数据
                    raw_data = {}
                    if row['proxy_list']:
                        try:
                            raw_data = json.loads(row['proxy_list'])
                        except:
                            pass
                    
                    proxy_info = ProxyInfo(
                        id=str(row['id']),
                        proxy_url=row['proxy_url'],
                        ip=row['ip'],
                        port=row['port'] or 0,
                        username=row['username'] or "",
                        password=row['password'] or "",
                        country_code=row['country_code'] or '',
                        city_name=row['city_name'] or '',
                        asn_name=raw_data.get('asn_name', ''),
                        asn_number=raw_data.get('asn_number', 0),
                        use_count=row['use_count'] or 0,
                        raw_data=raw_data
                    )
                    proxies.append(proxy_info)
                
                logger.info(f"Loaded {len(proxies)} proxies from database")
                
        except Exception as e:
            logger.error(f"Failed to load database proxies: {e}")
        
        return proxies
    
    async def load_all_available_proxies(self, force_refresh: bool = False) -> List[ProxyInfo]:
        """加载所有可用代理（webshare）
        
        Args:
            force_refresh: 是否强制刷新缓存
        
        Returns:
            List[ProxyInfo]: 去重后的代理列表
        """
        async with self._cache_lock:
            # 检查缓存
            if not force_refresh and self._is_cache_valid() and self._proxy_cache:
                logger.debug(f"Using cached proxies: {len(self._proxy_cache)} proxies")
                return self._proxy_cache.copy()
            
            # 从webshare加载
            webshare_proxies = self.load_webshare_proxies()
            
            # 从数据库加载
            # db_proxies = self.load_database_proxies()
            
            # 去重（优先使用webshare数据）
            seen_urls = set()
            all_proxies = []
            
            for proxy in webshare_proxies:
                if proxy.proxy_url not in seen_urls:
                    all_proxies.append(proxy)
                    seen_urls.add(proxy.proxy_url)
            
            # 更新缓存
            self._update_cache(all_proxies)
            
            logger.info(f"Total available proxies: {len(all_proxies)}")
            return all_proxies
    
    def load_json(self, file_path: Path) -> List[Dict[str, Any]]:
        """加载JSON文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            List[Dict]: JSON数据
        """
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            if not isinstance(data, list):
                raise ValueError(f"{file_path} does not contain a JSON array")
            return data
        except (IOError, json.JSONDecodeError) as e:
            logger.error(f"Failed to load {file_path}: {e}")
            raise
    
    def save_json(self, file_path: Path, data: Any) -> None:
        """保存JSON文件
        
        Args:
            file_path: 文件路径
            data: 要保存的数据
        """
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info(f"Successfully saved {file_path}")
        except IOError as e:
            logger.error(f"Failed to save {file_path}: {e}")
            raise
    
    def update_accounts_json(self, account_id: str, proxy_info: ProxyInfo) -> None:
        """更新accounts.json文件中的代理信息
        
        Args:
            account_id: 账户ID
            proxy_info: 新的代理信息
        """
        if not self.accounts_file.exists():
            logger.warning(f"Accounts file {self.accounts_file} not found")
            return
        
        try:
            accounts = self.load_json(self.accounts_file)
            
            # 更新对应账户的代理信息
            for account in accounts:
                if account.get("account_id") == account_id:
                    account["proxy"] = {
                        "id": proxy_info.id,
                        "proxy_url": proxy_info.proxy_url,
                        "country_code": proxy_info.country_code,
                        "city_name": proxy_info.city_name,
                        "asn_name": proxy_info.asn_name,
                        "asn_number": proxy_info.asn_number,
                    }
                    break
            
            self.save_json(self.accounts_file, accounts)
            logger.debug(f"Updated proxy for account {account_id} in {self.accounts_file}")
            
            # 代理分配状态变化，清除缓存
            self.clear_cache()
            
        except Exception as e:
            logger.error(f"Failed to update accounts.json: {e}")
    
    def update_accounts2proxy_json(self, accounts: List[Dict[str, Any]]) -> None:
        """更新accounts2proxy.json映射文件
        
        Args:
            accounts: 账户列表
        """
        try:
            accounts2proxy = {}
            for account in accounts:
                if account.get("proxy"):
                    accounts2proxy[account["account_id"]] = {
                        "proxy_id": account["proxy"]["id"],
                        "proxy_url": account["proxy"]["proxy_url"],
                        "country_code": account["proxy"]["country_code"],
                        "city_name": account["proxy"]["city_name"],
                        "asn_name": account["proxy"]["asn_name"],
                        "asn_number": account["proxy"]["asn_number"],
                    }
            
            self.save_json(self.accounts2proxy_file, accounts2proxy)
            logger.info(f"Updated {self.accounts2proxy_file} with {len(accounts2proxy)} mappings")
            
        except Exception as e:
            logger.error(f"Failed to update accounts2proxy.json: {e}")
    
    async def batch_test_proxies(
        self, 
        proxies: List[ProxyInfo], 
        concurrency_limit: Optional[int] = None
    ) -> List[ProxyInfo]:
        """批量测试代理可用性
        
        Args:
            proxies: 代理列表
            concurrency_limit: 并发限制
            
        Returns:
            List[ProxyInfo]: 可用的代理列表
        """
        if concurrency_limit is None:
            concurrency_limit = self.test_config.concurrency_limit
        
        semaphore = asyncio.Semaphore(concurrency_limit)
        valid_proxies = []
        
        async def test_single_proxy(proxy: ProxyInfo) -> Optional[ProxyInfo]:
            async with semaphore:
                async with aiohttp.ClientSession() as session:
                    is_valid = await self.test_proxy(proxy.proxy_url, session)
                    if is_valid:
                        proxy.is_valid = True
                        return proxy
                    return None
        
        logger.info(f"Testing {len(proxies)} proxies with concurrency {concurrency_limit}")
        
        tasks = [test_single_proxy(proxy) for proxy in proxies]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for result in results:
            if isinstance(result, ProxyInfo):
                valid_proxies.append(result)
            elif isinstance(result, Exception):
                logger.error(f"Proxy test error: {result}")
        
        logger.info(f"Found {len(valid_proxies)} valid proxies out of {len(proxies)}")
        return valid_proxies
    
    def get_assigned_proxies_from_database(self) -> Set[str]:
        """从数据库获取已分配的代理URL集合
        
        Returns:
            Set[str]: 已分配的代理URL集合
        """
        try:
            with sqlite3.connect(self.config.get_sqlite_path()) as conn:
                cursor = conn.execute("""
                    SELECT proxy_url FROM proxies 
                    WHERE assigned_to IS NOT NULL AND assigned_to != ''
                """)
                return {row[0] for row in cursor.fetchall()}
        except Exception as e:
            logger.error(f"Failed to get assigned proxies: {e}")
            return set()
    
    def get_proxy_usage_count(self) -> Dict[str, int]:
        """获取代理使用计数
        
        Returns:
            Dict[str, int]: 代理ID到使用次数的映射
        """
        usage_count = {}
        try:
            accounts = self.load_json(self.accounts_file)
            for account in accounts:
                if account.get("proxy"):
                    proxy_id = account["proxy"]["id"]
                    usage_count[proxy_id] = usage_count.get(proxy_id, 0) + 1
        except Exception as e:
            logger.error(f"Failed to get proxy usage count: {e}")
        
        return usage_count

# 创建全局实例
proxy_service = ProxyService()