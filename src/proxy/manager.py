import os
import sqlite3
import requests
import json
from typing import Optional, List, Dict, Any
from datetime import datetime
from src.utils.logger import setup_logger

logger = setup_logger(module_name="proxy_manager")

class ProxyManager:
    _instance = None
    _initialized = False

    def __new__(cls, db_path):
        if cls._instance is None:
            cls._instance = super(ProxyManager, cls).__new__(cls)
        return cls._instance

    def __init__(self, db_path):
        if not self._initialized:
            self.db_path = db_path
            self.init_db()
            self._initialized = True

    def init_db(self):
        logger.debug("Initializing proxies table")
        with sqlite3.connect(self.db_path) as conn:
            # 使用与schema.sql一致的表定义
            conn.execute("""
                CREATE TABLE IF NOT EXISTS proxies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ip VARCHAR(45) NOT NULL,
                    port INTEGER NOT NULL,
                    username TEXT,
                    password TEXT,
                    proxy_url TEXT,
                    country_code VARCHAR(10),
                    city_name VARCHAR(100),
                    assigned_to VARCHAR(255),
                    assigned_at TIMESTAMP,
                    use_count INTEGER DEFAULT 0,
                    last_used TIMESTAMP,
                    status VARCHAR(50) DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (assigned_to) REFERENCES accounts(account_id)
                )
            """)
            
            # 检查并添加可能缺失的字段（为了向后兼容旧版本）
            cursor = conn.execute("PRAGMA table_info(proxies)")
            columns = {row[1] for row in cursor.fetchall()}
            
            # 添加旧版本字段支持（如果需要）
            if "proxy_list" not in columns:
                logger.info("Adding proxy_list column for backward compatibility")
                conn.execute("ALTER TABLE proxies ADD COLUMN proxy_list TEXT")
            
            if "created" not in columns:
                logger.info("Adding created column for backward compatibility")
                conn.execute("ALTER TABLE proxies ADD COLUMN created TEXT")
            
            if "updated" not in columns:
                logger.info("Adding updated column for backward compatibility")
                conn.execute("ALTER TABLE proxies ADD COLUMN updated TEXT")
            
            conn.commit()

    def add_proxy(self, ip: str, port: int = 80, username: str = "", password: str = "", 
                  country_code: str = "", city_name: str = "", proxy_list: str = ""):
        """添加代理，包含地域信息、时间戳和原始数据"""
        now = datetime.utcnow().isoformat()
        
        # 构建完整的代理URL
        if username and password:
            proxy_url = f"http://{username}:{password}@{ip}:{port}"
        else:
            proxy_url = f"http://{ip}:{port}"
            
        logger.debug(f"Adding proxy: {proxy_url}, Country: {country_code}, City: {city_name}")
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO proxies (
                    ip, port, username, password, proxy_url, status, use_count, 
                    country_code, city_name, created_at, created, updated, proxy_list
                )
                VALUES (?, ?, ?, ?, ?, 'active', 0, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?)
            """, (ip, port, username, password, proxy_url, country_code, city_name, now, now, proxy_list))

    def check_proxy(self, ip: str) -> bool:
        logger.debug(f"Checking proxy: {ip}")
        try:
            proxy_url = ip if ip.startswith("http") else f"http://{ip}"
            response = requests.get("https://api.ipify.org", proxies={"http": proxy_url, "https": proxy_url}, timeout=5)
            logger.debug(f"Proxy {ip} check result: {response.status_code == 200}")
            return response.status_code == 200
        except requests.RequestException as e:
            logger.error(f"Proxy check failed for {ip}: {e}")
            return False

    def get_proxy(self, exclude_assigned: bool = True) -> Optional[str]:
        """获取可用代理（支持一对一绑定）
        
        Args:
            exclude_assigned: 是否排除已分配的代理
        
        Returns:
            Optional[str]: 可用的代理URL，如果没有可用代理则返回None
        """
        logger.debug("Fetching a proxy from pool")
        with sqlite3.connect(self.db_path) as conn:
            if exclude_assigned:
                # 只选择未分配的代理
                cursor = conn.execute("""
                    SELECT proxy_url, use_count FROM proxies 
                    WHERE status = 'active' AND (assigned_to IS NULL OR assigned_to = '')
                """)
            else:
                # 包括已分配的代理（向后兼容）
                cursor = conn.execute("SELECT proxy_url, use_count FROM proxies WHERE status = 'active'")
                
            proxies = [(row[0], row[1]) for row in cursor.fetchall()]
            if not proxies:
                logger.warning("No available proxies found")
                return None
                
            proxy_url = min(proxies, key=lambda x: x[1])[0]
            conn.execute("""
                UPDATE proxies 
                SET use_count = use_count + 1, last_used = CURRENT_TIMESTAMP, updated = ? 
                WHERE proxy_url = ?
            """, (datetime.utcnow().isoformat(), proxy_url))
            logger.debug(f"Selected proxy: {proxy_url}")
            return proxy_url
    
    def assign_proxy_to_account(self, proxy_url: str, account_id: str) -> bool:
        """将代理分配给指定账户
        
        Args:
            proxy_url: 代理URL
            account_id: 账户ID
            
        Returns:
            bool: 是否分配成功
        """
        logger.debug(f"Assigning proxy {proxy_url} to account {account_id}")
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 检查代理是否已被分配
                cursor = conn.execute("""
                    SELECT assigned_to FROM proxies 
                    WHERE proxy_url = ? AND (assigned_to IS NOT NULL AND assigned_to != '')
                """, (proxy_url,))
                existing_assignment = cursor.fetchone()
                
                if existing_assignment and existing_assignment[0] != account_id:
                    logger.warning(f"Proxy {proxy_url} is already assigned to {existing_assignment[0]}")
                    return False
                
                # 分配代理
                conn.execute("""
                    UPDATE proxies 
                    SET assigned_to = ?, assigned_at = CURRENT_TIMESTAMP, updated = ?
                    WHERE proxy_url = ?
                """, (account_id, datetime.utcnow().isoformat(), proxy_url))
                
                conn.commit()
                logger.debug(f"Successfully assigned proxy {proxy_url} to account {account_id}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to assign proxy {proxy_url} to account {account_id}: {e}")
            return False
    
    def release_proxy_from_account(self, account_id: str) -> bool:
        """释放账户的代理分配
        
        Args:
            account_id: 账户ID
            
        Returns:
            bool: 是否释放成功
        """
        logger.debug(f"Releasing proxy assignment for account {account_id}")
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT proxy_url FROM proxies WHERE assigned_to = ?", (account_id,))
                proxy_urls = [row[0] for row in cursor.fetchall()]
                
                if not proxy_urls:
                    logger.debug(f"No proxy assigned to account {account_id}")
                    return True
                
                # 释放所有分配给该账户的代理
                conn.execute("""
                    UPDATE proxies 
                    SET assigned_to = NULL, assigned_at = NULL, updated = ?
                    WHERE assigned_to = ?
                """, (datetime.utcnow().isoformat(), account_id))
                
                conn.commit()
                logger.info(f"Released proxy assignments for account {account_id}: {proxy_urls}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to release proxy for account {account_id}: {e}")
            return False
    
    def get_account_proxy(self, account_id: str) -> Optional[str]:
        """获取账户当前分配的代理
        
        Args:
            account_id: 账户ID
            
        Returns:
            Optional[str]: 账户分配的代理URL，如果没有分配则返回None
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT proxy_url FROM proxies WHERE assigned_to = ?", (account_id,))
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            logger.error(f"Failed to get proxy for account {account_id}: {e}")
            return None
    
    def get_unassigned_proxy_for_account(self, account_id: str) -> Optional[str]:
        """为账户获取一个未分配的代理并自动分配
        
        Args:
            account_id: 账户ID
            
        Returns:
            Optional[str]: 分配的代理URL，如果没有可用代理则返回None
        """
        logger.debug(f"Getting unassigned proxy for account {account_id}")
        
        # 先检查账户是否已有分配的代理
        existing_proxy = self.get_account_proxy(account_id)
        if existing_proxy:
            logger.debug(f"Account {account_id} already has proxy: {existing_proxy}")
            return existing_proxy
        
        # 获取一个未分配的代理
        proxy_url = self.get_proxy(exclude_assigned=True)
        if not proxy_url:
            logger.warning(f"No unassigned proxy available for account {account_id}")
            return None
        
        # 分配给账户
        if self.assign_proxy_to_account(proxy_url, account_id):
            return proxy_url
        else:
            logger.error(f"Failed to assign proxy {proxy_url} to account {account_id}")
            return None
    
    def validate_proxy_assignment(self, proxy_url: str, account_id: str) -> bool:
        """验证代理分配是否有效（一对一绑定检查）
        
        Args:
            proxy_url: 代理URL
            account_id: 账户ID
            
        Returns:
            bool: 分配是否有效
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 检查代理是否已被其他账户分配
                cursor = conn.execute("""
                    SELECT assigned_to FROM proxies 
                    WHERE proxy_url = ? AND assigned_to IS NOT NULL AND assigned_to != ''
                """, (proxy_url,))
                result = cursor.fetchone()
                
                if result and result[0] != account_id:
                    logger.warning(f"Proxy {proxy_url} is assigned to {result[0]}, not {account_id}")
                    return False
                
                # 检查账户是否已分配其他代理
                cursor = conn.execute("""
                    SELECT proxy_url FROM proxies 
                    WHERE assigned_to = ? AND proxy_url != ?
                """, (account_id, proxy_url))
                conflicting_proxies = cursor.fetchall()
                
                if conflicting_proxies:
                    logger.warning(f"Account {account_id} already has other proxies: {[p[0] for p in conflicting_proxies]}")
                    return False
                
                return True
                
        except Exception as e:
            logger.error(f"Failed to validate proxy assignment: {e}")
            return False
    
    def get_proxy_assignment_conflicts(self) -> List[Dict[str, Any]]:
        """检查代理分配冲突
        
        Returns:
            List[Dict[str, Any]]: 冲突列表，每个冲突包含详细信息
        """
        conflicts = []
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                # 检查一个代理分配给多个账户的情况
                cursor = conn.execute("""
                    SELECT proxy_url, GROUP_CONCAT(assigned_to) as accounts, COUNT(*) as count
                    FROM proxies 
                    WHERE assigned_to IS NOT NULL AND assigned_to != ''
                    GROUP BY proxy_url
                    HAVING COUNT(*) > 1
                """)
                
                for row in cursor.fetchall():
                    conflicts.append({
                        "type": "多重分配",
                        "proxy": row["proxy_url"],
                        "accounts": row["accounts"].split(","),
                        "count": row["count"]
                    })
                
                # 检查一个账户分配了多个代理的情况
                cursor = conn.execute("""
                    SELECT assigned_to, GROUP_CONCAT(proxy_url) as proxies, COUNT(*) as count
                    FROM proxies 
                    WHERE assigned_to IS NOT NULL AND assigned_to != ''
                    GROUP BY assigned_to
                    HAVING COUNT(*) > 1
                """)
                
                for row in cursor.fetchall():
                    conflicts.append({
                        "type": "多代理账户",
                        "account": row["assigned_to"],
                        "proxies": row["proxies"].split(","),
                        "count": row["count"]
                    })
                
        except Exception as e:
            logger.error(f"Failed to check proxy assignment conflicts: {e}")
            
        return conflicts
    
    def fix_proxy_assignment_conflicts(self) -> Dict[str, Any]:
        """修复代理分配冲突
        
        Returns:
            Dict[str, Any]: 修复结果统计
        """
        conflicts = self.get_proxy_assignment_conflicts()
        fixed_count = 0
        failed_fixes = []
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                for conflict in conflicts:
                    if conflict["type"] == "多重分配":
                        # 对于一个代理分配给多个账户，只保留第一个分配
                        proxy_url = conflict["proxy"]
                        accounts = conflict["accounts"]
                        keep_account = accounts[0]
                        release_accounts = accounts[1:]
                        
                        logger.info(f"Fixing multi-assignment for proxy {proxy_url}: keeping {keep_account}, releasing {release_accounts}")
                        
                        # 释放其他账户的分配
                        for account in release_accounts:
                            conn.execute("""
                                UPDATE proxies 
                                SET assigned_to = NULL, assigned_at = NULL, updated = ?
                                WHERE proxy_url = ? AND assigned_to = ?
                            """, (datetime.utcnow().isoformat(), proxy_url, account))
                        
                        fixed_count += len(release_accounts)
                        
                    elif conflict["type"] == "多代理账户":
                        # 对于一个账户分配了多个代理，只保留第一个代理
                        account_id = conflict["account"]
                        proxies = conflict["proxies"]
                        keep_proxy = proxies[0]
                        release_proxies = proxies[1:]
                        
                        logger.info(f"Fixing multi-proxy for account {account_id}: keeping {keep_proxy}, releasing {release_proxies}")
                        
                        # 释放其他代理
                        for proxy in release_proxies:
                            conn.execute("""
                                UPDATE proxies 
                                SET assigned_to = NULL, assigned_at = NULL, updated = ?
                                WHERE proxy_url = ? AND assigned_to = ?
                            """, (datetime.utcnow().isoformat(), proxy, account_id))
                        
                        fixed_count += len(release_proxies)
                
                conn.commit()
                logger.info(f"Fixed {fixed_count} proxy assignment conflicts")
                
        except Exception as e:
            logger.error(f"Failed to fix proxy assignment conflicts: {e}")
            failed_fixes.append(str(e))
        
        return {
            "total_conflicts": len(conflicts),
            "fixed_count": fixed_count,
            "failed_fixes": failed_fixes
        }

    def update_proxy_status(self):
        """更新代理状态和 updated 时间戳"""
        now = datetime.utcnow().isoformat()
        logger.debug("Updating proxy statuses")
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT proxy_url FROM proxies")
            for row in cursor:
                proxy_url = row[0]
                status = 'active' if self.check_proxy(proxy_url) else 'inactive'
                conn.execute("UPDATE proxies SET status = ?, updated = ? WHERE proxy_url = ?", (status, now, proxy_url))
                logger.debug(f"Updated proxy {proxy_url} to status: {status}")

    def fetch_webshare_proxies(self, api_key: str, page_size: int = 25):
        """从 Webshare API 获取代理列表并添加到数据库"""
        proxy_list = []
        proxy_raw_list = []
        page = 1
        has_next = True
        logger.debug(f"Fetching proxies from Webshare API (page_size={page_size})")

        while has_next:
            try:
                response = requests.get(
                    f"https://proxy.webshare.io/api/v2/proxy/list/?mode=direct&page={page}&page_size={page_size}",
                    headers={"Authorization": f"Token {api_key}"},
                    timeout=5
                )
                response.raise_for_status()
                data = response.json()
                logger.debug(f"Fetched page {page} with {len(data['results'])} proxies")

                for proxy in data["results"]:
                    if proxy["valid"]:
                        proxy_url = (
                            f"http://{proxy['username']}:{proxy['password']}@"
                            f"{proxy['proxy_address']}:{proxy['port']}"
                        )
                        country_code = proxy.get("country_code", "")
                        city_name = proxy.get("city_name", "")
                        proxy_raw = json.dumps(proxy)
                        proxy_list.append(proxy_url)
                        proxy_raw_list.append(proxy)
                        
                        # 使用新的 add_proxy 方法
                        self.add_proxy(
                            ip=proxy['proxy_address'],
                            port=proxy['port'],
                            username=proxy['username'],
                            password=proxy['password'],
                            country_code=country_code,
                            city_name=city_name,
                            proxy_list=proxy_raw
                        )
                        logger.debug(f"Added proxy: {proxy_url}, Country: {country_code}, City: {city_name}")

                has_next = data["next"] is not None
                page += 1
            except requests.RequestException as e:
                logger.error(f"Failed to fetch proxies from Webshare: {e}")
                break

        # self.update_proxy_status()
        logger.debug(f"Total proxies fetched: {len(proxy_list)}")
        webshare_proxy = os.path.join(os.path.dirname(__file__),'..','..', "webshare_proxy.json") 
        # print("webshare_proxy:"+ webshare_proxy)
        with open(webshare_proxy, "w") as f:
            json.dump(proxy_raw_list, f, indent=2)
        return proxy_list