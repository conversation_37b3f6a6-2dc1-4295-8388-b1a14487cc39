
from typing import List, Optional
from pydantic import BaseModel


class VideoInfo(BaseModel):
    duration_millis: int
    aspect_ratio: List[int]

class Stream(BaseModel):
    url: str
    bitrate: int
class MediaInfo(BaseModel):
    type: str
    media_url: str
    expanded_url: str
    original_url: str
    url: str
    display_url: str
    height: int
    width: int
    video_info: Optional[VideoInfo] = None
    streams: Optional[List[Stream]] = None

