"""
账号风险控制策略
实现请求频率限制、冷却期管理、风险预警和自动保护机制
"""

import asyncio
import json
import os
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Set, Tuple, Any
import random

from src.utils.logger import setup_logger

logger = setup_logger(module_name="risk_control")

class RiskLevel(Enum):
    """风险等级"""
    SAFE = "safe"           # 安全
    LOW = "low"             # 低风险
    MEDIUM = "medium"       # 中等风险
    HIGH = "high"           # 高风险
    CRITICAL = "critical"   # 危险

class ProtectionAction(Enum):
    """保护动作"""
    NONE = "none"                    # 无动作
    SLOW_DOWN = "slow_down"          # 减慢请求
    TEMPORARY_PAUSE = "temp_pause"   # 临时暂停
    COOLDOWN = "cooldown"            # 强制冷却
    QUARANTINE = "quarantine"        # 隔离账号

@dataclass
class RiskThresholds:
    """风险阈值配置"""
    # 请求频率阈值
    requests_per_minute: int = 10
    requests_per_hour: int = 100
    requests_per_day: int = 1000
    
    # 失败率阈值
    failure_rate_warning: float = 0.3    # 30%失败率警告
    failure_rate_critical: float = 0.5   # 50%失败率危险
    
    # 连续失败阈值
    consecutive_failures_warning: int = 5
    consecutive_failures_critical: int = 10
    
    # 限流阈值
    rate_limits_per_hour: int = 3
    rate_limits_per_day: int = 10
    
    # 认证失败阈值
    auth_failures_per_hour: int = 2
    auth_failures_per_day: int = 5

@dataclass
class RequestWindow:
    """请求时间窗口"""
    timestamps: deque = field(default_factory=deque)
    failures: deque = field(default_factory=deque)
    rate_limits: deque = field(default_factory=deque)
    auth_failures: deque = field(default_factory=deque)
    
    def add_request(self, timestamp: float, success: bool = True):
        """添加请求记录"""
        self.timestamps.append(timestamp)
        if not success:
            self.failures.append(timestamp)
    
    def add_rate_limit(self, timestamp: float):
        """添加限流记录"""
        self.rate_limits.append(timestamp)
    
    def add_auth_failure(self, timestamp: float):
        """添加认证失败记录"""
        self.auth_failures.append(timestamp)
    
    def cleanup_old_records(self, current_time: float, window_seconds: int):
        """清理过期记录"""
        cutoff_time = current_time - window_seconds
        
        while self.timestamps and self.timestamps[0] < cutoff_time:
            self.timestamps.popleft()
        
        while self.failures and self.failures[0] < cutoff_time:
            self.failures.popleft()
        
        while self.rate_limits and self.rate_limits[0] < cutoff_time:
            self.rate_limits.popleft()
        
        while self.auth_failures and self.auth_failures[0] < cutoff_time:
            self.auth_failures.popleft()

@dataclass
class AccountRiskProfile:
    """账号风险档案"""
    account_id: str
    risk_level: RiskLevel = RiskLevel.SAFE
    protection_action: ProtectionAction = ProtectionAction.NONE
    
    # 时间窗口记录
    minute_window: RequestWindow = field(default_factory=RequestWindow)
    hour_window: RequestWindow = field(default_factory=RequestWindow)
    day_window: RequestWindow = field(default_factory=RequestWindow)
    
    # 状态信息
    is_quarantined: bool = False
    quarantine_until: Optional[datetime] = None
    cooldown_until: Optional[datetime] = None
    last_risk_assessment: Optional[datetime] = None
    
    # 统计信息
    total_requests: int = 0
    total_failures: int = 0
    consecutive_failures: int = 0
    last_request_time: Optional[datetime] = None
    
    # 风险评分历史
    risk_scores: List[Tuple[datetime, float]] = field(default_factory=list)

class RiskControlManager:
    """风险控制管理器"""
    
    def __init__(self, thresholds: Optional[RiskThresholds] = None):
        self.thresholds = thresholds or RiskThresholds()
        self.account_profiles: Dict[str, AccountRiskProfile] = {}
        self.global_stats = {
            "total_requests": 0,
            "total_failures": 0,
            "accounts_quarantined": 0,
            "protection_actions_taken": 0
        }
        
        # 配置参数
        self.risk_assessment_interval = int(os.getenv("RISK_ASSESSMENT_INTERVAL", "60"))  # 1分钟
        self.cleanup_interval = int(os.getenv("CLEANUP_INTERVAL", "300"))  # 5分钟
        
        # 后台任务控制
        self._risk_monitor_task: Optional[asyncio.Task] = None
        self._cleanup_task: Optional[asyncio.Task] = None
        self._is_monitoring = False
        self._is_cleanup_running = False
        
        logger.info("RiskControlManager initialized (background tasks not started)")
    
    async def start_risk_monitor(self) -> bool:
        """启动风险监控后台任务"""
        if self._is_monitoring:
            logger.warning("Risk monitor is already running")
            return False
        
        try:
            self._risk_monitor_task = asyncio.create_task(self._risk_monitor_loop())
            self._is_monitoring = True
            logger.info("Risk monitor started successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to start risk monitor: {e}")
            return False
    
    async def stop_risk_monitor(self) -> bool:
        """停止风险监控后台任务"""
        if not self._is_monitoring or not self._risk_monitor_task:
            logger.warning("Risk monitor is not running")
            return False
        
        try:
            self._risk_monitor_task.cancel()
            await self._risk_monitor_task
            self._is_monitoring = False
            self._risk_monitor_task = None
            logger.info("Risk monitor stopped successfully")
            return True
        except asyncio.CancelledError:
            self._is_monitoring = False
            self._risk_monitor_task = None
            logger.info("Risk monitor stopped successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to stop risk monitor: {e}")
            return False
    
    async def start_cleanup_task(self) -> bool:
        """启动清理任务后台任务"""
        if self._is_cleanup_running:
            logger.warning("Cleanup task is already running")
            return False
        
        try:
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            self._is_cleanup_running = True
            logger.info("Cleanup task started successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to start cleanup task: {e}")
            return False
    
    async def stop_cleanup_task(self) -> bool:
        """停止清理任务后台任务"""
        if not self._is_cleanup_running or not self._cleanup_task:
            logger.warning("Cleanup task is not running")
            return False
        
        try:
            self._cleanup_task.cancel()
            await self._cleanup_task
            self._is_cleanup_running = False
            self._cleanup_task = None
            logger.info("Cleanup task stopped successfully")
            return True
        except asyncio.CancelledError:
            self._is_cleanup_running = False
            self._cleanup_task = None
            logger.info("Cleanup task stopped successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to stop cleanup task: {e}")
            return False
    
    async def start_all_background_tasks(self) -> bool:
        """启动所有后台任务"""
        try:
            monitor_success = await self.start_risk_monitor()
            cleanup_success = await self.start_cleanup_task()
            return monitor_success and cleanup_success
        except Exception as e:
            logger.error(f"Failed to start background tasks: {e}")
            return False
    
    async def stop_all_background_tasks(self) -> bool:
        """停止所有后台任务"""
        try:
            monitor_success = await self.stop_risk_monitor()
            cleanup_success = await self.stop_cleanup_task()
            return monitor_success and cleanup_success
        except Exception as e:
            logger.error(f"Failed to stop background tasks: {e}")
            return False
    
    def is_monitoring(self) -> bool:
        """检查风险监控是否正在运行"""
        return self._is_monitoring
    
    def is_cleanup_running(self) -> bool:
        """检查清理任务是否正在运行"""
        return self._is_cleanup_running
    
    async def get_background_tasks_status(self) -> Dict[str, Any]:
        """获取后台任务状态"""
        return {
            "risk_monitor": {
                "is_running": self._is_monitoring,
                "task_running": self._risk_monitor_task is not None and not self._risk_monitor_task.done(),
                "task_cancelled": self._risk_monitor_task.cancelled() if self._risk_monitor_task else False,
                "task_exception": str(self._risk_monitor_task.exception()) if self._risk_monitor_task and self._risk_monitor_task.done() and self._risk_monitor_task.exception() else None
            },
            "cleanup_task": {
                "is_running": self._is_cleanup_running,
                "task_running": self._cleanup_task is not None and not self._cleanup_task.done(),
                "task_cancelled": self._cleanup_task.cancelled() if self._cleanup_task else False,
                "task_exception": str(self._cleanup_task.exception()) if self._cleanup_task and self._cleanup_task.done() and self._cleanup_task.exception() else None
            },
            "intervals": {
                "risk_assessment_interval": self.risk_assessment_interval,
                "cleanup_interval": self.cleanup_interval
            }
        }
    
    async def trigger_risk_assessment(self) -> bool:
        """手动触发一次风险评估"""
        try:
            await self._assess_all_accounts_risk()
            logger.info("Manual risk assessment completed")
            return True
        except Exception as e:
            logger.error(f"Manual risk assessment failed: {e}")
            return False
    
    async def trigger_cleanup(self) -> bool:
        """手动触发一次数据清理"""
        try:
            await self._cleanup_expired_data()
            logger.info("Manual cleanup completed")
            return True
        except Exception as e:
            logger.error(f"Manual cleanup failed: {e}")
            return False
    
    async def _risk_monitor_loop(self):
        """风险监控后台任务循环"""
        while True:
            try:
                await self._assess_all_accounts_risk()
                await asyncio.sleep(self.risk_assessment_interval)
            except asyncio.CancelledError:
                logger.info("Risk monitor task cancelled")
                break
            except Exception as e:
                logger.error(f"Risk monitor task error: {e}")
                await asyncio.sleep(60)
    
    async def _cleanup_loop(self):
        """清理过期数据的后台任务循环"""
        while True:
            try:
                await self._cleanup_expired_data()
                await asyncio.sleep(self.cleanup_interval)
            except asyncio.CancelledError:
                logger.info("Cleanup task cancelled")
                break
            except Exception as e:
                logger.error(f"Cleanup task error: {e}")
                await asyncio.sleep(60)
    
    async def check_request_permission(self, account_id: str) -> Tuple[bool, str]:
        """检查账号是否允许发起请求"""
        logger.debug(f'check_request_permission {account_id}')
        profile = self._get_or_create_profile(account_id)
        current_time = time.time()
        
        # 检查隔离状态
        if profile.is_quarantined:
            if profile.quarantine_until and datetime.now() < profile.quarantine_until:
                return False, f"Account is quarantined until {profile.quarantine_until}"
            else:
                # 隔离期结束，解除隔离
                profile.is_quarantined = False
                profile.quarantine_until = None
                logger.info(f"Account {account_id} quarantine lifted")
        
        # 检查冷却期
        if profile.cooldown_until and datetime.now() < profile.cooldown_until:
            return False, f"Account is in cooldown until {profile.cooldown_until}"
        
        # 检查请求频率限制
        profile.minute_window.cleanup_old_records(current_time, 60)
        profile.hour_window.cleanup_old_records(current_time, 3600)
        profile.day_window.cleanup_old_records(current_time, 86400)
        
        # 分钟级限制
        if len(profile.minute_window.timestamps) >= self.thresholds.requests_per_minute:
            return False, "Minute request limit exceeded"
        
        # 小时级限制
        if len(profile.hour_window.timestamps) >= self.thresholds.requests_per_hour:
            return False, "Hour request limit exceeded"
        
        # 日级限制
        if len(profile.day_window.timestamps) >= self.thresholds.requests_per_day:
            return False, "Day request limit exceeded"
        
        return True, "Request permitted"
    
    async def record_request(self, account_id: str, success: bool, error_type: Optional[str] = None, err: Optional[Exception] = None, proxy: Optional[str] = ''):
        """记录请求结果"""
        logger.debug(f'record_request: account_id:{account_id}, proxy: {proxy}, success:{success} error_type:{error_type}, err: {err}')
        profile = self._get_or_create_profile(account_id)
        current_time = time.time()
        
        # 更新统计
        profile.total_requests += 1
        profile.last_request_time = datetime.now()
        self.global_stats["total_requests"] += 1
        
        # 记录到时间窗口
        profile.minute_window.add_request(current_time, success)
        profile.hour_window.add_request(current_time, success)
        profile.day_window.add_request(current_time, success)
        
        if not success:
            profile.total_failures += 1
            profile.consecutive_failures += 1
            self.global_stats["total_failures"] += 1
            
            # 记录特定错误类型
            if error_type == "rate_limit":
                profile.minute_window.add_rate_limit(current_time)
                profile.hour_window.add_rate_limit(current_time)
                profile.day_window.add_rate_limit(current_time)
            elif error_type == "auth_failure":
                profile.minute_window.add_auth_failure(current_time)
                profile.hour_window.add_auth_failure(current_time)
                profile.day_window.add_auth_failure(current_time)
        else:
            profile.consecutive_failures = 0
        
        # 立即评估风险
        await self._assess_account_risk(profile)
    
    async def _assess_account_risk(self, profile: AccountRiskProfile):
        """评估单个账号的风险"""
        current_time = time.time()
        risk_score = 0.0
        
        # 清理过期数据
        profile.hour_window.cleanup_old_records(current_time, 3600)
        profile.day_window.cleanup_old_records(current_time, 86400)
        
        # 计算失败率风险
        if profile.total_requests > 0:
            failure_rate = profile.total_failures / profile.total_requests
            if failure_rate >= self.thresholds.failure_rate_critical:
                risk_score += 40
            elif failure_rate >= self.thresholds.failure_rate_warning:
                risk_score += 20
        
        # 计算连续失败风险
        if profile.consecutive_failures >= self.thresholds.consecutive_failures_critical:
            risk_score += 30
        elif profile.consecutive_failures >= self.thresholds.consecutive_failures_warning:
            risk_score += 15
        
        # 计算限流风险
        hour_rate_limits = len(profile.hour_window.rate_limits)
        day_rate_limits = len(profile.day_window.rate_limits)
        
        if day_rate_limits >= self.thresholds.rate_limits_per_day:
            risk_score += 25
        elif hour_rate_limits >= self.thresholds.rate_limits_per_hour:
            risk_score += 15
        
        # 计算认证失败风险
        hour_auth_failures = len(profile.hour_window.auth_failures)
        day_auth_failures = len(profile.day_window.auth_failures)
        
        if day_auth_failures >= self.thresholds.auth_failures_per_day:
            risk_score += 20
        elif hour_auth_failures >= self.thresholds.auth_failures_per_hour:
            risk_score += 10
        
        # 更新风险等级
        old_risk_level = profile.risk_level
        if risk_score >= 80:
            profile.risk_level = RiskLevel.CRITICAL
        elif risk_score >= 60:
            profile.risk_level = RiskLevel.HIGH
        elif risk_score >= 40:
            profile.risk_level = RiskLevel.MEDIUM
        elif risk_score >= 20:
            profile.risk_level = RiskLevel.LOW
        else:
            profile.risk_level = RiskLevel.SAFE
        
        # 记录风险评分历史
        profile.risk_scores.append((datetime.now(), risk_score))
        if len(profile.risk_scores) > 100:  # 只保留最近100条记录
            profile.risk_scores.pop(0)
        
        profile.last_risk_assessment = datetime.now()
        
        # 如果风险等级发生变化，采取保护措施
        if old_risk_level != profile.risk_level:
            await self._apply_protection_action(profile)
            logger.warning(f"Account {profile.account_id} risk level changed: {old_risk_level.value} -> {profile.risk_level.value}")
    
    async def _apply_protection_action(self, profile: AccountRiskProfile):
        """应用保护措施"""
        old_action = profile.protection_action
        
        if profile.risk_level == RiskLevel.CRITICAL:
            profile.protection_action = ProtectionAction.QUARANTINE
            profile.is_quarantined = True
            profile.quarantine_until = datetime.now() + timedelta(hours=2)
            self.global_stats["accounts_quarantined"] += 1
            
        elif profile.risk_level == RiskLevel.HIGH:
            profile.protection_action = ProtectionAction.COOLDOWN
            profile.cooldown_until = datetime.now() + timedelta(minutes=30)
            
        elif profile.risk_level == RiskLevel.MEDIUM:
            profile.protection_action = ProtectionAction.TEMPORARY_PAUSE
            profile.cooldown_until = datetime.now() + timedelta(minutes=10)
            
        elif profile.risk_level == RiskLevel.LOW:
            profile.protection_action = ProtectionAction.SLOW_DOWN
            
        else:
            profile.protection_action = ProtectionAction.NONE
            profile.cooldown_until = None
        
        if old_action != profile.protection_action:
            self.global_stats["protection_actions_taken"] += 1
            logger.info(f"Applied protection action for {profile.account_id}: {profile.protection_action.value}")
    
    async def _assess_all_accounts_risk(self):
        """评估所有账号的风险"""
        for profile in self.account_profiles.values():
            await self._assess_account_risk(profile)
    
    async def _cleanup_expired_data(self):
        """清理过期数据"""
        current_time = time.time()
        
        for profile in self.account_profiles.values():
            profile.minute_window.cleanup_old_records(current_time, 60)
            profile.hour_window.cleanup_old_records(current_time, 3600)
            profile.day_window.cleanup_old_records(current_time, 86400)
            
            # 清理过期的风险评分历史
            cutoff_time = datetime.now() - timedelta(days=7)
            profile.risk_scores = [
                (timestamp, score) for timestamp, score in profile.risk_scores
                if timestamp > cutoff_time
            ]
    
    def _get_or_create_profile(self, account_id: str) -> AccountRiskProfile:
        """获取或创建账号风险档案"""
        if account_id not in self.account_profiles:
            self.account_profiles[account_id] = AccountRiskProfile(account_id=account_id)
        return self.account_profiles[account_id]
    
    async def get_account_risk_report(self, account_id: str) -> Dict:
        """获取账号风险报告"""
        profile = self._get_or_create_profile(account_id)
        
        return {
            "account_id": account_id,
            "risk_level": profile.risk_level.value,
            "protection_action": profile.protection_action.value,
            "is_quarantined": profile.is_quarantined,
            "quarantine_until": profile.quarantine_until.isoformat() if profile.quarantine_until else None,
            "cooldown_until": profile.cooldown_until.isoformat() if profile.cooldown_until else None,
            "statistics": {
                "total_requests": profile.total_requests,
                "total_failures": profile.total_failures,
                "consecutive_failures": profile.consecutive_failures,
                "failure_rate": profile.total_failures / profile.total_requests if profile.total_requests > 0 else 0,
                "last_request": profile.last_request_time.isoformat() if profile.last_request_time else None
            },
            "recent_activity": {
                "requests_last_hour": len(profile.hour_window.timestamps),
                "failures_last_hour": len(profile.hour_window.failures),
                "rate_limits_last_hour": len(profile.hour_window.rate_limits),
                "auth_failures_last_hour": len(profile.hour_window.auth_failures)
            },
            "risk_score_trend": [
                {"timestamp": ts.isoformat(), "score": score}
                for ts, score in profile.risk_scores[-10:]  # 最近10条记录
            ]
        }
    
    async def get_global_risk_report(self) -> Dict:
        """获取全局风险报告"""
        risk_distribution = defaultdict(int)
        for profile in self.account_profiles.values():
            risk_distribution[profile.risk_level.value] += 1
        
        return {
            "total_accounts": len(self.account_profiles),
            "risk_distribution": dict(risk_distribution),
            "global_stats": self.global_stats.copy(),
            "thresholds": {
                "requests_per_minute": self.thresholds.requests_per_minute,
                "requests_per_hour": self.thresholds.requests_per_hour,
                "requests_per_day": self.thresholds.requests_per_day,
                "failure_rate_warning": self.thresholds.failure_rate_warning,
                "failure_rate_critical": self.thresholds.failure_rate_critical
            }
        }

# 创建全局实例
risk_control_manager = RiskControlManager()
