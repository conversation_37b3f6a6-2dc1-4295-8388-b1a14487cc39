import asyncio
import aiohttp
from typing import Dict, List, Any, Optional
from datetime import datetime
from twikit import Client, User, Tweet
from src.utils.logger import setup_logger
from src.utils.utils import tweet_to_dict, user_to_dict, media_to_dict

logger = setup_logger(module_name="api_helpers")

# 为了保持向后兼容性，提供别名函数
def convert_tweet_to_dict(tweet: Tweet) -> Dict[str, Any]:
    """将 Tweet 对象转换为字典 - 使用 utils.py 中的统一实现"""
    return tweet_to_dict(tweet)

def convert_user_to_dict(user: User) -> Dict[str, Any]:
    """将 User 对象转换为字典 - 使用 utils.py 中的统一实现"""
    return user_to_dict(user)

def convert_media_to_dict(media) -> Dict[str, Any]:
    """将 Media 对象转换为字典 - 使用 utils.py 中的统一实现"""
    return media_to_dict(media)

async def collect_user_state(screen_name: str, config: Any, client: Client) -> Dict[str, Any]:
    """收集用户当前状态
    
    Args:
        screen_name: 用户名（如 elonmusk）
        config: 配置对象
        client: Twitter API客户端
        
    Returns:
        Dict[str, Any]: 包含用户状态的字典
    """
    state = {
        "profile": None,
        "tweets": set(),
        "following": set(),
        "followers": set(),
        "mentions": set(),
        "likes": set(),
        "retweets": set(),
        "timestamp": datetime.now().isoformat()
    }
    
    try:
        # 获取用户资料
        logger.debug(f"开始获取用户资料: {screen_name}")
        user = await client.get_user_by_screen_name(screen_name)
        if not user:
            raise ValueError(f"找不到用户: {screen_name}")
        user_id = user.id
        logger.debug(f"成功获取用户资料: {user}")
        state["profile"] = convert_user_to_dict(user)
        
        # 获取推文
        if config.monitor_tweets:
            await asyncio.sleep(1)
            logger.debug(f"开始获取推文: {screen_name}")
            tweets = await client.get_user_tweets(user_id, tweet_type="Tweets", count=40)
            logger.debug(f"成功获取推文: {tweets}")
            state["tweets"] = {tweet.id for tweet in tweets}
        
        # 获取关注关系
        if getattr(config, "monitor_relations", False):
            await asyncio.sleep(1)
            try:
                logger.debug(f"开始获取关注关系: {screen_name}")
                following = await client.get_user_following(user_id, count=20)
                logger.debug(f"成功获取关注关系: {following}")
                state["following"] = {user.id for user in following}
            except Exception as e:
                logger.error(f"获取关注关系失败: {e}")
                state["following"] = set()
            
            try:
                logger.debug(f"开始获取粉丝关系: {screen_name}")
                followers = await client.get_user_followers(user_id, count=20) 
                logger.debug(f"成功获取粉丝关系: {followers}")
                state["followers"] = {user.id for user in followers}
            except Exception as e:
                logger.error(f"获取粉丝关系失败: {e}")
                state["followers"] = set()
        
        # 获取提及（暂时跳过，因为API限制）
        if getattr(config, "monitor_mentions", False):
            logger.warning(f"Mentions monitoring is currently not supported for user {screen_name}")
            state["mentions"] = set()
        
        # 获取点赞
        if getattr(config, "monitor_likes", False):
            await asyncio.sleep(1)
            # 使用get_user_tweets的Likes类型获取点赞
            logger.debug(f"开始获取点赞: {screen_name}")
            likes = await client.get_user_tweets(user_id, tweet_type="Likes", count=40)
            logger.debug(f"成功获取点赞: {likes}")
            state["likes"] = {tweet.id for tweet in likes}
        
        # 获取转发
        if getattr(config, "monitor_retweets", False):
            await asyncio.sleep(1)
            # 从普通推文中筛选转发
            logger.debug(f"开始获取转发: {screen_name}")
            tweets = await client.get_user_tweets(user_id, tweet_type="Tweets", count=40)
            logger.debug(f"成功获取推文: {tweets}")
            retweets = [tweet for tweet in tweets if getattr(tweet, "retweeted_status", None)]
            state["retweets"] = {tweet.id for tweet in retweets}
            
    except Exception as e:
        logger.debug(f"Failed to collect user state for {screen_name}: {str(e)}")
        
    return state

def detect_state_changes(last_state: Dict[str, Any], current_state: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """检测状态变化"""
    changes = {}
    
    # 检查个人资料变化
    logger.debug(f"last_state['profile']: {last_state['profile']}")
    logger.debug(f"current_state['profile']: {current_state['profile']}")
    if last_state["profile"] and current_state["profile"]:
        profile_changes = {}
        for field in ["name", "description", "location", "url", "followers_count", "following_count"]:
            old_val = last_state["profile"].get(field)
            new_val = current_state["profile"].get(field)
            if old_val != new_val:
                profile_changes[field] = {"old": old_val, "new": new_val}
        if profile_changes:
            changes["profile"] = profile_changes
    
    # 确保所有状态字段都是集合类型
    logger.debug(f"last_state: {last_state}")
    logger.debug(f"current_state: {current_state}")
    for state in [last_state, current_state]:
        for field in ["tweets", "following", "followers", "mentions", "likes", "retweets"]:
            if isinstance(state[field], list):
                state[field] = set(state[field])
    
    # 检查新推文
    new_tweets = current_state["tweets"] - last_state["tweets"]
    if new_tweets:
        changes["new_tweets"] = list(new_tweets)
    
    # 检查关注关系变化
    if last_state["following"] and current_state["following"]:
        new_following = current_state["following"] - last_state["following"]
        unfollowing = last_state["following"] - current_state["following"]
        if new_following or unfollowing:
            changes["following"] = {
                "new": list(new_following),
                "removed": list(unfollowing)
            }
    
    # 检查粉丝变化
    if last_state["followers"] and current_state["followers"]:
        new_followers = current_state["followers"] - last_state["followers"]
        unfollowers = last_state["followers"] - current_state["followers"]
        if new_followers or unfollowers:
            changes["followers"] = {
                "new": list(new_followers),
                "removed": list(unfollowers)
            }
    
    # 检查新提及
    new_mentions = current_state["mentions"] - last_state["mentions"]
    if new_mentions:
        changes["new_mentions"] = list(new_mentions)
    
    # 检查新点赞
    new_likes = current_state["likes"] - last_state["likes"]
    if new_likes:
        changes["new_likes"] = list(new_likes)
    
    # 检查新转发
    new_retweets = current_state["retweets"] - last_state["retweets"]
    if new_retweets:
        changes["new_retweets"] = list(new_retweets)
    
    return changes if changes else None

async def get_historical_data(user_id: str, days: int = 30) -> List[Dict[str, Any]]:
    """获取历史数据"""
    # 这里应该从数据库中获取历史数据
    # 为演示目的，返回空列表
    # TODO: 实现从数据库获取历史数据的逻辑
    _ = user_id, days  # 避免未使用参数警告
    return []

async def send_notifications(changes: Dict[str, Any], config: Any) -> None:
    """发送通知"""
    try:
        if not config.notify_url:
            return
            
        async with aiohttp.ClientSession() as session:
            await session.post(
                config.notify_url,
                json={
                    "type": "state_change",
                    "changes": changes,
                    "timestamp": datetime.now().isoformat()
                }
            )
    except Exception as e:
        logger.error(f"Failed to send notification: {str(e)}")

async def send_error_notification(user_id: str, error: str, config: Any) -> None:
    """发送错误通知"""
    try:
        if not config.notify_url:
            return
            
        async with aiohttp.ClientSession() as session:
            await session.post(
                config.notify_url,
                json={
                    "type": "error",
                    "user_id": user_id,
                    "error": error,
                    "timestamp": datetime.now().isoformat()
                }
            )
    except Exception as e:
        logger.error(f"Failed to send error notification: {str(e)}") 