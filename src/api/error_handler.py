from fastapi import HTTPException
from functools import wraps
from typing import Callable, Any
from src.utils.logger import setup_logger

logger = setup_logger(module_name="error_handler")

class TwitterAPIErrorHandler:
    @staticmethod
    def classify_error(error: Exception) -> str:
        """统一的错误分类逻辑"""
        error_str = str(error).lower()
        if "429" in error_str or "rate limit" in error_str:
            return "rate_limit"
        elif "401" in error_str or "403" in error_str:
            return "auth_failure"
        elif "404" in error_str or "not found" in error_str:
            return "not_found"
        elif "timeout" in error_str or "connection" in error_str:
            return "network_error"
        elif "suspended" in error_str or "locked" in error_str:
            return "account_suspended"
        return "unknown"
    
    @staticmethod
    def handle_api_error(error: Exception) -> HTTPException:
        """将内部异常转换为 HTTP 异常"""
        error_type = TwitterAPIErrorHandler.classify_error(error)
        if error_type == "rate_limit":
            return HTTPException(status_code=429, detail="Rate limit exceeded")
        elif error_type == "auth_failure":
            return HTTPException(status_code=401, detail="Authentication failed")
        elif error_type == "not_found":
            return HTTPException(status_code=404, detail="Resource not found")
        elif error_type == "network_error":
            return HTTPException(status_code=503, detail="Network error or timeout")
        elif error_type == "account_suspended":
            return HTTPException(status_code=403, detail="Account suspended or locked")
        return HTTPException(status_code=500, detail=str(error))

def handle_twitter_errors(func: Callable) -> Callable:
    """装饰器：统一处理 Twitter API 错误"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except HTTPException:
            # 如果已经是 HTTPException，直接传递
            raise
        except Exception as e:
            logger.error(f"Error in {func.__name__}: {str(e)}")
            raise TwitterAPIErrorHandler.handle_api_error(e)
    return wrapper