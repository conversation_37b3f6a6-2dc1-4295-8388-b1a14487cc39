"""
Twitter API 门面管理器 (Twitter API Facade)

这是 Twitter 账号管理系统的高级接口层，采用门面模式整合多个底层管理组件，
为用户提供统一、简洁、易用的 Twitter API 调用体验。

核心功能：
- 统一的智能请求执行接口
- 自动账号健康度管理和智能选择
- 智能重试策略和错误处理
- 风险控制和安全防护
- 综合报告和系统优化建议
- 便捷的函数式调用接口

集成组件：
- AccountHealthManager: 账号健康度管理
- SmartRetryManager: 智能重试策略
- RiskControlManager: 风险控制管理

使用场景：
- 业务代码中的 Twitter API 调用（推荐）
- 需要自动账号管理和错误处理时
- 需要统一的请求监控和统计时
- 快速原型开发和测试

设计优势：
- 简化接口：隐藏复杂的底层逻辑
- 自动化：无需手动管理账号和重试
- 可靠性：内置错误处理和恢复机制
- 可观测：提供详细的监控和报告

使用示例：
```python
from src.api.twitter_api_facade import smart_execute

# 简单调用
result = await smart_execute(
    get_user_by_username,
    priority="high",
    username="example"
)

# 高级调用
result = await twitter_api_facade.execute_smart_request(
    func=get_user_by_username,
    priority=RequestPriority.HIGH,
    preferred_account_id="account_123",
    username="example"
)
```

注意：
- 这是推荐的主要使用接口
- 自动处理账号选择、重试和错误恢复
- 提供详细的日志和监控信息
"""
from typing import Any, Awaitable, Callable, Dict, List, Optional, TypeVar
from twikit import Client

from src.api.account_health_manager import (
    account_health_manager,
    RequestContext,
    RequestPriority,
    AccountHealthError,
    NoAvailableAccountsError
)
from src.api.retry_strategies import (
    smart_retry_manager,
    RetryConfig,
    RetryStrategy
)
from src.api.risk_control import risk_control_manager
from src.utils.logger import setup_logger

logger = setup_logger(module_name="twitter_api_facade")

T = TypeVar('T')

# 自定义异常类
class TwitterApiFacadeError(Exception):
    """Twitter API 门面管理器相关的基础异常"""
    pass

class RequestExecutionError(TwitterApiFacadeError):
    """请求执行异常"""
    pass

class ClientCreationError(TwitterApiFacadeError):
    """客户端创建异常"""
    pass

class TwitterApiFacade:
    """Twitter API 门面管理器
    
    作为统一的 Twitter API 调用接口，集成以下功能：
    - 账号健康度管理和智能选择
    - 智能重试策略和错误处理
    - 风险控制和安全防护
    - 综合报告和优化建议
    
    使用门面模式整合多个底层管理组件，为用户提供简洁易用的 API。
    """
    
    def __init__(self) -> None:
        """初始化 Twitter API 门面管理器

        集成账号健康度管理器、重试管理器和风险控制管理器。
        """
        self.health_manager = account_health_manager
        self.retry_manager = smart_retry_manager
        self.risk_manager = risk_control_manager

        logger.info("TwitterApiFacade initialized")
    
    async def execute_smart_request(
        self,
        func: Callable[..., Awaitable[T]],
        priority: RequestPriority = RequestPriority.NORMAL,
        max_retries: int = 3,
        timeout: float = 30.0,
        preferred_account_id: Optional[str] = None,
        exclude_account_ids: Optional[List[str]] = None,
        retry_strategy: RetryStrategy = RetryStrategy.ADAPTIVE,
        *args,
        **kwargs
    ) -> T:
        """执行智能请求 - 集成所有功能的主要接口

        这是 TwitterApiFacade 的核心方法，提供完整的智能请求执行功能。
        自动处理账号选择、重试、错误恢复和风险控制。

        Args:
            func: 要执行的异步函数，第一个参数必须是 twikit.Client 实例
            priority: 请求优先级，影响账号选择策略
                - LOW: 可使用健康度较低的账号
                - NORMAL: 正常处理
                - HIGH: 优先使用健康账号
                - CRITICAL: 只使用最健康的账号
            max_retries: 最大重试次数，包括账号切换重试
            timeout: 单次请求超时时间（秒）
            preferred_account_id: 首选账号ID，优先尝试使用此账号
            exclude_account_ids: 排除的账号ID列表，这些账号不会被选择
            retry_strategy: 重试策略
                - FIXED: 固定延迟重试
                - EXPONENTIAL: 指数退避重试
                - ADAPTIVE: 自适应重试（推荐）
            *args, **kwargs: 传递给 func 的其他参数

        Returns:
            T: func 的执行结果

        Raises:
            RuntimeError: 没有可用账号或请求被风险控制拒绝
            TimeoutError: 请求超时
            Exception: func 执行过程中的其他异常

        执行流程：
        1. 创建请求上下文和重试配置
        2. 通过健康度管理器选择最佳账号
        3. 进行风险控制检查
        4. 执行请求并记录结果
        5. 如果失败，根据重试策略进行重试
        6. 更新账号健康度和风险评分

        使用示例：
        ```python
        async def get_user(client, username):
            return await client.get_user_by_username(username)

        result = await facade.execute_smart_request(
            get_user,
            priority=RequestPriority.HIGH,
            username="example"
        )
        ```
        """
        exclude_account_ids = exclude_account_ids or []
        
        # 创建请求上下文
        request_context = RequestContext(
            priority=priority,
            max_retries=max_retries,
            timeout=timeout,
            preferred_account_id=preferred_account_id,
            exclude_account_ids=exclude_account_ids.copy()
        )
        
        # 创建重试配置
        retry_config = RetryConfig(
            strategy=retry_strategy,
            max_attempts=max_retries,
            timeout_per_attempt=timeout,
            enable_account_switching=True
        )
        
        # 定义获取客户端的函数
        async def get_client_func(account_id: Optional[str] = None) -> Client:
            return await self._get_safe_client(request_context, account_id)
        
        # 定义包装后的执行函数
        async def wrapped_func(client: Client, *args, **kwargs) -> T:
            account_id = getattr(client, '_account_id', None)
            
            # 检查请求权限
            if account_id:
                permitted, reason = await self.risk_manager.check_request_permission(account_id)
                if not permitted:
                    raise RuntimeError(f"Request not permitted for account {account_id}: {reason}")
            
            try:
                # 执行实际请求
                logger.debug(f"🚀 Executing {func.__name__}({str(args)}, {str(kwargs)}) with priority={priority.value}")
                result = await func(client, *args, **kwargs)
                logger.debug(f"✅ Success {func.__name__}({str(args)}, {str(kwargs)}) with priority={priority.value}")
                # 记录成功
                if account_id:
                    logger.debug(f"✅ Request successful for account {account_id}")
                    await self.risk_manager.record_request(account_id, success=True)
                
                return result
                
            except Exception as e:
                # 记录失败
                if account_id:
                    error_type = self._classify_error(e)
                    logger.warning(f"❌ Request failed for account {account_id}: {error_type} - {str(e)}")
                    await self.risk_manager.record_request(account_id, success=False, error_type=error_type, err=e, proxy=getattr(client, '_proxy', ''))
                raise
        
        # 使用重试管理器执行请求
        logger.debug(f"🎬 Starting smart_execute with retry manager for {func.__name__}")
        return await self.retry_manager.execute_with_retry(
            wrapped_func,
            get_client_func,
            retry_config,
            *args,
            **kwargs
        )
    
    async def _get_safe_client(self, context: RequestContext, preferred_account: Optional[str] = None) -> Client:
        """获取安全的客户端实例"""
        # 如果指定了首选账号，先尝试使用
        if preferred_account:
            context.preferred_account_id = preferred_account
        
        # 获取最佳账号
        try:
            account_id = await self.health_manager.get_best_account(context)
        except NoAvailableAccountsError as e:
            raise ClientCreationError(f"No available accounts: {e}") from e
        
        # 检查风险控制
        permitted, reason = await self.risk_manager.check_request_permission(account_id)
        if not permitted:
            # 如果首选账号不可用，排除它并重新选择
            context.exclude_account_ids.append(account_id)
            try:
                account_id = await self.health_manager.get_best_account(context)
            except NoAvailableAccountsError as e:
                raise ClientCreationError(f"No safe accounts available. Last reason: {reason}") from e
        
        # 获取客户端
        return await self.health_manager.get_client(account_id)
    
    def _classify_error(self, error: Exception) -> Optional[str]:
        logger.debug(f"🔍 Classifying error: {str(error)}")
        """分类错误类型

        Args:
            error: 要分类的异常对象

        Returns:
            Optional[str]: 错误类型字符串，如果无法分类则返回 "unknown"
                - "rate_limit": 频率限制错误
                - "auth_failure": 认证失败错误
                - "timeout": 超时错误
                - "network": 网络错误
                - "unknown": 未知错误类型
        """
        error_str = str(error).lower()
        
        if "429" in error_str or "rate limit" in error_str:
            return "rate_limit"
        elif "401" in error_str or "403" in error_str or "unauthorized" in error_str:
            return "auth_failure"
        elif "timeout" in error_str:
            return "timeout"
        elif "connection" in error_str or "network" in error_str:
            return "network"
        else:
            return "unknown"
    
    async def get_comprehensive_report(self) -> Dict[str, Any]:
        """获取综合报告"""
        # 获取各个组件的报告
        health_report = await self.health_manager.get_account_health_report()
        risk_report = await self.risk_manager.get_global_risk_report()
        
        return {
            "timestamp": health_report.get("timestamp"),
            "account_health": {
                "total_accounts": health_report["total_accounts"],
                "status_distribution": {
                    "healthy": health_report["healthy_accounts"],
                    "warning": health_report["warning_accounts"],
                    "critical": health_report["critical_accounts"],
                    "suspended": health_report["suspended_accounts"],
                    "banned": health_report["banned_accounts"]
                },
                "performance": {
                    "average_health_score": health_report["average_health_score"],
                    "average_risk_score": health_report["average_risk_score"]
                }
            },
            "risk_control": {
                "risk_distribution": risk_report["risk_distribution"],
                "protection_stats": {
                    "accounts_quarantined": risk_report["global_stats"]["accounts_quarantined"],
                    "protection_actions_taken": risk_report["global_stats"]["protection_actions_taken"]
                }
            },
            "request_stats": health_report["stats"],
            "recommendations": await self._generate_recommendations(health_report, risk_report)
        }
    
    async def _generate_recommendations(self, health_report: Dict, risk_report: Dict) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 基于健康度的建议
        if health_report["banned_accounts"] > 0:
            recommendations.append(f"有 {health_report['banned_accounts']} 个账号被封禁，建议检查账号使用策略")
        
        if health_report["critical_accounts"] > health_report["total_accounts"] * 0.2:
            recommendations.append("超过20%的账号处于危险状态，建议降低请求频率")
        
        if health_report["average_health_score"] < 70:
            recommendations.append("平均健康度较低，建议优化请求策略和增加账号休息时间")
        
        # 基于风险控制的建议
        if risk_report["global_stats"]["accounts_quarantined"] > 0:
            recommendations.append("有账号被隔离，建议检查请求模式是否过于激进")
        
        if risk_report["global_stats"]["total_failures"] > risk_report["global_stats"]["total_requests"] * 0.3:
            recommendations.append("失败率超过30%，建议检查网络连接和代理配置")
        
        # 基于请求统计的建议
        stats = health_report["stats"]
        if stats["rate_limited_requests"] > stats["total_requests"] * 0.1:
            recommendations.append("限流请求超过10%，建议降低请求频率或增加账号数量")
        
        if not recommendations:
            recommendations.append("系统运行良好，继续保持当前策略")
        
        return recommendations

    async def optimize_account_usage(self) -> Dict[str, Any]:
        """优化账号使用策略"""
        health_report = await self.health_manager.get_account_health_report()

        optimization_actions = []

        # 分析账号使用情况
        healthy_accounts = []
        problematic_accounts = []

        for account in health_report["accounts"]:
            if account["health_score"] >= 80:
                healthy_accounts.append(account["account_id"])
            elif account["health_score"] < 50:
                problematic_accounts.append(account["account_id"])

        # 生成优化建议
        if len(problematic_accounts) > 0:
            optimization_actions.append({
                "action": "rest_accounts",
                "accounts": problematic_accounts,
                "reason": "Low health score accounts need rest"
            })

        if len(healthy_accounts) < 3:
            optimization_actions.append({
                "action": "add_accounts",
                "reason": "Need more healthy accounts for load distribution"
            })

        return {
            "healthy_accounts": len(healthy_accounts),
            "problematic_accounts": len(problematic_accounts),
            "optimization_actions": optimization_actions,
            "recommended_priority_distribution": {
                "critical": f"Use top {min(3, len(healthy_accounts))} healthiest accounts",
                "high": f"Use top {min(5, len(healthy_accounts))} healthiest accounts",
                "normal": "Use any available account",
                "low": "Prefer accounts with lower usage"
            }
        }


# 创建全局实例
twitter_api_facade = TwitterApiFacade()

# 便捷函数
async def smart_execute(
    func: Callable[..., Awaitable[T]],
    priority: str = "normal",
    max_retries: int = 3,
    timeout: float = 30.0,
    preferred_account_id: Optional[str] = None,
    *args,
    **kwargs
) -> T:
    """便捷的智能执行函数

    这是 TwitterApiFacade 的便捷函数接口，提供更简单的调用方式。
    内部调用 execute_smart_request 方法，支持字符串形式的优先级参数。

    Args:
        func: 要执行的异步函数，第一个参数必须是 twikit.Client 实例
        priority: 优先级字符串，支持以下值：
            - "low": 低优先级，可使用健康度较低的账号
            - "normal": 普通优先级（默认）
            - "high": 高优先级，优先使用健康账号
            - "critical": 关键优先级，只使用最健康的账号
        max_retries: 最大重试次数，默认3次
        timeout: 单次请求超时时间（秒），默认30秒
        preferred_account_id: 首选账号ID，如果指定则优先使用此账号
        *args, **kwargs: 传递给 func 的其他参数

    Returns:
        T: func 的执行结果

    Raises:
        同 execute_smart_request 方法

    使用示例：
    ```python
    # 简单调用
    result = await smart_execute(get_user_by_username, username="example")

    # 指定优先级
    result = await smart_execute(
        get_user_by_username,
        priority="high",
        username="example"
    )

    # 指定首选账号
    result = await smart_execute(
        get_user_by_username,
        preferred_account_id="account_123",
        username="example"
    )
    ```

    注意：
    - 这是推荐的简单调用方式
    - 优先级字符串会自动转换为 RequestPriority 枚举
    - 无效的优先级字符串会使用默认的 NORMAL 优先级
    """
    priority_map = {
        "low": RequestPriority.LOW,
        "normal": RequestPriority.NORMAL,
        "high": RequestPriority.HIGH,
        "critical": RequestPriority.CRITICAL
    }

    priority_enum = priority_map.get(priority, RequestPriority.NORMAL)
    logger.debug(f'🚀 smart_execute called with max_retries={max_retries}, priority={priority}')
    return await twitter_api_facade.execute_smart_request(
        func=func,
        priority=priority_enum,
        max_retries=max_retries,
        timeout=timeout,
        preferred_account_id=preferred_account_id,
        *args,
        **kwargs
    )
