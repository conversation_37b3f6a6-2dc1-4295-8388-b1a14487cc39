import json
import pathlib
import sqlite3
from fastapi import APIRouter, HTTPException
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime
from src.config import Config
from src.storage.database import Database
from src.proxy.manager import ProxyManager
from src.utils.logger import setup_logger
from src.api.twitter_api_facade import smart_execute
from twikit import Client

router = APIRouter(prefix="/accounts", tags=["accounts"])
logger = setup_logger(module_name="account_routes")
config = Config()
db = Database(config)
proxy_manager = ProxyManager(config.get_sqlite_path())

class AccountCreate(BaseModel):
    account_id: str
    auth_info_1: str
    auth_info_2: str
    password: str
    totp_secret: str
    proxy: Optional[str] = None

class StatusResponse(BaseModel):
    status: str
    last_check: datetime
    is_active: bool
    details: Dict[str, Any]

class ImportAccountsRequest(BaseModel):
    accounts: List[AccountCreate]
    force: Optional[bool] = False
    max_concurrent: Optional[int] = 2

@router.post("/import", response_model=dict)
async def import_accounts(request: ImportAccountsRequest):
    """批量导入账户，登录并保存 Cookies"""
    logger.debug(f"Importing {len(request.accounts)} accounts")
    failed_accounts = []

    async def process_account(account: AccountCreate):
        account_id = account.account_id
        
        # 如果用户提供了代理，直接使用；否则分配一个未使用的代理
        if account.proxy:
            proxy = account.proxy
            # 验证代理分配是否有效
            if not proxy_manager.validate_proxy_assignment(proxy, account_id):
                logger.warning(f"Proxy assignment validation failed for {account_id}: {proxy}")
                # 尝试修复冲突
                conflicts = proxy_manager.get_proxy_assignment_conflicts()
                if conflicts:
                    logger.info(f"Found {len(conflicts)} conflicts, attempting to fix...")
                    proxy_manager.fix_proxy_assignment_conflicts()
            
            # 将用户提供的代理分配给账户
            if not proxy_manager.assign_proxy_to_account(proxy, account_id):
                logger.error(f"Failed to assign provided proxy {proxy} to account {account_id}")
                proxy = None
            logger.info(f"{account.account_id} already with a proxy: {account.proxy}")
        else:
            # 为账户分配一个未使用的代理
            proxy = proxy_manager.get_unassigned_proxy_for_account(account_id)
            if not proxy:
                logger.warning(f"No available proxy for account {account_id}")

        # 检查数据库记录
        with sqlite3.connect(config.get_sqlite_path()) as conn:
            cursor = conn.execute("SELECT cookies, proxy FROM accounts WHERE account_id = ?", (account_id,))
            db_record = cursor.fetchone()

        if db_record and not request.force:
            logger.info(f"Skipping {account_id}: DB record exists")
            return

        # 登录并保存 Cookies
        try:
            from pathlib import Path
            cookies_file = Path(f"data/cookies/{account.account_id}.json")
            client = Client(language='en-US', proxy=account.proxy)
            await client.login(
                auth_info_1=account.auth_info_1,
                auth_info_2=account.auth_info_2,
                password=account.password,
                totp_secret=account.totp_secret,
                cookies_file=str(cookies_file)
            )
            logger.info(f"✅twitter Client login {account.auth_info_1} succeed!")
            with open(cookies_file, 'r') as f:
                cookies = json.load(f)
            await db.save_account(
                account_id=account_id,
                auth_info_1=account.auth_info_1,
                auth_info_2=account.auth_info_2,
                password=account.password,
                totp_secret=account.totp_secret,
                cookies=cookies,
                proxy=proxy or ""
            )
            logger.info(f"✅ Imported {account_id} with proxy: {proxy}\n")
        except Exception as e:
            # 如果导入失败，释放代理分配
            if proxy and not account.proxy:  # 只有自动分配的代理才释放
                proxy_manager.release_proxy_from_account(account_id)
                
            error_info = {
                "account_id": account_id,
                "auth_info_1": account.auth_info_1,
                "auth_info_2": account.auth_info_2,
                "password": account.password,
                "totp_secret": account.totp_secret,
                "proxy": proxy,
                "error": str(e)
            }
            failed_accounts.append(error_info)
            logger.error(f"❌ Login failed for {account_id}: {json.dumps(error_info, indent=2)}")

    # 并发处理账户
    import asyncio
    semaphore = asyncio.Semaphore(request.max_concurrent or 2)
    async def process_account_sema(account):
        async with semaphore:
            await process_account(account)

    tasks = [process_account_sema(account) for account in request.accounts]
    await asyncio.gather(*tasks)

    # 保存失败账户到 failed.json
    if failed_accounts:
        from pathlib import Path
        failed_file = Path("data/accounts/failed.json")
        existing_failed = []
        if failed_file.exists():
            with open(failed_file, 'r') as f:
                existing_failed = json.load(f)
        existing_failed.extend(failed_accounts)
        with open(failed_file, 'w') as f:
            json.dump(existing_failed, f, indent=2)

    return {"imported": len(request.accounts) - len(failed_accounts), "failed": len(failed_accounts)}

@router.post("/import_one", response_model=dict)
async def import_account(account: AccountCreate, force: Optional[bool] = False):
    """导入单个账户，登录并保存 Cookies"""
    logger.debug(f"Importing account: {account.account_id}")
    failed_accounts = []
    
    account_id = account.account_id
    
    # 如果用户提供了代理，直接使用；否则分配一个未使用的代理
    if account.proxy:
        proxy = account.proxy
        # 验证代理分配是否有效
        if not proxy_manager.validate_proxy_assignment(proxy, account_id):
            logger.warning(f"Proxy assignment validation failed for {account_id}: {proxy}")
            # 尝试修复冲突
            conflicts = proxy_manager.get_proxy_assignment_conflicts()
            if conflicts:
                logger.info(f"Found {len(conflicts)} conflicts, attempting to fix...")
                proxy_manager.fix_proxy_assignment_conflicts()
        
        # 将用户提供的代理分配给账户
        if not proxy_manager.assign_proxy_to_account(proxy, account_id):
            logger.error(f"Failed to assign provided proxy {proxy} to account {account_id}")
            return {"imported": 0, "failed": 1, "error": "Failed to assign provided proxy"}
    else:
        # 为账户分配一个未使用的代理
        proxy = proxy_manager.get_unassigned_proxy_for_account(account_id)
        if not proxy:
            logger.warning(f"No available proxy for account {account_id}")
            return {"imported": 0, "failed": 1, "error": "No available proxy"}

    # 检查数据库记录
    with sqlite3.connect(config.get_sqlite_path()) as conn:
        cursor = conn.execute("SELECT cookies, proxy FROM accounts WHERE account_id = ?", (account.account_id,))
        db_record = cursor.fetchone()

    if db_record and not force:
        logger.debug(f"Skipping {account.account_id}: DB record exists")
        return {"imported": 0, "failed": 0, "skipped": 1}

    # 登录并保存 Cookies
    try:
        await db.save_account(
            account_id=account.account_id,
            auth_info_1=account.auth_info_1,
            auth_info_2=account.auth_info_2,
            password=account.password,
            totp_secret=account.totp_secret,
            proxy=proxy or ""
        )
        logger.info(f"✅ Imported {account.account_id} with proxy: {proxy}")
        return {"imported": 1, "failed": 0}
    except Exception as e:
        # 如果导入失败，释放代理分配
        if proxy and not account.proxy:  # 只有自动分配的代理才释放
            proxy_manager.release_proxy_from_account(account_id)
            
        error_info = {
            "account_id": account.account_id,
            "auth_info_1": account.auth_info_1,
            "auth_info_2": account.auth_info_2,
            "password": account.password,
            "totp_secret": account.totp_secret,
            "proxy": proxy,
            "error": str(e)
        }
        failed_accounts.append(error_info)
        logger.error(f"❌ Login failed for {account.account_id}: {json.dumps(error_info, indent=2)}")
        # 保存失败账户到 failed.json
        from pathlib import Path
        failed_file = Path("data/accounts/failed.json")
        existing_failed = []
        if failed_file.exists():
            with open(failed_file, 'r') as f:
                existing_failed = json.load(f)
        existing_failed.extend(failed_accounts)
        with open(failed_file, 'w') as f:
            json.dump(existing_failed, f, indent=2)
        return {"imported": 0, "failed": 1}

@router.get("/{account_id}/status")
async def check_account_status(account_id: str):
    """检查账户状态"""
    try:
        # 使用 AccountManager 获取客户端并检查状态
        async def check_status(client: Client, account_id):
            user = await client.get_user_by_id(account_id)
            return {
                "screen_name": user.screen_name,
                "followers_count": user.followers_count,
                "following_count": user.following_count
            }
            
        details = await smart_execute(check_status, priority="normal", account_id=account_id)
        
        return StatusResponse(
            status="active",
            last_check=datetime.now(),
            is_active=True,
            details=details
        )
        
    except Exception as e:
        logger.error(f"Failed to check account status: {e}")
        raise HTTPException(status_code=500, detail=str(e)) 