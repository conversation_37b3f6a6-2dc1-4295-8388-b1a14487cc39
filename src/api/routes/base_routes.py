from fastapi import APIRouter, HTTPException
from fastapi.responses import HTMLResponse
import psutil
from datetime import datetime
import sqlite3
from src.config import Config
from src.utils.logger import setup_logger
from src.utils.decorators import monitor_performance

router = APIRouter()
logger = setup_logger(module_name="base_routes")
config = Config()

@router.get("/")
def render_home():
    """渲染主页"""
    title = "<h2>Twx</h2>"
    desc = "<p>Advanced Twitter monitor,search,analysis open source tool<p>"
    wrapper = f"<div style='margin-top:60px;display:flex;justify-content:center;align-items:center;flex-direction: column;' >{title}{desc}</div>"
    return HTMLResponse(wrapper)

@router.get("/health")
@monitor_performance()
async def health_check():
    """系统健康检查"""
    try:
        # 检查数据库连接
        db_status = "healthy"
        try:
            with sqlite3.connect(config.get_sqlite_path()) as conn:
                conn.execute("SELECT 1")
        except Exception as e:
            db_status = f"error: {str(e)}"

        # 检查代理池状态
        proxy_status = "healthy"
        try:
            with sqlite3.connect(config.get_sqlite_path()) as conn:
                cursor = conn.execute("SELECT COUNT(*) FROM proxies WHERE status = 'active'")
                proxy_count = cursor.fetchone()[0]
        except Exception as e:
            proxy_status = f"error: {str(e)}"
            proxy_count = 0

        # 获取系统资源使用情况
        process = psutil.Process()
        memory_info = process.memory_info()

        return {
            "status": "healthy",
            "version": "1.0.0",
            "components": {
                "database": db_status,
                "proxy_pool": {
                    "status": proxy_status,
                    "active_proxies": proxy_count
                },
                "system": {
                    "cpu_percent": process.cpu_percent(),
                    "memory_usage": memory_info.rss / 1024 / 1024,  # MB
                    "memory_percent": process.memory_percent()
                }
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 