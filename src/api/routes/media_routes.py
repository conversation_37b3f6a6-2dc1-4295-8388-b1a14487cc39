import io
import aiohttp
from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import StreamingResponse
from typing import Optional
from twikit import Tweet, Client
from src.utils.logger import setup_logger
from src.api.twitter_api_facade import smart_execute
from src.utils.utils import media_to_dict

router = APIRouter(prefix="/media", tags=["media"])
logger = setup_logger(module_name="media_routes")


@router.get("/tweets/{tweet_id}")
async def get_tweet_media(
    tweet_id: str,
    priority: str = Query("normal", description="Request priority: low, normal, high, critical")
):
    """获取推文中的媒体资源信息 - 使用智能账号管理"""
    logger.debug(f"Getting media for tweet {tweet_id} with priority={priority}")

    try:
        async def fetch_tweet(client: Client, tweet_id) -> Tweet:
            logger.debug(f"Fetching tweet {tweet_id} with account {getattr(client, '_account_id', 'unknown')}")
            return await client.get_tweet_by_id(tweet_id)

        tweet = await smart_execute(
            fetch_tweet,
            priority=priority,
            tweet_id=tweet_id
        )

        if not tweet:
            logger.warning(f"Tweet {tweet_id} not found")
            raise HTTPException(status_code=404, detail="Tweet not found")

        media_list = []
        if hasattr(tweet, "media"):
            logger.debug(f"Found {len(tweet.media)} media items in tweet {tweet_id}")
            for media in tweet.media:
                media_info = media_to_dict(media)
                media_list.append(media_info)
        else:
            logger.debug(f"No media found in tweet {tweet_id}")

        logger.info(f"Successfully retrieved {len(media_list)} media items for tweet {tweet_id}")
        return {
            "media": media_list,
            "count": len(media_list),
            "request_info": {
                "priority": priority,
                "tweet_id": tweet_id
            }
        }

    except Exception as e:
        logger.error(f"Failed to get media for tweet {tweet_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/download/{media_id}")
async def download_media(
    media_id: str,
    media_type: str = Query("image", description="Media type: image or video")
):
    """下载媒体文件 - 直接下载，不需要Twitter API"""
    logger.debug(f"Downloading media {media_id} of type {media_type}")

    try:
        async with aiohttp.ClientSession() as session:
            if media_type == "image":
                url = f"https://pbs.twimg.com/media/{media_id}?format=jpg&name=large"
                filename = f"{media_id}.jpg"
            else:
                url = f"https://video.twimg.com/tweet_video/{media_id}.mp4"
                filename = f"{media_id}.mp4"

            logger.debug(f"Downloading from URL: {url}")

            async with session.get(url) as response:
                if response.status != 200:
                    logger.error(f"Failed to download media {media_id}: HTTP {response.status}")
                    raise HTTPException(status_code=response.status, detail="Failed to download media")

                content = await response.read()
                content_type = response.headers.get("content-type", "application/octet-stream")

                logger.info(f"Successfully downloaded media {media_id} ({len(content)} bytes)")

                return StreamingResponse(
                    io.BytesIO(content),
                    media_type=content_type,
                    headers={"Content-Disposition": f'attachment; filename="{filename}"'}
                )

    except Exception as e:
        logger.error(f"Failed to download media {media_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))