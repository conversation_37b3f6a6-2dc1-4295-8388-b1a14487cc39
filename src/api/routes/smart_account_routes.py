"""
智能账号管理API路由
提供账号健康度监控、智能请求路由等功能的API接口
"""

from fastapi import APIRouter, HTTPException, Query
from typing import Optional, Dict, Any, List
from pydantic import BaseModel
from datetime import datetime

from src.api.account_health_manager import account_health_manager, RequestContext, RequestPriority
from src.utils.logger import setup_logger

logger = setup_logger(module_name="smart_account_routes")
router = APIRouter(prefix="/smart-accounts", tags=["smart-accounts"])

class SmartRequestModel(BaseModel):
    """智能请求模型"""
    priority: str = "normal"  # low, normal, high, critical
    max_retries: int = 3
    timeout: float = 30.0
    allow_degraded_accounts: bool = True
    preferred_account_id: Optional[str] = None
    exclude_account_ids: List[str] = []

class TestRequestModel(BaseModel):
    """测试请求模型"""
    username: str
    request_config: SmartRequestModel = SmartRequestModel()

@router.get("/health-report")
async def get_health_report():
    """获取所有账号的健康度报告"""
    try:
        report = await account_health_manager.get_account_health_report()
        return {
            "success": True,
            "data": report,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to get health report: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/best-account")
async def get_best_account(
    priority: str = Query("normal", description="Request priority: low, normal, high, critical"),
    exclude_accounts: str = Query("", description="Comma-separated account IDs to exclude"),
    preferred_account: Optional[str] = Query(None, description="Preferred account ID")
):
    """获取最适合的账号"""
    try:
        # 解析优先级
        priority_map = {
            "low": RequestPriority.LOW,
            "normal": RequestPriority.NORMAL,
            "high": RequestPriority.HIGH,
            "critical": RequestPriority.CRITICAL
        }
        
        if priority not in priority_map:
            raise HTTPException(status_code=400, detail="Invalid priority")
        
        # 创建请求上下文
        context = RequestContext(
            priority=priority_map[priority],
            preferred_account_id=preferred_account,
            exclude_account_ids=exclude_accounts.split(",") if exclude_accounts else []
        )
        
        # 获取最佳账号
        best_account = await account_health_manager.get_best_account(context)
        
        if not best_account:
            return {
                "success": False,
                "message": "No available accounts found",
                "account_id": None
            }
        
        return {
            "success": True,
            "account_id": best_account,
            "priority": priority,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get best account: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/test-request")
async def test_smart_request(request: TestRequestModel):
    """测试智能请求功能"""
    try:
        # 解析优先级
        priority_map = {
            "low": RequestPriority.LOW,
            "normal": RequestPriority.NORMAL,
            "high": RequestPriority.HIGH,
            "critical": RequestPriority.CRITICAL
        }
        
        priority = priority_map.get(request.request_config.priority, RequestPriority.NORMAL)
        
        # 创建请求上下文
        context = RequestContext(
            priority=priority,
            max_retries=request.request_config.max_retries,
            timeout=request.request_config.timeout,
            allow_degraded_accounts=request.request_config.allow_degraded_accounts,
            preferred_account_id=request.request_config.preferred_account_id,
            exclude_account_ids=request.request_config.exclude_account_ids
        )
        
        # 定义测试函数
        async def test_get_user(client, username: str):
            """测试获取用户信息"""
            user = await client.get_user_by_username(username)
            return {
                "id": user.id,
                "username": user.username,
                "display_name": user.name,
                "followers_count": user.followers_count,
                "following_count": user.following_count,
                "verified": getattr(user, 'verified', False)
            }
        
        # 执行智能请求
        result = await account_health_manager.execute_with_smart_retry(
            test_get_user,
            context,
            request.username
        )
        
        return {
            "success": True,
            "data": result,
            "request_config": {
                "priority": request.request_config.priority,
                "max_retries": request.request_config.max_retries,
                "timeout": request.request_config.timeout
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Test request failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "timestamp": datetime.now().isoformat()
        }

@router.get("/account/{account_id}/health")
async def get_account_health(account_id: str):
    """获取指定账号的健康度信息"""
    try:
        report = await account_health_manager.get_account_health_report()
        
        # 查找指定账号
        account_info = None
        for account in report["accounts"]:
            if account["account_id"] == account_id:
                account_info = account
                break
        
        if not account_info:
            raise HTTPException(status_code=404, detail="Account not found")
        
        return {
            "success": True,
            "data": account_info,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get account health: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/account/{account_id}/reset-health")
async def reset_account_health(account_id: str):
    """重置指定账号的健康度数据"""
    try:
        # 检查账号是否存在
        if account_id not in account_health_manager._account_health:
            raise HTTPException(status_code=404, detail="Account not found")

        # 重置健康度数据
        health = account_health_manager._account_health[account_id]
        health.consecutive_failures = 0
        health.rate_limit_count = 0
        health.auth_failure_count = 0
        health.network_error_count = 0
        health.cooldown_until = None
        health.health_score = 100.0
        health.risk_score = 0.0
        
        # 重新计算状态
        await account_health_manager._calculate_health_score(health)
        await account_health_manager._calculate_risk_score(health)
        await account_health_manager._update_account_status(health)
        
        return {
            "success": True,
            "message": f"Account {account_id} health data reset successfully",
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to reset account health: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stats")
async def get_smart_manager_stats():
    """获取智能管理器统计信息"""
    try:
        report = await account_health_manager.get_account_health_report()

        return {
            "success": True,
            "data": {
                "account_summary": {
                    "total": report["total_accounts"],
                    "healthy": report["healthy_accounts"],
                    "warning": report["warning_accounts"],
                    "critical": report["critical_accounts"],
                    "suspended": report["suspended_accounts"],
                    "banned": report["banned_accounts"]
                },
                "performance": {
                    "average_health_score": report["average_health_score"],
                    "average_risk_score": report["average_risk_score"]
                },
                "request_stats": report["stats"]
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/priority-guide")
async def get_priority_guide():
    """获取请求优先级使用指南"""
    return {
        "success": True,
        "data": {
            "priorities": {
                "low": {
                    "description": "低优先级请求，可以使用健康度较低的账号",
                    "use_cases": ["批量数据收集", "非关键信息获取", "定期监控任务"],
                    "account_requirements": "任何非封禁状态的账号"
                },
                "normal": {
                    "description": "普通优先级请求，使用健康度中等以上的账号",
                    "use_cases": ["常规API调用", "用户信息查询", "推文搜索"],
                    "account_requirements": "健康度 >= 50分的账号"
                },
                "high": {
                    "description": "高优先级请求，优先使用健康账号",
                    "use_cases": ["重要数据获取", "实时监控", "关键业务操作"],
                    "account_requirements": "健康度 >= 70分的账号"
                },
                "critical": {
                    "description": "关键优先级请求，只使用最健康的账号",
                    "use_cases": ["紧急数据获取", "VIP用户请求", "系统关键操作"],
                    "account_requirements": "健康度 >= 90分的健康状态账号"
                }
            },
            "best_practices": [
                "根据业务重要性选择合适的优先级",
                "关键操作使用critical优先级",
                "批量操作使用low优先级以保护健康账号",
                "定期检查账号健康度报告",
                "合理设置重试次数和超时时间"
            ]
        }
    }

@router.get("/optimization-suggestions")
async def get_optimization_suggestions():
    """获取系统优化建议"""
    try:
        from src.api.twitter_api_facade import twitter_api_facade
        suggestions = await twitter_api_facade.optimize_account_usage()
        return {
            "success": True,
            "data": suggestions
        }
    except Exception as e:
        logger.error(f"Failed to get optimization suggestions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/risk-report")
async def get_risk_report():
    """获取风险控制报告"""
    try:
        from src.api.risk_control import risk_control_manager
        report = await risk_control_manager.get_global_risk_report()
        return {
            "success": True,
            "data": report,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to get risk report: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/account/{account_id}/risk-profile")
async def get_account_risk_profile(account_id: str):
    """获取指定账号的风险画像"""
    try:
        report = await account_health_manager.get_account_health_report()
        
        # 查找指定账号
        account_info = None
        for account in report["accounts"]:
            if account["account_id"] == account_id:
                account_info = account
                break
        
        if not account_info:
            raise HTTPException(status_code=404, detail="Account not found")
        
        # 构建风险画像
        risk_profile = {
            "account_id": account_info["account_id"],
            "risk_level": "low" if account_info["risk_score"] < 30 else 
                         "medium" if account_info["risk_score"] < 60 else 
                         "high" if account_info["risk_score"] < 80 else "extreme",
            "risk_score": account_info["risk_score"],
            "health_score": account_info["health_score"],
            "success_rate": account_info["success_rate"],
            "consecutive_failures": account_info["consecutive_failures"],
            "status": account_info["status"],
            "recommendations": []
        }
        
        # 生成建议
        if account_info["risk_score"] > 60:
            risk_profile["recommendations"].append("建议暂停使用此账号，等待风险降低")
        elif account_info["consecutive_failures"] > 5:
            risk_profile["recommendations"].append("连续失败次数过多，建议检查账号状态")
        elif account_info["success_rate"] < 80:
            risk_profile["recommendations"].append("成功率较低，建议减少使用频率")
        
        return {
            "success": True,
            "data": risk_profile,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get account risk profile: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 健康度监控任务控制接口
@router.post("/monitor/start")
async def start_health_monitor():
    """启动健康度监控后台任务"""
    try:
        success = await account_health_manager.start_health_monitor()
        if success:
            return {
                "success": True,
                "message": "Health monitor started successfully",
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail="Failed to start health monitor")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start health monitor: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/monitor/stop")
async def stop_health_monitor():
    """停止健康度监控后台任务"""
    try:
        success = await account_health_manager.stop_health_monitor()
        if success:
            return {
                "success": True,
                "message": "Health monitor stopped successfully",
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail="Failed to stop health monitor")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to stop health monitor: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/monitor/status")
async def get_monitor_status():
    """获取健康度监控任务状态"""
    try:
        status = await account_health_manager.get_monitor_status()
        return {
            "success": True,
            "data": status,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to get monitor status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/monitor/trigger-update")
async def trigger_health_update():
    """手动触发一次健康度更新"""
    try:
        success = await account_health_manager.trigger_health_update()
        if success:
            return {
                "success": True,
                "message": "Health update triggered successfully",
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail="Failed to trigger health update")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to trigger health update: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 风险控制后台任务管理接口
@router.post("/risk-control/start")
async def start_risk_control():
    """启动风险控制后台任务"""
    try:
        from src.api.risk_control import risk_control_manager
        success = await risk_control_manager.start_all_background_tasks()
        if success:
            return {
                "success": True,
                "message": "Risk control background tasks started successfully",
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail="Failed to start risk control tasks")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start risk control tasks: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/risk-control/stop")
async def stop_risk_control():
    """停止风险控制后台任务"""
    try:
        from src.api.risk_control import risk_control_manager
        success = await risk_control_manager.stop_all_background_tasks()
        if success:
            return {
                "success": True,
                "message": "Risk control background tasks stopped successfully",
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail="Failed to stop risk control tasks")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to stop risk control tasks: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/risk-control/status")
async def get_risk_control_status():
    """获取风险控制后台任务状态"""
    try:
        from src.api.risk_control import risk_control_manager
        status = await risk_control_manager.get_background_tasks_status()
        return {
            "success": True,
            "data": status,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to get risk control status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/risk-control/trigger-assessment")
async def trigger_risk_assessment():
    """手动触发一次风险评估"""
    try:
        from src.api.risk_control import risk_control_manager
        success = await risk_control_manager.trigger_risk_assessment()
        if success:
            return {
                "success": True,
                "message": "Risk assessment triggered successfully",
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail="Failed to trigger risk assessment")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to trigger risk assessment: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/risk-control/trigger-cleanup")
async def trigger_risk_cleanup():
    """手动触发一次数据清理"""
    try:
        from src.api.risk_control import risk_control_manager
        success = await risk_control_manager.trigger_cleanup()
        if success:
            return {
                "success": True,
                "message": "Risk cleanup triggered successfully",
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail="Failed to trigger risk cleanup")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to trigger risk cleanup: {e}")
        raise HTTPException(status_code=500, detail=str(e))
