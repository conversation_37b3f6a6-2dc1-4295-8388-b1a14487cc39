import time
import random
import asyncio
import aiohttp
import sqlite3
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime
from twikit import Client
from src.utils.logger import setup_logger
from src.storage.database import Database
from src.config import Config

logger = setup_logger(module_name="health_check")

router = APIRouter(tags=["health-check"])
config = Config()
db = Database(config)

class HealthCheckRequest(BaseModel):
    concurrency: int
    proxy_id: Optional[str] = None
    account_id: Optional[str] = None

class ProxyStatus(BaseModel):
    id: str
    proxy_address: str
    port: int
    status: str = "unknown"
    lastCheck: str = datetime.now().isoformat()
    responseTime: Optional[int] = None
    error: Optional[str] = None

class AccountStatus(BaseModel):
    account_id: str
    status: str = "unknown"
    lastCheck: str = datetime.now().isoformat()
    proxyStatus: Optional[str] = None
    error: Optional[str] = None

# API 路由
@router.get("/proxies")
async def get_proxies():
    """获取所有代理列表（从数据库）"""
    logger.info("获取代理列表")
    try:
        # 从数据库获取所有账户的代理信息
        with sqlite3.connect(config.get_sqlite_path()) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("SELECT account_id, proxy FROM accounts WHERE proxy IS NOT NULL AND proxy != ''")
            proxy_data = []
            for row in cursor.fetchall():
                if row["proxy"]:
                    proxy_data.append({
                        "account_id": row["account_id"],
                        "proxy": row["proxy"],
                        "status": "unknown",
                        "lastCheck": datetime.now().isoformat()
                    })
        return proxy_data
    except Exception as e:
        logger.error(f"获取代理列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/accounts")
async def get_accounts():
    """获取所有账户列表（从数据库）"""
    logger.info("获取账户列表")
    try:
        accounts = db.get_active_accounts()
        account_list = []
        for account in accounts:
            account_list.append({
                "account_id": account["account_id"],
                "proxy": account.get("proxy"),
                "status": account.get("status", "unknown"),
                "lastCheck": datetime.now().isoformat()
            })
        return account_list
    except Exception as e:
        logger.error(f"获取账户列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/accounts/{account_id}")
async def get_account(account_id: str):
    """获取指定账户信息（从数据库）"""
    logger.info(f"获取账户信息: {account_id}")
    try:
        account = db.get_account(account_id)
        if not account:
            logger.warning(f"账户不存在: {account_id}")
            raise HTTPException(status_code=404, detail="账户不存在")

        return {
            "account_id": account["account_id"],
            "proxy": account.get("proxy"),
            "status": account.get("status", "unknown"),
            "lastCheck": datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取账户信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/proxies/{proxy_id}")
async def get_proxy(proxy_id: str):
    """获取指定代理信息（通过账户ID查找）"""
    logger.info(f"获取代理信息: {proxy_id}")
    try:
        # 这里proxy_id实际上是account_id，因为代理是通过账户关联的
        account = db.get_account(proxy_id)
        if not account or not account.get("proxy"):
            logger.warning(f"代理不存在: {proxy_id}")
            raise HTTPException(status_code=404, detail="代理不存在")

        return {
            "account_id": account["account_id"],
            "proxy": account["proxy"],
            "status": "unknown",
            "lastCheck": datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取代理信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/health-check/start")
async def start_health_check(request: HealthCheckRequest):
    """启动健康检测（使用数据库）"""
    logger.info(f"开始健康检测 - 并发数: {request.concurrency}, 代理ID: {request.proxy_id}, 账户ID: {request.account_id}")
    if request.concurrency < 1 or request.concurrency > 50:
        logger.warning(f"无效的并发数: {request.concurrency}")
        raise HTTPException(status_code=400, detail="并发数必须在1-50之间")

    try:
        # 直接执行健康检测并返回结果
        await run_health_check(
            request.concurrency,
            request.proxy_id,
            request.account_id
        )
        return {"message": "健康检测完成"}
    except Exception as e:
        logger.error(f"健康检测失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/accounts/{account_id}/check")
async def check_single_account(account_id: str):
    """检测单个账户（使用数据库）"""
    logger.info(f"开始检测单个账户: {account_id}")
    try:
        account = db.get_account(account_id)
        if not account:
            logger.warning(f"账户不存在: {account_id}")
            raise HTTPException(status_code=404, detail="账户不存在")

        # 执行检测并等待结果
        updated_account = await check_account_from_db(account)

        logger.info(f"账户检测完成: {account_id}, 状态: {updated_account['status']}")
        return updated_account
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检测账户失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/proxies/{account_id}/check")
async def check_single_proxy(account_id: str):
    """检测单个代理（通过账户ID）"""
    logger.info(f"开始检测单个代理: {account_id}")
    try:
        account = db.get_account(account_id)
        if not account or not account.get("proxy"):
            logger.warning(f"账户或代理不存在: {account_id}")
            raise HTTPException(status_code=404, detail="账户或代理不存在")

        # 执行检测并等待结果
        updated_proxy = await check_proxy_from_db(account)

        logger.info(f"代理检测完成: {account_id}, 状态: {updated_proxy['status']}")
        return updated_proxy
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检测代理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/accounts/check-all")
async def check_all_accounts(request: HealthCheckRequest):
    """批量检测所有账户（使用数据库）"""
    logger.info(f"开始批量检测账户 - 并发数: {request.concurrency}")
    if request.concurrency < 1 or request.concurrency > 50:
        logger.warning(f"无效的并发数: {request.concurrency}")
        raise HTTPException(status_code=400, detail="并发数必须在1-50之间")

    try:
        # 直接执行检测并返回结果
        results = await run_accounts_check(request.concurrency)
        return {
            "message": "账户批量检测完成",
            "total_checked": len(results),
            "results": results
        }
    except Exception as e:
        logger.error(f"批量检测账户失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/proxies/check-all")
async def check_all_proxies(request: HealthCheckRequest):
    """批量检测所有代理（使用数据库）"""
    logger.info(f"开始批量检测代理 - 并发数: {request.concurrency}")
    if request.concurrency < 1 or request.concurrency > 50:
        logger.warning(f"无效的并发数: {request.concurrency}")
        raise HTTPException(status_code=400, detail="并发数必须在1-50之间")

    try:
        # 直接执行检测并返回结果
        results = await run_proxies_check(request.concurrency)
        return {
            "message": "代理批量检测完成",
            "total_checked": len(results),
            "results": results
        }
    except Exception as e:
        logger.error(f"批量检测代理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 代理检测配置
PROXY_TEST_CONFIG = {
    "max_retries": 3,
    "timeout": 10,
    "test_urls": [
        "http://httpbin.org/ip",
        "https://api.ipify.org?format=json",
        "https://api.myip.com",
        "https://wtfismyip.com/json",
        "https://api.iplocation.net/?cmd=get-ip",
        "https://ip-api.com/json",
        "https://ipwho.is",
        "https://get.geojs.io/v1/ip.json",
        "https://ipapi.co/json",
        "https://jsonip.com"
    ]
}

async def test_proxy(proxy_url: str, session: aiohttp.ClientSession) -> dict:
    """测试代理是否可用"""
    for attempt in range(1, PROXY_TEST_CONFIG["max_retries"] + 1):
        try:
            timeout = aiohttp.ClientTimeout(total=PROXY_TEST_CONFIG["timeout"])
            test_url = random.choice(PROXY_TEST_CONFIG["test_urls"])
            start_time = time.time()
            
            logger.debug(f"测试代理 {proxy_url} - 尝试 {attempt}/{PROXY_TEST_CONFIG['max_retries']} - URL: {test_url}")
            
            async with session.get(test_url, proxy=proxy_url, timeout=timeout) as response:
                response_time = time.time() - start_time
                if response.status == 200:
                    try:
                        ip_info = await response.json()
                        logger.info(f"代理 {proxy_url} 测试成功 - 延迟: {round(response_time * 1000, 2)}ms")
                        return {
                            "status": "active",
                            "latency": round(response_time * 1000, 2),
                            "ip": ip_info.get("ip", "unknown"),
                            "location": ip_info.get("country", "unknown"),
                            "attempt": attempt,
                            "test_url": test_url
                        }
                    except:
                        logger.warning(f"代理 {proxy_url} 响应解析失败")
                        return {
                            "status": "active",
                            "latency": round(response_time * 1000, 2),
                            "ip": "unknown",
                            "location": "unknown",
                            "attempt": attempt,
                            "test_url": test_url
                        }
                
                logger.warning(f"代理 {proxy_url} 测试失败 - 状态码: {response.status}, 尝试: {attempt}")
                
        except asyncio.TimeoutError:
            logger.warning(f"代理 {proxy_url} 超时 - 尝试: {attempt}")
        except Exception as e:
            logger.error(f"代理 {proxy_url} 测试出错: {str(e)}, 尝试: {attempt}")
        
        await asyncio.sleep(1)  # 重试间隔
    
    logger.error(f"代理 {proxy_url} 测试失败 - 超过最大重试次数")
    return {
        "status": "error",
        "error": "Max retries exceeded",
        "attempts": PROXY_TEST_CONFIG["max_retries"]
    }

async def check_proxy_from_db(account: Dict[str, Any]) -> Dict[str, Any]:
    """检测代理（使用数据库账户数据）"""
    try:
        proxy_str = account.get("proxy", "")
        if not proxy_str:
            return {
                "account_id": account["account_id"],
                "status": "no_proxy",
                "error": "账户没有配置代理",
                "lastCheck": datetime.now().isoformat()
            }

        logger.info(f"开始检测账户 {account['account_id']} 的代理")

        async with aiohttp.ClientSession() as session:
            test_result = await test_proxy(proxy_str, session)

            result = {
                "account_id": account["account_id"],
                "proxy": proxy_str,
                "lastCheck": datetime.now().isoformat()
            }

            if test_result["status"] == "active":
                result["status"] = "healthy"
                result["responseTime"] = test_result["latency"]
                logger.info(f"代理检测成功 - 账户: {account['account_id']}, 延迟: {test_result['latency']}ms")
            else:
                result["status"] = "unhealthy"
                result["error"] = test_result.get("error", "检测失败")
                logger.warning(f"代理检测失败 - 账户: {account['account_id']}, 错误: {result['error']}")

            return result

    except Exception as e:
        logger.error(f"代理检测出错 - 账户: {account['account_id']}, 错误: {str(e)}")
        return {
            "account_id": account["account_id"],
            "status": "unhealthy",
            "error": str(e),
            "lastCheck": datetime.now().isoformat()
        }

async def check_account_from_db(account: Dict[str, Any]) -> Dict[str, Any]:
    """检测账户（使用数据库账户数据）"""
    try:
        account_id = account["account_id"]
        logger.info(f"开始检测账户: {account_id}")

        result = {
            "account_id": account_id,
            "lastCheck": datetime.now().isoformat()
        }

        proxy_str = account.get("proxy", "")
        if not proxy_str:
            result.update({
                "status": "no_proxy",
                "error": "账户没有配置代理，检测可能会被封禁"
            })
            logger.warning(f"账户 {account_id} 没有配置代理")
            return result

        # 检查cookies是否存在（从数据库）
        cookies = account.get("cookies")
        if not cookies:
            result.update({
                "status": "no_cookie",
                "error": "找不到账户cookie数据"
            })
            logger.warning(f"账户 {account_id} 找不到cookie数据")
            return result

        try:
            # 创建twikit客户端
            client = Client(language="en-US", proxy=proxy_str)

            # 加载cookie
            client.set_cookies(cookies)

            # 获取用户信息
            user = await client.user()

            logger.info(f"账户 {account_id} 检测成功 - 用户名: {user.screen_name}, 关注者: {user.followers_count}, 关注中: {user.following_count}")

            # 更新账户状态
            result.update({
                "status": "healthy",
                "proxyStatus": "healthy",
                "user_info": {
                    "screen_name": user.screen_name,
                    "followers_count": user.followers_count,
                    "following_count": user.following_count
                }
            })

        except Exception as e:
            result.update({
                "status": "unhealthy",
                "proxyStatus": "healthy",
                "error": f"账户检测失败: {str(e)}"
            })
            logger.error(f"账户 {account_id} 检测失败: {str(e)}")

    except Exception as e:
        result = {
            "account_id": account.get("account_id", "unknown"),
            "status": "unhealthy",
            "error": str(e),
            "lastCheck": datetime.now().isoformat()
        }
        logger.error(f"账户检测过程出错: {str(e)}")

    return result

async def run_health_check(concurrency: int, proxy_id: Optional[str] = None, account_id: Optional[str] = None):
    """批量健康检测（使用数据库）"""
    logger.info(f"开始批量健康检测 - 并发数: {concurrency}, 代理ID: {proxy_id}, 账户ID: {account_id}")

    # 创建信号量来控制并发
    semaphore = asyncio.Semaphore(concurrency)

    async def check_proxy_with_semaphore(account):
        async with semaphore:
            return await check_proxy_from_db(account)

    async def check_account_with_semaphore(account):
        async with semaphore:
            return await check_account_from_db(account)

    try:
        # 从数据库获取要检测的账户
        all_accounts = db.get_active_accounts()

        # 根据指定的ID筛选要检测的账户
        if account_id:
            accounts_to_check = [acc for acc in all_accounts if acc["account_id"] == account_id]
        else:
            accounts_to_check = all_accounts

        # 筛选有代理的账户用于代理检测
        accounts_with_proxy = [acc for acc in accounts_to_check if acc.get("proxy")]

        logger.info(f"待检测账户数: {len(accounts_to_check)}, 待检测代理数: {len(accounts_with_proxy)}")

        # 并发检测代理
        if accounts_with_proxy and (not proxy_id or any(acc["account_id"] == proxy_id for acc in accounts_with_proxy)):
            if proxy_id:
                proxy_accounts = [acc for acc in accounts_with_proxy if acc["account_id"] == proxy_id]
            else:
                proxy_accounts = accounts_with_proxy

            proxy_tasks = [
                check_proxy_with_semaphore(account)
                for account in proxy_accounts
            ]
            updated_proxies = await asyncio.gather(*proxy_tasks)
            logger.info(f"完成 {len(updated_proxies)} 个代理的检测")

        # 并发检测账户
        if accounts_to_check:
            account_tasks = [
                check_account_with_semaphore(account)
                for account in accounts_to_check
            ]
            updated_accounts = await asyncio.gather(*account_tasks)
            logger.info(f"完成 {len(updated_accounts)} 个账户的检测")

    except Exception as e:
        logger.error(f"批量健康检测失败: {e}")
        raise

async def run_proxies_check(concurrency: int):
    """批量检测代理（使用数据库）"""
    logger.info(f"开始批量检测代理 - 并发数: {concurrency}")

    # 创建信号量来控制并发
    semaphore = asyncio.Semaphore(concurrency)

    async def check_with_semaphore(account):
        async with semaphore:
            return await check_proxy_from_db(account)

    try:
        # 从数据库获取所有有代理的账户
        all_accounts = db.get_active_accounts()
        accounts_with_proxy = [acc for acc in all_accounts if acc.get("proxy")]

        logger.info(f"待检测代理数: {len(accounts_with_proxy)}")

        # 并发检测代理
        proxy_tasks = [
            check_with_semaphore(account)
            for account in accounts_with_proxy
        ]
        updated_proxies = await asyncio.gather(*proxy_tasks)

        logger.info(f"完成 {len(updated_proxies)} 个代理的检测")
        return updated_proxies

    except Exception as e:
        logger.error(f"批量检测代理失败: {e}")
        raise

async def run_accounts_check(concurrency: int):
    """批量检测账户（使用数据库）"""
    logger.info(f"开始批量检测账户 - 并发数: {concurrency}")

    # 创建信号量来控制并发
    semaphore = asyncio.Semaphore(concurrency)

    async def check_with_semaphore(account):
        async with semaphore:
            return await check_account_from_db(account)

    try:
        # 从数据库获取所有账户
        accounts_to_check = db.get_active_accounts()

        logger.info(f"待检测账户数: {len(accounts_to_check)}")

        # 并发检测账户
        account_tasks = [
            check_with_semaphore(account)
            for account in accounts_to_check
        ]
        updated_accounts = await asyncio.gather(*account_tasks)

        logger.info(f"完成 {len(updated_accounts)} 个账户的检测")
        return updated_accounts

    except Exception as e:
        logger.error(f"批量检测账户失败: {e}")
        raise