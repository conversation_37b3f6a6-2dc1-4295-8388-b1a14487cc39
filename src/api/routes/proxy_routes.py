import sqlite3
import aiohttp
import asyncio
import random
import time
from fastapi import APIRouter, HTTPException
from typing import Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime
from twikit import Client
from src.config import Config
from src.proxy.manager import ProxyManager
from src.proxy.rotator import proxy_rotator
from src.storage.database import Database
from src.utils.logger import setup_logger

router = APIRouter(prefix="/proxies", tags=["proxies"])
logger = setup_logger(module_name="proxy_routes")
config = Config()
proxy_manager = ProxyManager(config.get_sqlite_path())
db = Database(config)

class WebshareRequest(BaseModel):
    api_key: str

class HealthCheckRequest(BaseModel):
    concurrency: int
    proxy_id: Optional[str] = None
    account_id: Optional[str] = None

# 代理检测配置
PROXY_TEST_CONFIG = {
    "max_retries": 3,
    "timeout": 10,
    "test_urls": [
        "http://httpbin.org/ip",
        "https://api.ipify.org?format=json",
        "https://api.myip.com",
        "https://wtfismyip.com/json",
        "https://api.iplocation.net/?cmd=get-ip",
        "https://ip-api.com/json",
        "https://ipwho.is",
        "https://get.geojs.io/v1/ip.json",
        "https://ipapi.co/json",
        "https://jsonip.com"
    ]
}

async def test_proxy(proxy_url: str, session: aiohttp.ClientSession) -> dict:
    """测试代理是否可用"""
    for attempt in range(1, PROXY_TEST_CONFIG["max_retries"] + 1):
        try:
            timeout = aiohttp.ClientTimeout(total=PROXY_TEST_CONFIG["timeout"])
            test_url = random.choice(PROXY_TEST_CONFIG["test_urls"])
            start_time = time.time()
            
            async with session.get(test_url, proxy=proxy_url, timeout=timeout) as response:
                response_time = time.time() - start_time
                if response.status == 200:
                    try:
                        ip_info = await response.json()
                        return {
                            "status": "active",
                            "latency": round(response_time * 1000, 2),  # 转换为毫秒
                            "ip": ip_info.get("ip", "unknown"),
                            "location": ip_info.get("country", "unknown"),
                            "attempt": attempt,
                            "test_url": test_url
                        }
                    except:
                        return {
                            "status": "active",
                            "latency": round(response_time * 1000, 2),
                            "ip": "unknown",
                            "location": "unknown",
                            "attempt": attempt,
                            "test_url": test_url
                        }
                
                logger.warning(f"Proxy {proxy_url} failed with status {response.status}, attempt {attempt}")
                
        except asyncio.TimeoutError:
            logger.warning(f"Proxy {proxy_url} timeout, attempt {attempt}")
        except Exception as e:
            logger.error(f"Proxy {proxy_url} error: {str(e)}, attempt {attempt}")
        
        await asyncio.sleep(1)  # 重试间隔
    
    return {
        "status": "error",
        "error": "Max retries exceeded",
        "attempts": PROXY_TEST_CONFIG["max_retries"]
    }

@router.post("/webshare", response_model=dict)
async def fetch_webshare_proxies(request: WebshareRequest):
    """从 Webshare API 获取代理"""
    logger.debug("Fetching proxies from Webshare API")
    try:
        proxies = proxy_manager.fetch_webshare_proxies(request.api_key)
        return {"fetched": len(proxies), "failed": 0}
    except Exception as e:
        logger.error(f"Failed to fetch Webshare proxies: {e}")
        return {"fetched": 0, "failed": 1, "error": str(e)}

@router.get("/status")
async def check_proxies_status(
    concurrent_limit: Optional[int] = 5,
    include_history: Optional[bool] = False
):
    """检查代理状态"""
    try:
        # 获取所有代理
        accounts = []
        with sqlite3.connect(config.get_sqlite_path()) as conn:
            cursor = conn.execute("SELECT account_id, proxy FROM accounts WHERE proxy IS NOT NULL")
            accounts = [{"account_id": row[0], "proxy": row[1]} for row in cursor.fetchall()]
        
        if not accounts:
            return {"proxies": [], "message": "No proxies found"}
        
        results = []
        # 确保 concurrent_limit 不为 None
        limit = concurrent_limit if concurrent_limit is not None else 5
        semaphore = asyncio.Semaphore(limit)
        
        async def check_single_proxy(account: dict):
            async with semaphore:
                proxy_url = account["proxy"]
                try:
                    async with aiohttp.ClientSession() as session:
                        test_result = await test_proxy(proxy_url, session)
                        result = {
                            "account_id": account["account_id"],
                            "proxy": proxy_url,
                            "last_check": datetime.now().isoformat(),
                            "is_active": test_result["status"] == "active",
                            **test_result
                        }
                        
                        # 更新数据库中的代理状态
                        with sqlite3.connect(config.database["path"]) as conn:
                            conn.execute("""
                                UPDATE accounts 
                                SET proxy_status = ?, 
                                    last_check = ?,
                                    proxy_latency = ?
                                WHERE account_id = ?
                            """, (
                                test_result["status"],
                                datetime.now().isoformat(),
                                test_result.get("latency", 0),
                                account["account_id"]
                            ))
                            conn.commit()
                        
                        results.append(result)
                        
                except Exception as e:
                    logger.error(f"Failed to check proxy for account {account['account_id']}: {e}")
                    results.append({
                        "account_id": account["account_id"],
                        "proxy": proxy_url,
                        "status": "error",
                        "error": str(e),
                        "last_check": datetime.now().isoformat(),
                        "is_active": False
                    })
        
        # 并发检测所有代理
        async with asyncio.TaskGroup() as tg:
            for account in accounts:
                tg.create_task(check_single_proxy(account))
        
        # 如果需要历史记录
        if include_history:
            with sqlite3.connect(config.get_sqlite_path()) as conn:
                cursor = conn.execute("""
                    SELECT account_id, proxy_status, last_check, proxy_latency 
                    FROM accounts 
                    WHERE proxy IS NOT NULL 
                    ORDER BY last_check DESC
                """)
                history = [
                    {
                        "account_id": row[0],
                        "status": row[1],
                        "check_time": row[2],
                        "latency": row[3]
                    }
                    for row in cursor.fetchall()
                ]
                return {
                    "proxies": results,
                    "history": history,
                    "total": len(results),
                    "active": sum(1 for r in results if r["is_active"])
                }
        
        return {
            "proxies": results,
            "total": len(results),
            "active": sum(1 for r in results if r["is_active"])
        }
        
    except Exception as e:
        logger.error(f"Failed to check proxies status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class ProxyRotationRequest(BaseModel):
    dry_run: Optional[bool] = False
    concurrent_limit: Optional[int] = 5

@router.post("/rotate")
async def rotate_proxies(request: ProxyRotationRequest):
    """自动检测失效代理并轮换"""
    logger.info(f"开始代理轮换 - dry_run: {request.dry_run}")
    
    try:
        if request.dry_run:
            # 只检测不执行轮换
            failed_proxies = await proxy_rotator.detect_failed_proxies()
            
            if not failed_proxies:
                return {
                    "success": True,
                    "message": "所有代理都正常工作",
                    "failed_proxies": [],
                    "replacement_analysis": []
                }
            
            # 分析可用替换代理
            available_proxies = proxy_rotator.load_available_proxies()
            replacement_analysis = []
            
            for account_id, failed_proxy_url in failed_proxies:
                best_proxy = proxy_rotator.find_best_replacement_proxy(
                    failed_proxy_url, available_proxies
                )
                replacement_analysis.append({
                    "account_id": account_id,
                    "failed_proxy": failed_proxy_url,
                    "replacement_proxy": best_proxy.proxy_url if best_proxy else None,
                    "match_reason": getattr(best_proxy, 'match_reason', None) if best_proxy else "没有找到合适的替换代理"
                })
            
            return {
                "success": True,
                "message": f"检测到 {len(failed_proxies)} 个失效代理（模拟模式）",
                "failed_proxies": [{"account_id": aid, "proxy": proxy} for aid, proxy in failed_proxies],
                "available_proxies_count": len(available_proxies),
                "replacement_analysis": replacement_analysis
            }
        else:
            # 执行实际轮换
            result = await proxy_rotator.auto_rotate_failed_proxies()
            
            return {
                "success": True,
                "message": "代理轮换完成",
                **result
            }
            
    except Exception as e:
        logger.error(f"代理轮换失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/check/{account_id}")
async def check_single_proxy(account_id: str):
    """检测单个账户的代理状态"""
    logger.info(f"检测账户 {account_id} 的代理状态")
    
    try:
        account = db.get_account(account_id)
        if not account:
            raise HTTPException(status_code=404, detail="账户不存在")
        
        proxy_url = account.get("proxy")
        if not proxy_url:
            return {
                "account_id": account_id,
                "has_proxy": False,
                "message": "账户没有配置代理"
            }
        
        is_working = await proxy_rotator._test_proxy(proxy_url)
        
        return {
            "account_id": account_id,
            "proxy_url": proxy_url,
            "has_proxy": True,
            "is_working": is_working,
            "status": "正常" if is_working else "失效",
            "checked_at": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检测代理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/check-all")
async def check_all_proxies():
    """检测所有账户的代理状态"""
    logger.info("检测所有账户的代理状态")
    
    try:
        # 获取失效代理
        failed_proxies = await proxy_rotator.detect_failed_proxies()
        
        # 获取统计信息
        all_accounts = db.get_active_accounts()
        total_accounts = len(all_accounts)
        accounts_with_proxy = sum(1 for acc in all_accounts if acc.get("proxy"))
        failed_count = len(failed_proxies)
        working_count = accounts_with_proxy - failed_count
        
        return {
            "total_accounts": total_accounts,
            "accounts_with_proxy": accounts_with_proxy,
            "working_proxies": working_count,
            "failed_proxies": failed_count,
            "failed_proxy_details": [
                {
                    "account_id": account_id,
                    "proxy_url": proxy_url
                }
                for account_id, proxy_url in failed_proxies
            ],
            "checked_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"检测所有代理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/available")
async def list_available_proxies():
    """列出所有可用代理"""
    logger.info("获取可用代理列表")
    
    try:
        available_proxies = proxy_rotator.load_available_proxies()
        
        # 按国家分组统计
        by_country = {}
        for proxy in available_proxies:
            country = proxy.country_code or "Unknown"
            if country not in by_country:
                by_country[country] = {
                    "count": 0,
                    "cities": set(),
                    "proxies": []
                }
            by_country[country]["count"] += 1
            if proxy.city_name:
                by_country[country]["cities"].add(proxy.city_name)
            by_country[country]["proxies"].append({
                "ip": proxy.ip,
                "port": proxy.port,
                "city": proxy.city_name,
                "use_count": proxy.use_count
            })
        
        # 转换set为list以便JSON序列化
        country_stats = {}
        for country, data in by_country.items():
            country_stats[country] = {
                "count": data["count"],
                "cities": list(data["cities"]),
                "city_count": len(data["cities"]),
                "proxies": data["proxies"][:5]  # 只返回前5个代理详情
            }
        
        return {
            "total_proxies": len(available_proxies),
            "countries": list(by_country.keys()),
            "country_stats": country_stats
        }
        
    except Exception as e:
        logger.error(f"获取可用代理列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/assignments/conflicts")
async def check_proxy_assignment_conflicts():
    """检查代理分配冲突"""
    logger.info("检查代理分配冲突")
    
    try:
        conflicts = proxy_manager.get_proxy_assignment_conflicts()
        
        return {
            "has_conflicts": len(conflicts) > 0,
            "total_conflicts": len(conflicts),
            "conflicts": conflicts,
            "checked_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"检查代理分配冲突失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/assignments/fix-conflicts")
async def fix_proxy_assignment_conflicts():
    """修复代理分配冲突"""
    logger.info("修复代理分配冲突")
    
    try:
        result = proxy_manager.fix_proxy_assignment_conflicts()
        
        return {
            "success": True,
            "message": "代理分配冲突修复完成",
            **result,
            "fixed_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"修复代理分配冲突失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class ProxyAssignmentRequest(BaseModel):
    proxy_url: str
    account_id: str

@router.post("/assignments/validate")
async def validate_proxy_assignment(request: ProxyAssignmentRequest):
    """验证代理分配是否有效"""
    logger.info(f"验证代理分配: {request.proxy_url} -> {request.account_id}")
    
    try:
        is_valid = proxy_manager.validate_proxy_assignment(request.proxy_url, request.account_id)
        
        return {
            "is_valid": is_valid,
            "proxy_url": request.proxy_url,
            "account_id": request.account_id,
            "validated_at": datetime.now().isoformat(),
            "message": "代理分配有效" if is_valid else "代理分配无效或有冲突"
        }
        
    except Exception as e:
        logger.error(f"验证代理分配失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/assignments/account/{account_id}")
async def get_account_proxy_assignment(account_id: str):
    """获取账户的代理分配信息"""
    logger.info(f"获取账户 {account_id} 的代理分配信息")
    
    try:
        proxy_url = proxy_manager.get_account_proxy(account_id)
        
        if proxy_url:
            # 验证分配是否有效
            is_valid = proxy_manager.validate_proxy_assignment(proxy_url, account_id)
            
            return {
                "account_id": account_id,
                "has_proxy": True,
                "proxy_url": proxy_url,
                "is_valid": is_valid,
                "checked_at": datetime.now().isoformat()
            }
        else:
            return {
                "account_id": account_id,
                "has_proxy": False,
                "proxy_url": None,
                "is_valid": True,  # 没有代理分配也是有效状态
                "checked_at": datetime.now().isoformat()
            }
        
    except Exception as e:
        logger.error(f"获取账户代理分配失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/assignments")
async def list_all_proxy_assignments():
    """列出所有代理分配"""
    logger.info("获取所有代理分配列表")
    
    try:
        assignments = []
        
        with sqlite3.connect(config.get_sqlite_path()) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("""
                SELECT ip, assigned_to, assigned_at, country_code, city_name, use_count
                FROM proxies 
                WHERE assigned_to IS NOT NULL AND assigned_to != ''
                ORDER BY assigned_at DESC
            """)
            
            for row in cursor.fetchall():
                assignments.append({
                    "proxy_url": row["ip"],
                    "account_id": row["assigned_to"],
                    "assigned_at": row["assigned_at"],
                    "country_code": row["country_code"],
                    "city_name": row["city_name"],
                    "use_count": row["use_count"]
                })
        
        # 检查冲突
        conflicts = proxy_manager.get_proxy_assignment_conflicts()
        
        return {
            "total_assignments": len(assignments),
            "assignments": assignments,
            "has_conflicts": len(conflicts) > 0,
            "conflict_count": len(conflicts),
            "checked_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取代理分配列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ===== 保持原有的健康检查功能集成 =====

async def check_proxy_from_db(account: Dict[str, Any]) -> Dict[str, Any]:
    """检测代理（使用数据库账户数据）"""
    try:
        proxy_str = account.get("proxy", "")
        if not proxy_str:
            return {
                "account_id": account["account_id"],
                "status": "no_proxy",
                "error": "账户没有配置代理",
                "lastCheck": datetime.now().isoformat()
            }

        logger.info(f"开始检测账户 {account['account_id']} 的代理")

        async with aiohttp.ClientSession() as session:
            test_result = await test_proxy(proxy_str, session)

            result = {
                "account_id": account["account_id"],
                "proxy": proxy_str,
                "lastCheck": datetime.now().isoformat()
            }

            if test_result["status"] == "active":
                result["status"] = "healthy"
                result["responseTime"] = test_result["latency"]
                logger.info(f"代理检测成功 - 账户: {account['account_id']}, 延迟: {test_result['latency']}ms")
            else:
                result["status"] = "unhealthy"
                result["error"] = test_result.get("error", "检测失败")
                logger.warning(f"代理检测失败 - 账户: {account['account_id']}, 错误: {result['error']}")

            return result

    except Exception as e:
        logger.error(f"代理检测出错 - 账户: {account['account_id']}, 错误: {str(e)}")
        return {
            "account_id": account["account_id"],
            "status": "unhealthy",
            "error": str(e),
            "lastCheck": datetime.now().isoformat()
        }

async def check_account_from_db(account: Dict[str, Any]) -> Dict[str, Any]:
    """检测账户（使用数据库账户数据）"""
    try:
        account_id = account["account_id"]
        logger.info(f"开始检测账户: {account_id}")

        result = {
            "account_id": account_id,
            "lastCheck": datetime.now().isoformat()
        }

        proxy_str = account.get("proxy", "")
        if not proxy_str:
            result.update({
                "status": "no_proxy",
                "error": "账户没有配置代理，检测可能会被封禁"
            })
            logger.warning(f"账户 {account_id} 没有配置代理")
            return result

        # 检查cookies是否存在（从数据库）
        cookies = account.get("cookies")
        if not cookies:
            result.update({
                "status": "no_cookie",
                "error": "找不到账户cookie数据"
            })
            logger.warning(f"账户 {account_id} 找不到cookie数据")
            return result

        try:
            # 创建twikit客户端
            client = Client(language="en-US", proxy=proxy_str)

            # 加载cookie
            client.set_cookies(cookies)

            # 获取用户信息
            user = await client.user()

            logger.info(f"账户 {account_id} 检测成功 - 用户名: {user.screen_name}, 关注者: {user.followers_count}, 关注中: {user.following_count}")

            # 更新账户状态
            result.update({
                "status": "healthy",
                "proxyStatus": "healthy",
                "user_info": {
                    "screen_name": user.screen_name,
                    "followers_count": user.followers_count,
                    "following_count": user.following_count
                }
            })

        except Exception as e:
            result.update({
                "status": "unhealthy",
                "proxyStatus": "healthy",
                "error": f"账户检测失败: {str(e)}"
            })
            logger.error(f"账户 {account_id} 检测失败: {str(e)}")

    except Exception as e:
        result = {
            "account_id": account.get("account_id", "unknown"),
            "status": "unhealthy",
            "error": str(e),
            "lastCheck": datetime.now().isoformat()
        }
        logger.error(f"账户检测过程出错: {str(e)}")

    return result

# ===== 健康检查API端点 =====

@router.get("/health/accounts")
async def get_accounts_health():
    """获取所有账户健康状态"""
    logger.info("获取账户健康状态列表")
    try:
        accounts = db.get_active_accounts()
        account_list = []
        for account in accounts:
            account_list.append({
                "account_id": account["account_id"],
                "proxy": account.get("proxy"),
                "status": account.get("status", "unknown"),
                "lastCheck": datetime.now().isoformat()
            })
        return account_list
    except Exception as e:
        logger.error(f"获取账户健康状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/health/proxies")
async def get_proxies_health():
    """获取所有代理健康状态"""
    logger.info("获取代理健康状态列表")
    try:
        # 从数据库获取所有账户的代理信息
        with sqlite3.connect(config.get_sqlite_path()) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("SELECT account_id, proxy FROM accounts WHERE proxy IS NOT NULL AND proxy != ''")
            proxy_data = []
            for row in cursor.fetchall():
                if row["proxy"]:
                    proxy_data.append({
                        "account_id": row["account_id"],
                        "proxy": row["proxy"],
                        "status": "unknown",
                        "lastCheck": datetime.now().isoformat()
                    })
        return proxy_data
    except Exception as e:
        logger.error(f"获取代理健康状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/health/accounts/{account_id}/check")
async def check_single_account_health(account_id: str):
    """检测单个账户健康状态"""
    logger.info(f"开始检测单个账户: {account_id}")
    try:
        account = db.get_account(account_id)
        if not account:
            logger.warning(f"账户不存在: {account_id}")
            raise HTTPException(status_code=404, detail="账户不存在")

        # 执行检测并等待结果
        updated_account = await check_account_from_db(account)

        logger.info(f"账户检测完成: {account_id}, 状态: {updated_account['status']}")
        return updated_account
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检测账户失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/health/proxies/{account_id}/check")
async def check_single_proxy_health(account_id: str):
    """检测单个代理健康状态（通过账户ID）"""
    logger.info(f"开始检测单个代理: {account_id}")
    try:
        account = db.get_account(account_id)
        if not account or not account.get("proxy"):
            logger.warning(f"账户或代理不存在: {account_id}")
            raise HTTPException(status_code=404, detail="账户或代理不存在")

        # 执行检测并等待结果
        updated_proxy = await check_proxy_from_db(account)

        logger.info(f"代理检测完成: {account_id}, 状态: {updated_proxy['status']}")
        return updated_proxy
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检测代理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/health/accounts/check-all")
async def check_all_accounts_health(request: HealthCheckRequest):
    """批量检测所有账户健康状态"""
    logger.info(f"开始批量检测账户 - 并发数: {request.concurrency}")
    if request.concurrency < 1 or request.concurrency > 50:
        logger.warning(f"无效的并发数: {request.concurrency}")
        raise HTTPException(status_code=400, detail="并发数必须在1-50之间")

    try:
        # 创建信号量来控制并发
        semaphore = asyncio.Semaphore(request.concurrency)

        async def check_with_semaphore(account):
            async with semaphore:
                return await check_account_from_db(account)

        # 从数据库获取所有账户
        accounts_to_check = db.get_active_accounts()

        logger.info(f"待检测账户数: {len(accounts_to_check)}")

        # 并发检测账户
        account_tasks = [
            check_with_semaphore(account)
            for account in accounts_to_check
        ]
        results = await asyncio.gather(*account_tasks)

        return {
            "message": "账户批量检测完成",
            "total_checked": len(results),
            "results": results
        }
    except Exception as e:
        logger.error(f"批量检测账户失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/health/proxies/check-all")
async def check_all_proxies_health(request: HealthCheckRequest):
    """批量检测所有代理健康状态"""
    logger.info(f"开始批量检测代理 - 并发数: {request.concurrency}")
    if request.concurrency < 1 or request.concurrency > 50:
        logger.warning(f"无效的并发数: {request.concurrency}")
        raise HTTPException(status_code=400, detail="并发数必须在1-50之间")

    try:
        # 创建信号量来控制并发
        semaphore = asyncio.Semaphore(request.concurrency)

        async def check_with_semaphore(account):
            async with semaphore:
                return await check_proxy_from_db(account)

        # 从数据库获取所有有代理的账户
        all_accounts = db.get_active_accounts()
        accounts_with_proxy = [acc for acc in all_accounts if acc.get("proxy")]

        logger.info(f"待检测代理数: {len(accounts_with_proxy)}")

        # 并发检测代理
        proxy_tasks = [
            check_with_semaphore(account)
            for account in accounts_with_proxy
        ]
        results = await asyncio.gather(*proxy_tasks)

        return {
            "message": "代理批量检测完成",
            "total_checked": len(results),
            "results": results
        }
    except Exception as e:
        logger.error(f"批量检测代理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))