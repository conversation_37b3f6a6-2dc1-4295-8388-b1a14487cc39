import time
import requests
from fastapi import <PERSON><PERSON>outer, HTTPException, Query
from pydantic import BaseModel
from typing import Optional, Dict, Any, Tuple
from urllib.parse import urlparse
from src.api.twitter_api_facade import smart_execute
from src.api.error_handler import handle_twitter_errors
from src.utils.logger import setup_logger
from twikit import Client

logger = setup_logger(module_name="tweet_routes")
router = APIRouter(prefix="/tweet", tags=["tweet"])

class TweetUrlRequest(BaseModel):
    url: str
    force_refresh: Optional[bool] = False
    priority: Optional[str] = "normal"  # 请求优先级

# 简单内存缓存
tweet_cache: Dict[str, Tuple[float, dict]] = {}
CACHE_TTL = 6 * 600  # 60分钟

def extract_tweet_id(url: str) -> Optional[str]:
    """
    从 Twitter URL 中提取推文 ID
    支持的 URL 格式：
    - https://twitter.com/username/status/1234567890
    - https://x.com/username/status/1234567890
    - https://t.co/abcdef
    """
    # 处理 t.co 短链接
    if 't.co' in url:
        try:
            response = requests.head(url, allow_redirects=True)
            url = response.url
        except Exception as e:
            logger.error(f"Error resolving t.co URL: {e}")
            return None

    # 解析 URL
    parsed = urlparse(url)
    path_parts = parsed.path.strip('/').split('/')

    # 检查是否是推文 URL
    if len(path_parts) >= 3 and path_parts[1] == 'status':
        return path_parts[2]
    
    return None

async def get_tweet_info(
    tweet_id: str,
    priority: str = "normal"
) -> Optional[Dict[str, Any]]:
    """
    使用智能账号管理器获取推文信息

    Args:
        tweet_id: 推文ID
        priority: 请求优先级 (low, normal, high, critical)
    """
    logger.debug(f"Fetching tweet info for {tweet_id} with priority={priority}")

    try:
        async def fetch_tweet(client: Client, tweet_id: str) -> Dict[str, Any]:
            logger.debug(f"Executing inner fetch_tweet for {tweet_id} with account {getattr(client, '_account_id', 'unknown')}")

            tweet = await client.get_tweet_by_id(tweet_id)

            result = {
                "id": tweet.id,
                "text": tweet.text,
                "created_at": tweet.created_at,
                "favorite_count": tweet.favorite_count,
                "retweet_count": tweet.retweet_count,
                "reply_count": tweet.reply_count,
                "quote_count": tweet.quote_count,
                "language": tweet.lang,
                "user": {
                    "id": tweet.user.id,
                    "name": tweet.user.name,
                    "screen_name": tweet.user.screen_name,
                    "description": tweet.user.description,
                    "followers_count": tweet.user.followers_count,
                    "following_count": tweet.user.following_count,
                    "statuses_count": tweet.user.statuses_count,
                    "profile_image_url": tweet.user.profile_image_url,
                    "profile_banner_url": tweet.user.profile_banner_url,
                },
                "media": [{
                    "type": m.type,
                    "media_url": m.media_url,
                    "expanded_url": m.expanded_url,
                    "original_url": m.original_info,
                    "url": m.url,
                    "display_url": m.display_url,
                    "height": m.height,
                    "width": m.width,
                    "video_info": getattr(m, "video_info", {}),
                    "streams": [{
                        "url": stream.url,
                        "bitrate": stream.bitrate,
                        "content_type": stream.content_type,
                    } for stream in getattr(m, "streams", [])]
                    } for m in tweet.media] if hasattr(tweet, "media") else []
            }

            logger.debug(f"Successfully fetched tweet {tweet_id}: {tweet.text[:50]}...")
            return result
        logger.debug(f"Executing smart_execute for {tweet_id} with priority={priority}")
        tweet_info = await smart_execute(
            fetch_tweet,
            priority=priority,
            tweet_id=tweet_id
        )

        logger.info(f"Tweet info retrieved successfully for {tweet_id}")
        return tweet_info

    except Exception as e:
        logger.error(f"Error fetching tweet info for {tweet_id}: {e}")
        return None

@router.post("/parse")
@handle_twitter_errors
async def parse_tweet_url(request: TweetUrlRequest):
    """
    解析 Twitter URL 并返回推文信息 - 使用智能账号管理
    请求体格式：
    curl -X POST -H "Content-Type: application/json" -d '
    {
        "url": "https://twitter.com/nocontextfooty/status/1933881320712638829",
        "force_refresh": false,
        "priority": "normal"
    }
    '
    http://localhost:8000/api/tweet/parse
    """
    logger.debug(f"Parsing tweet URL: {request.url} with priority={request.priority}")

    try:
        tweet_id = extract_tweet_id(request.url)

        if not tweet_id:
            logger.warning(f"Invalid Twitter URL provided: {request.url}")
            raise HTTPException(
                status_code=400,
                detail="Invalid Twitter URL"
            )

        logger.debug(f"Extracted tweet ID: {tweet_id}")

        now = time.time()
        cache_key = tweet_id
        if not request.force_refresh:
            cached = tweet_cache.get(cache_key)
            if cached and now - cached[0] < CACHE_TTL:
                logger.info(f"Cache hit for tweet {tweet_id}")
                return {
                    "success": True,
                    "data": cached[1],
                    "cached": True,
                    "request_info": {
                        "priority": request.priority
                    }
                }

        logger.debug(f"Cache miss for tweet {tweet_id}, fetching from API")

        tweet_info = await get_tweet_info(
            tweet_id,
            priority=request.priority or "normal"
        )

        if not tweet_info:
            logger.error(f"Failed to fetch tweet information for {tweet_id}")
            raise HTTPException(
                status_code=500,
                detail="Failed to fetch tweet information"
            )

        # 更新缓存
        tweet_cache[cache_key] = (now, tweet_info)
        logger.debug(f"Tweet {tweet_id} cached successfully")

        return {
            "success": True,
            "data": tweet_info,
            "cached": False,
            "request_info": {
                "priority": request.priority,
                "tweet_id": tweet_id
            }
        }

    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Error parsing tweet URL {request.url}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )