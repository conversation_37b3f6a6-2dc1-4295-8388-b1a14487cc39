import asyncio
import aiohttp
from fastapi import APIRouter, HTTPException, BackgroundTasks, Query
from typing import Dict, List, Optional
from pydantic import BaseModel
from src.utils.logger import setup_logger
from src.api.twitter_api_facade import smart_execute
from src.api.helpers import (
    collect_user_state,
    detect_state_changes,
    send_notifications,
    send_error_notification
)

router = APIRouter(prefix="/monitor", tags=["monitor"])
logger = setup_logger(module_name="monitor_routes")

# 监控任务存储
_monitor_tasks = {}

class MonitorConfig(BaseModel):
    interval: int = 300  # 监控间隔，默认5分钟
    notify_url: Optional[str] = None  # 回调通知URL
    monitor_profile: bool = True  # 是否监控个人资料
    monitor_tweets: bool = True  # 是否监控推文
    monitor_relations: bool = True  # 是否监控关注关系

class EnhancedMonitorConfig(MonitorConfig):
    monitor_mentions: bool = False
    monitor_likes: bool = False
    monitor_retweets: bool = False
    alert_threshold: Dict[str, float] = {
        "followers_change": 100,
        "engagement_rate": 0.1
    }
    notification_channels: List[str] = ["webhook"]
    priority: str = "normal"  # 监控请求优先级

async def monitor_user_task(user_id: str, config: EnhancedMonitorConfig):
    """增强版监控任务 - 支持智能账号管理"""
    logger.debug(f"Starting monitor task for user {user_id} with priority={config.priority}")

    last_state = {
        "profile": None,
        "tweets": set(),
        "following": set(),
        "followers": set(),
        "mentions": set(),
        "likes": set(),
        "retweets": set()
    }

    while True:
        try:
            logger.debug(f"Monitoring user {user_id} - fetching current state")

            # 使用智能账号管理器执行API调用
            async def fetch_state(client, user_id, config):
                logger.debug(f"Collecting state for user {user_id} with account {getattr(client, '_account_id', 'unknown')}")
                return await collect_user_state(user_id, config, client)

            current_state = await smart_execute(
                fetch_state,
                priority=config.priority,
                user_id=user_id,
                config=config
            )

            changes = detect_state_changes(last_state, current_state)

            if changes:
                logger.info(f"Detected changes for user {user_id}: {list(changes.keys())}")
                if config.notify_url:
                    await send_notifications(changes, config)
            else:
                logger.debug(f"No changes detected for user {user_id}")

            last_state = current_state

        except Exception as e:
            logger.error(f"Monitor task error for user {user_id}: {e}")
            if config.notify_url:
                await send_error_notification(user_id, str(e), config)

        logger.debug(f"Monitor task for user {user_id} sleeping for {config.interval}s")
        await asyncio.sleep(config.interval)

@router.post("/user/{identifier}")
async def start_user_monitor(
    identifier: str,
    config: EnhancedMonitorConfig,
    background_tasks: BackgroundTasks
):
    """启动用户监控"""
    try:
        # 使用智能账号管理器获取用户信息
        async def get_user(client, identifier):
            logger.debug(f"Getting user {identifier} with account {getattr(client, '_account_id', 'unknown')}")
            if identifier.isdigit():
                return await client.get_user_by_id(identifier)
            return await client.get_user_by_screen_name(identifier)

        user = await smart_execute(
            get_user,
            priority=config.priority,
            identifier=identifier
        )
            
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 停止现有的监控任务
        if user.id in _monitor_tasks:
            _monitor_tasks[user.id].cancel()
        
        # 创建新的监控任务
        task = asyncio.create_task(monitor_user_task(user.id, config))
        _monitor_tasks[user.id] = task
        
        return {"status": "success", "message": f"Started monitoring user {user.screen_name}"}
        
    except Exception as e:
        logger.error(f"Failed to start monitoring {identifier}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/user/{identifier}")
async def stop_user_monitor(identifier: str):
    """停止用户监控"""
    try:
        # 使用智能账号管理器获取用户信息
        async def get_user(client, identifier):
            logger.debug(f"Getting user {identifier} for stop monitoring with account {getattr(client, '_account_id', 'unknown')}")
            if identifier.isdigit():
                return await client.get_user_by_id(identifier)
            return await client.get_user_by_screen_name(identifier)

        user = await smart_execute(
            get_user,
            priority="normal",  # 停止监控使用普通优先级
            identifier=identifier
        )
            
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        if user.id in _monitor_tasks:
            _monitor_tasks[user.id].cancel()
            del _monitor_tasks[user.id]
            return {"status": "success", "message": f"Stopped monitoring user {user.screen_name}"}
        else:
            raise HTTPException(status_code=404, detail="No active monitoring for this user")
            
    except Exception as e:
        logger.error(f"Failed to stop monitoring {identifier}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/batch")
async def batch_monitor_users(
    users: List[str],
    config: EnhancedMonitorConfig,
    background_tasks: BackgroundTasks
):
    """批量启动用户监控"""
    results = []
    for user in users:
        try:
            result = await start_user_monitor(user, config, background_tasks)
            results.append({"user": user, "status": "success", "detail": result})
        except Exception as e:
            results.append({"user": user, "status": "failed", "error": str(e)})
    return results 