from fastapi import APIRouter, HTTPException, Query
from typing import Optional, Dict, Any
from src.api.twitter_api_facade import smart_execute
from src.utils.logger import setup_logger
from src.utils.utils import user_to_dict
from twikit import Client

logger = setup_logger(module_name="user_routes")
router = APIRouter(prefix="/users", tags=["users"])

@router.get("/{username}")
async def get_user_info(
    username: str,
    priority: str = Query("normal", description="Request priority: low, normal, high, critical"),
    preferred_account: Optional[str] = Query(None, description="Preferred account ID")
) -> Dict[str, Any]:
    """获取用户信息 - 使用智能账号管理"""
    try:
        async def fetch_user(client: Client, username) -> Dict[str, Any]:
            user = await client.get_user_by_screen_name(username)
            return user_to_dict(user)

        result = await smart_execute(
            fetch_user,
            priority=priority,
            preferred_account_id=preferred_account,
            username=username
        )

        return {
            "success": True,
            "data": result,
            "request_info": {
                "priority": priority,
                "preferred_account": preferred_account
            }
        }
    except Exception as e:
        logger.error(f"Error fetching user {username}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{username}/followers")
async def get_user_followers(username: str, limit: int = 20):
    """获取用户的关注者列表"""
    try:
        async def fetch_followers(client: Client, username, limit):
            return await client.get_user_followers(username, count=limit)
            
        result = await smart_execute(
            fetch_followers,
            priority="normal",
            username=username,
            limit=limit
        )
        return result
    except Exception as e:
        logger.error(f"Error fetching followers for user {username}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{username}/following")
async def get_user_following(username: str, limit: int = 20) -> Dict[str, Any]:
    """获取用户的关注列表"""
    try:
        async def fetch_following(client, username, limit):
            return await client.get_user_following(username, limit=limit)
            
        result = await smart_execute(
            fetch_following,
            priority="normal",
            username=username,
            limit=limit
        )
        return result
    except Exception as e:
        logger.error(f"Error fetching following for user {username}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 