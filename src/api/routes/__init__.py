from fastapi import APIRouter
from .base_routes import router as base_router
from .user_routes import router as user_router
from .media_routes import router as media_router
from .monitor_routes import router as monitor_router
from .account_routes import router as account_router
from .proxy_routes import router as proxy_router
from .analytics_routes import router as analytics_router
from .tweet_routes import router as tweet_router
from .health_check import router as health_check_router
from .smart_account_routes import router as smart_account_router  

router = APIRouter(prefix="/api")

router.include_router(base_router)
router.include_router(user_router)
router.include_router(media_router)
router.include_router(monitor_router)
router.include_router(account_router)
router.include_router(proxy_router)
router.include_router(analytics_router)
router.include_router(tweet_router)
router.include_router(health_check_router)
router.include_router(smart_account_router)