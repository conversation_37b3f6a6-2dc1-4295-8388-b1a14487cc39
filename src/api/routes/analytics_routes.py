from fastapi import APIRouter, HTTPException, Query
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from src.utils.logger import setup_logger
from src.utils.decorators import Cache, rate_limit, monitor_performance
from src.utils.analytics import TwitterAnalytics
from src.api.twitter_api_facade import smart_execute
from src.api.helpers import (
    convert_tweet_to_dict,
    convert_user_to_dict,
    get_historical_data
)

router = APIRouter(prefix="/analytics", tags=["analytics"])
logger = setup_logger(module_name="analytics_routes")

class AnalyticsRequest(BaseModel):
    time_range: str = "7d"
    metrics: List[str] = ["engagement", "growth", "activity"]

class ExportRequest(BaseModel):
    data_type: str = "all"  # tweets, followers, following, media
    format: str = "json"    # json, csv, excel

class AnalyticsResponse(BaseModel):
    engagement_rate: float
    follower_growth: Dict[str, Any]
    posting_times: List[Dict[str, Any]]
    top_hashtags: List[Dict[str, Any]]
    sentiment: Dict[str, Any]
    topics: List[Dict[str, Any]]
    user_metrics: Dict[str, Any]

@router.get("/user/{identifier}", response_model=AnalyticsResponse)
@monitor_performance()
@Cache.cache_response(expire_minutes=15)
@rate_limit(requests_per_minute=30)
async def get_user_analytics(
    identifier: str,
    request: AnalyticsRequest,
    priority: str = Query("normal", description="Request priority: low, normal, high, critical")
):
    """获取用户数据分析 - 使用智能账号管理"""
    logger.debug(f"Getting analytics for user {identifier} with priority={priority}")

    try:
        # 使用智能账号管理器获取用户信息
        async def get_user(client, identifier):
            logger.debug(f"Getting user {identifier} with account {getattr(client, '_account_id', 'unknown')}")
            if identifier.isdigit():
                return await client.get_user_by_id(identifier)
            return await client.get_user_by_screen_name(identifier)

        user = await smart_execute(
            get_user,
            priority=priority,
            identifier=identifier
        )

        if not user:
            logger.warning(f"User {identifier} not found")
            raise HTTPException(status_code=404, detail="User not found")

        logger.debug(f"Found user {user.screen_name} ({user.id})")

        # 使用智能账号管理器获取用户推文
        async def get_tweets(client, user_id):
            logger.debug(f"Getting tweets for user {user_id} with account {getattr(client, '_account_id', 'unknown')}")
            return await client.get_user_tweets(user_id, tweet_type="Tweets", count=200)

        tweets = await smart_execute(
            get_tweets,
            priority=priority,
            user_id=user.id
        )

        tweet_data = [convert_tweet_to_dict(tweet) for tweet in tweets]
        logger.debug(f"Retrieved {len(tweet_data)} tweets for analysis")
        
        # 获取历史数据
        historical_data = await get_historical_data(user.id)
        
        # 执行分析
        analytics = AnalyticsResponse(
            engagement_rate=TwitterAnalytics.calculate_engagement_rate(
                tweet_data, user.followers_count
            ),
            follower_growth=TwitterAnalytics.analyze_follower_growth(
                historical_data, request.time_range
            ),
            posting_times=TwitterAnalytics.analyze_posting_times(tweet_data),
            top_hashtags=TwitterAnalytics.extract_hashtags(tweet_data),
            sentiment=TwitterAnalytics.perform_sentiment_analysis(tweet_data),
            topics=TwitterAnalytics.analyze_content_topics(tweet_data),
            user_metrics=TwitterAnalytics.calculate_user_metrics(
                convert_user_to_dict(user), tweet_data
            )
        )
        
        return analytics
        
    except Exception as e:
        logger.error(f"Analytics failed for user {identifier}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/export/user/{identifier}")
@monitor_performance()
@rate_limit(requests_per_minute=10)
async def export_user_data(
    identifier: str,
    request: ExportRequest,
    priority: str = Query("low", description="Request priority for export (default: low)")
):
    """导出用户数据 - 使用智能账号管理"""
    logger.debug(f"Exporting data for user {identifier} with priority={priority}")

    try:
        # 使用智能账号管理器获取用户信息
        async def get_user(client, identifier):
            logger.debug(f"Getting user {identifier} for export with account {getattr(client, '_account_id', 'unknown')}")
            if identifier.isdigit():
                return await client.get_user_by_id(identifier)
            return await client.get_user_by_screen_name(identifier)

        user = await smart_execute(
            get_user,
            priority=priority,
            identifier=identifier
        )

        if not user:
            logger.warning(f"User {identifier} not found for export")
            raise HTTPException(status_code=404, detail="User not found")

        logger.debug(f"Exporting data for user {user.screen_name} ({user.id})")

        # 收集数据
        data = {}

        if request.data_type in ["all", "tweets"]:
            logger.debug("Exporting tweets data")
            async def get_tweets(client, user_id):
                logger.debug(f"Getting tweets for export with account {getattr(client, '_account_id', 'unknown')}")
                return await client.get_user_tweets(user_id, tweet_type="Tweets", count=200)

            tweets = await smart_execute(
                get_tweets,
                priority=priority,
                user_id=user.id
            )
            data["tweets"] = [convert_tweet_to_dict(tweet) for tweet in tweets]
            logger.debug(f"Exported {len(data['tweets'])} tweets")

        if request.data_type in ["all", "followers"]:
            logger.debug("Exporting followers data")
            async def get_followers(client, user_id):
                logger.debug(f"Getting followers for export with account {getattr(client, '_account_id', 'unknown')}")
                return await client.get_user_followers(user_id, count=200)

            followers = await smart_execute(
                get_followers,
                priority=priority,
                user_id=user.id
            )
            data["followers"] = [convert_user_to_dict(follower) for follower in followers]
            logger.debug(f"Exported {len(data['followers'])} followers")

        if request.data_type in ["all", "following"]:
            logger.debug("Exporting following data")
            async def get_following(client, user_id):
                logger.debug(f"Getting following for export with account {getattr(client, '_account_id', 'unknown')}")
                return await client.get_user_following(user_id, count=200)

            following = await smart_execute(
                get_following,
                priority=priority,
                user_id=user.id
            )
            data["following"] = [convert_user_to_dict(friend) for friend in following]
            logger.debug(f"Exported {len(data['following'])} following")
            
        # 格式化数据
        if request.format == "json":
            return data
        elif request.format == "csv":
            import pandas as pd
            import io
            from fastapi.responses import StreamingResponse
            
            # 将数据转换为 DataFrame
            df = pd.DataFrame()
            if "tweets" in data:
                df = pd.DataFrame(data["tweets"])
            elif "followers" in data:
                df = pd.DataFrame(data["followers"])
            elif "following" in data:
                df = pd.DataFrame(data["following"])
                
            # 创建 CSV 响应
            output = io.StringIO()
            df.to_csv(output, index=False)
            return StreamingResponse(
                iter([output.getvalue()]),
                media_type="text/csv",
                headers={
                    "Content-Disposition": f'attachment; filename="{identifier}_{request.data_type}.csv"'
                }
            )
        else:
            raise HTTPException(status_code=400, detail="Unsupported format")
            
    except Exception as e:
        logger.error(f"Export failed for user {identifier}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 