"""
重试策略和故障转移机制
提供多层次的重试策略、智能故障转移和错误恢复机制
"""

import asyncio
import logging
import os
import random
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Awaitable, Callable, Dict, List, Optional, TypeVar

from src.utils.logger import setup_logger

logger = setup_logger(module_name="retry_strategies")
T = TypeVar('T')

class RetryStrategy(Enum):
    """重试策略枚举"""
    FIXED_DELAY = "fixed_delay"           # 固定延迟
    EXPONENTIAL_BACKOFF = "exponential"   # 指数退避
    LINEAR_BACKOFF = "linear"             # 线性退避
    JITTERED_EXPONENTIAL = "jittered"     # 带抖动的指数退避
    ADAPTIVE = "adaptive"                 # 自适应策略
    LITTLE = "litte"                      # 0.1-1s

class FailureType(Enum):
    """失败类型枚举"""
    RATE_LIMIT = "rate_limit"             # 限流
    AUTHENTICATION = "authentication"     # 认证失败
    NETWORK = "network"                   # 网络错误
    SERVER_ERROR = "server_error"         # 服务器错误
    CLIENT_ERROR = "client_error"         # 客户端错误
    TIMEOUT = "timeout"                   # 超时
    UNKNOWN = "unknown"                   # 未知错误

@dataclass
class RetryConfig:
    """重试配置"""
    strategy: RetryStrategy = RetryStrategy.LITTLE
    max_attempts: int = 10
    base_delay: float = 1.0
    max_delay: float = 30.0
    backoff_multiplier: float = 2.0
    jitter_range: float = 0.1
    timeout_per_attempt: float = 30.0
    
    # 针对不同错误类型的特殊配置
    rate_limit_delay: float = 60.0
    auth_failure_delay: float = 30.0
    network_error_delay: float = 5.0
    
    # 故障转移配置
    enable_account_switching: bool = True
    max_account_switches: int = int(os.environ.get("MAX_SWITCHES", "3"))
    account_cooldown_minutes: int = 15

@dataclass
class RetryAttempt:
    """重试尝试记录"""
    attempt_number: int
    account_id: Optional[str]
    start_time: datetime
    end_time: Optional[datetime] = None
    error: Optional[Exception] = None
    success: bool = False
    delay_before: float = 0.0

class RetryContext:
    """重试上下文"""
    
    def __init__(self, config: RetryConfig):
        self.config = config
        logger.debug(f"RetryContext created with config: {config}")
        self.attempts: List[RetryAttempt] = []
        self.tried_accounts: set = set()
        self.failed_accounts: set = set()  # ✨ 新增：跟踪失败的账户
        self.start_time = datetime.now()
        self.total_delay = 0.0
        self.account_switches = 0
    
    def add_attempt(self, attempt: RetryAttempt):
        """添加重试尝试记录"""
        self.attempts.append(attempt)
        if attempt.account_id:
            self.tried_accounts.add(attempt.account_id)
    
    def get_last_attempt(self) -> Optional[RetryAttempt]:
        """获取最后一次尝试"""
        return self.attempts[-1] if self.attempts else None
    
    def get_failure_pattern(self) -> Dict[FailureType, int]:
        """获取失败模式统计"""
        pattern = {}
        for attempt in self.attempts:
            if attempt.error:
                failure_type = self._classify_error(attempt.error)
                pattern[failure_type] = pattern.get(failure_type, 0) + 1
        return pattern
    
    def _classify_error(self, error: Exception) -> FailureType:
        """分类错误类型"""
        error_str = str(error).lower()
        
        if "429" in error_str or "rate limit" in error_str:
            return FailureType.RATE_LIMIT
        elif "401" in error_str or "403" in error_str or "unauthorized" in error_str:
            return FailureType.AUTHENTICATION
        elif "timeout" in error_str:
            return FailureType.TIMEOUT
        elif "connection" in error_str or "network" in error_str:
            return FailureType.NETWORK
        elif "500" in error_str or "502" in error_str or "503" in error_str:
            return FailureType.SERVER_ERROR
        elif "400" in error_str or "404" in error_str:
            return FailureType.CLIENT_ERROR
        else:
            return FailureType.UNKNOWN

class BaseRetryStrategy(ABC):
    """重试策略基类"""
    
    @abstractmethod
    async def calculate_delay(
        self, 
        attempt_number: int, 
        context: RetryContext, 
        error: Exception
    ) -> float:
        """计算重试延迟"""
        pass
    
    @abstractmethod
    def should_retry(
        self, 
        attempt_number: int, 
        context: RetryContext, 
        error: Exception
    ) -> bool:
        """判断是否应该重试"""
        pass

class ExponentialBackoffStrategy(BaseRetryStrategy):
    """指数退避策略"""
    
    async def calculate_delay(
        self, 
        attempt_number: int, 
        context: RetryContext, 
        error: Exception
    ) -> float:
        config = context.config
        failure_type = context._classify_error(error)
        
        # 根据错误类型调整延迟
        if failure_type == FailureType.RATE_LIMIT:
            base_delay = config.rate_limit_delay
        elif failure_type == FailureType.AUTHENTICATION:
            base_delay = config.auth_failure_delay
        elif failure_type == FailureType.NETWORK:
            base_delay = config.network_error_delay
        else:
            base_delay = config.base_delay
        
        # 指数退避计算
        delay = base_delay * (config.backoff_multiplier ** (attempt_number - 1))
        
        # 限制最大延迟
        delay = min(delay, config.max_delay)
        
        return delay
    
    def should_retry(
        self, 
        attempt_number: int, 
        context: RetryContext, 
        error: Exception
    ) -> bool:
        if attempt_number >= context.config.max_attempts:
            return False
        
        failure_type = context._classify_error(error)
        
        # 某些错误类型不应该重试
        if failure_type == FailureType.CLIENT_ERROR:
            error_str = str(error).lower()
            if "404" in error_str or "invalid" in error_str:
                return False
        
        return True

class JitteredExponentialStrategy(ExponentialBackoffStrategy):
    """带抖动的指数退避策略"""
    
    async def calculate_delay(
        self, 
        attempt_number: int, 
        context: RetryContext, 
        error: Exception
    ) -> float:
        base_delay = await super().calculate_delay(attempt_number, context, error)
        
        # 添加随机抖动
        jitter_range = context.config.jitter_range
        jitter = random.uniform(-jitter_range, jitter_range) * base_delay
        
        return max(0, base_delay + jitter)

class AdaptiveRetryStrategy(BaseRetryStrategy):
    """自适应重试策略"""
    
    async def calculate_delay(
        self, 
        attempt_number: int, 
        context: RetryContext, 
        error: Exception
    ) -> float:
        config = context.config
        failure_pattern = context.get_failure_pattern()
        
        # 基础延迟
        base_delay = config.base_delay * (config.backoff_multiplier ** (attempt_number - 1))
        
        # 根据失败模式调整
        if FailureType.RATE_LIMIT in failure_pattern:
            # 如果有限流错误，增加延迟
            rate_limit_count = failure_pattern[FailureType.RATE_LIMIT]
            base_delay *= (1 + rate_limit_count * 0.5)
        
        if FailureType.NETWORK in failure_pattern:
            # 如果有网络错误，减少延迟但增加重试次数
            network_count = failure_pattern[FailureType.NETWORK]
            if network_count > 2:
                base_delay *= 0.5
        
        # 添加抖动
        jitter = random.uniform(0.8, 1.2) * base_delay
        
        return min(jitter, config.max_delay)
    
    def should_retry(
        self, 
        attempt_number: int, 
        context: RetryContext, 
        error: Exception
    ) -> bool:
        if attempt_number >= context.config.max_attempts:
            return False
        
        failure_pattern = context.get_failure_pattern()
        failure_type = context._classify_error(error)
        
        # 自适应重试逻辑
        if failure_type == FailureType.RATE_LIMIT:
            # 限流错误：减少重试次数
            return attempt_number <= 3
        elif failure_type == FailureType.NETWORK:
            # 网络错误：增加重试次数
            return attempt_number <= context.config.max_attempts + 2
        elif failure_type == FailureType.AUTHENTICATION:
            # 认证错误：如果连续失败超过2次，停止重试
            auth_failures = failure_pattern.get(FailureType.AUTHENTICATION, 0)
            return auth_failures <= 2
        
        return True
class LittleRetryStrategy(BaseRetryStrategy):
    """自适应重试策略"""
    
    async def calculate_delay(
        self, 
        attempt_number: int, 
        context: RetryContext, 
        error: Exception
    ) -> float:
        jitter = random.uniform(0.1, 1)
        return jitter
    
    def should_retry(
        self, 
        attempt_number: int, 
        context: RetryContext, 
        error: Exception
    ) -> bool:
        if attempt_number >= context.config.max_attempts:
            return False
        
        return True

class SmartRetryManager:
    """智能重试管理器"""
    
    def __init__(self):
        self.strategies = {
            RetryStrategy.FIXED_DELAY: ExponentialBackoffStrategy(),
            RetryStrategy.EXPONENTIAL_BACKOFF: ExponentialBackoffStrategy(),
            RetryStrategy.JITTERED_EXPONENTIAL: JitteredExponentialStrategy(),
            RetryStrategy.ADAPTIVE: AdaptiveRetryStrategy(),
            RetryStrategy.LITTLE: LittleRetryStrategy()
        }
        
        # ✨ 延迟加载 account_health_manager 避免循环导入
        self._health_manager = None
    
    def _get_health_manager(self):
        """延迟初始化健康度管理器"""
        if self._health_manager is None:
            try:
                from src.api.account_health_manager import account_health_manager
                self._health_manager = account_health_manager
                logger.debug("✨ Health manager initialized in retry manager")
            except ImportError as e:
                logger.warning(f"⚠️ Could not import health manager: {e}")
                self._health_manager = None
        return self._health_manager
    
    async def execute_with_retry(
        self,
        func: Callable[..., Awaitable[T]],
        get_client_func: Callable[[Optional[str]], Awaitable[Any]],
        config: RetryConfig,
        *args,
        **kwargs
    ) -> T:
        """执行带重试的函数调用"""
        context = RetryContext(config)
        strategy = self.strategies[config.strategy]
        last_error = None
        
        for attempt_num in range(1, config.max_attempts + 1):
            attempt = RetryAttempt(
                attempt_number=attempt_num,
                account_id=None,
                start_time=datetime.now()
            )
            
            try:
                # 获取客户端（可能涉及账号切换）
                client = await self._get_client_with_switching(
                    get_client_func, context, attempt_num
                )
                
                attempt.account_id = getattr(client, '_account_id', None)
                
                # 执行函数调用
                logger.debug(f"🎬asyncio.wait_for executing API call: {func.__name__}({str(args)}, {str(kwargs)}) with account {attempt.account_id}, proxy: {client._proxy}")
                result = await asyncio.wait_for(
                    func(client, *args, **kwargs),
                    timeout=config.timeout_per_attempt
                )
                logger.info(f"✅Request succeeded on attempt {attempt_num}, account_id: {attempt.account_id}, proxy: {client._proxy}")
                attempt.success = True
                attempt.end_time = datetime.now()
                context.add_attempt(attempt)
                
                # ✨ 记录成功到健康度管理器
                health_manager = self._get_health_manager()
                if health_manager and attempt.account_id:
                    try:
                        await health_manager._record_request_success(attempt.account_id)
                        logger.debug(f"📈 Success recorded for account {attempt.account_id}")
                    except Exception as e:
                        logger.warning(f"⚠️ Failed to record success for account {attempt.account_id}: {e}")

                return result
                
            except Exception as e:
                last_error = e
                attempt.error = e
                attempt.end_time = datetime.now()
                context.add_attempt(attempt)
                
                logger.warning(f"Attempt {attempt_num} failed: account_id: {attempt.account_id}, proxy: {client._proxy}")
                logger.warning(f"❌❌❌Error: {str(e)}")
                
                # ✨ 记录失败到健康度管理器
                health_manager = self._get_health_manager()
                if health_manager and attempt.account_id:
                    try:
                        await health_manager._record_request_failure(attempt.account_id, e)
                        logger.debug(f"📉 Failure recorded for account {attempt.account_id}")
                        
                        # ✨ 将失败账户添加到排除列表，避免下次重试时再次选中
                        context.failed_accounts = getattr(context, 'failed_accounts', set())
                        context.failed_accounts.add(attempt.account_id)
                        logger.debug(f"🚫 Account {attempt.account_id} added to failed accounts list")
                    except Exception as record_error:
                        logger.warning(f"⚠️ Failed to record failure for account {attempt.account_id}: {record_error}")
                
                # 判断是否应该重试
                if not strategy.should_retry(attempt_num, context, e):
                    logger.error(f"Stopping retries after attempt {attempt_num}")
                    break
                
                # 如果还有重试机会，计算延迟
                if attempt_num < config.max_attempts:
                    delay = await strategy.calculate_delay(attempt_num, context, e)
                    attempt.delay_before = delay
                    context.total_delay += delay
                    
                    logger.info(f"Waiting {delay:.2f}s before attempt {attempt_num + 1}")
                    await asyncio.sleep(delay)
        
        # 所有重试都失败了
        self._log_retry_summary(context)
        if last_error:
            raise last_error
        raise RuntimeError("All retry attempts failed")
    
    async def _get_client_with_switching(
        self,
        get_client_func: Callable[[Optional[str]], Awaitable[Any]],
        context: RetryContext,
        attempt_num: int
    ) -> Any:
        """获取客户端，支持账号切换"""
        if not context.config.enable_account_switching:
            return await get_client_func(None)
        
        # 第一次尝试使用默认账号
        if attempt_num == 1:
            return await get_client_func(None)
        
        # 后续尝试考虑账号切换
        if (context.account_switches < context.config.max_account_switches and
            len(context.tried_accounts) > 0):
            
            # ✨ 使用健康度管理器进行智能账户选择
            health_manager = self._get_health_manager()
            if health_manager:
                try:
                    # ✨ 创建排除列表：已尝试的账户 + 失败的账户
                    exclude_accounts = list(context.tried_accounts.union(getattr(context, 'failed_accounts', set())))
                    logger.debug(f"🚫 Excluding accounts: tried={context.tried_accounts}, failed={getattr(context, 'failed_accounts', set())}")
                    
                    # ✨ 使用健康度管理器获取最佳账户
                    from src.api.account_health_manager import RequestContext, RequestPriority
                    request_context = RequestContext(
                        priority=RequestPriority.HIGH,  # 重试时优先使用健康账户
                        exclude_account_ids=exclude_accounts
                    )
                    
                    # 获取最佳账户并创建客户端
                    best_account_id = await health_manager.get_best_account(request_context)
                    client = await health_manager.get_client(best_account_id)
                    
                    if best_account_id and best_account_id not in context.tried_accounts:
                        context.account_switches += 1
                        health_score = getattr(health_manager._account_health.get(best_account_id), 'health_score', 'N/A')
                        logger.info(f"🔄 Switched to healthy account: {best_account_id} (health_score: {health_score})")
                        return client
                    else:
                        logger.debug(f"🔍 Best account {best_account_id} already tried, using fallback")
                        
                except Exception as e:
                    logger.warning(f"⚠️ Smart account switching failed: {e}, falling back to default")
            
            # ✨ 回退方案：尝试获取任意可用账户
            try:
                client = await get_client_func(None)
                current_account = getattr(client, '_account_id', None)
                
                if current_account and current_account not in context.tried_accounts:
                    context.account_switches += 1
                    logger.info(f"🔄 Switched to fallback account: {current_account}")
                    return client
            except Exception as e:
                logger.warning(f"⚠️ Failed to switch account: {e}")
        
        # 使用默认账号
        return await get_client_func(None)
    
    def _log_retry_summary(self, context: RetryContext):
        """记录重试摘要"""
        total_time = (datetime.now() - context.start_time).total_seconds()
        failure_pattern = context.get_failure_pattern()
        
        logger.error(f"📄 Retry summary:")
        logger.error(f"Total attempts: {len(context.attempts)}")
        logger.error(f"Total time: {total_time:.2f}s")
        logger.error(f"Total delay: {context.total_delay:.2f}s")
        logger.error(f"Account switches: {context.account_switches}")
        logger.error(f"Tried accounts: {len(context.tried_accounts)}")
        logger.error(f"🚫 Failed accounts: {getattr(context, 'failed_accounts', set())}")
        logger.error(f"Failure pattern: {failure_pattern}")

# 创建全局实例
smart_retry_manager = SmartRetryManager()
