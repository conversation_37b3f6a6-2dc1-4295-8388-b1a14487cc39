"""
FastAPI 依赖注入模块

注意：原有的 get_twit_client 函数已被移除，因为它与智能账号管理系统重复。
现在所有需要 Twitter 客户端的操作都应该使用以下方式：

1. 对于简单操作：使用 twitter_api_facade.smart_execute
2. 对于复杂操作：使用 enhanced_account_manager.smart_execute

这样可以确保：
- 智能账号选择（基于健康度、风险评估等）
- 自动重试和错误处理
- 统一的日志记录和监控
- 更好的性能和稳定性

示例用法：
```python
from src.api.twitter_api_facade import smart_execute

async def my_twitter_operation(client, *args, **kwargs):
    # 使用 client 进行 Twitter API 操作
    return await client.get_user_by_username("example")

result = await smart_execute(
    my_twitter_operation,
    priority="normal",
    max_retries=3
)
```
"""

# 如果将来需要其他 FastAPI 依赖注入函数，可以在这里添加