"""
账号健康度管理器 (Account Health Manager)

这是 Twitter 账号管理系统的核心模块，专注于账号健康度评估、监控和智能选择功能。
作为底层管理组件，提供以下核心功能：

核心功能：
- 账号健康度实时评估和监控
- 基于优先级的智能账号选择算法
- 后台健康度监控任务管理
- 请求统计和失败分析
- 账号状态管理和冷却期控制
- 风险评分计算和状态更新

使用场景：
- 需要直接访问账号健康度数据时
- 实现自定义账号选择逻辑时
- 进行系统监控和诊断时
- 需要精细控制账号管理行为时

设计原则：
- 单一职责：专注于账号健康度管理
- 高内聚：相关功能集中在一个模块中
- 低耦合：通过清晰的接口与其他模块交互
- 可扩展：支持自定义健康度评估算法

注意：
- 这是底层模块，通常不直接在业务代码中使用
- 推荐通过 TwitterApiFacade 使用高级功能
- 直接使用时需要处理错误和重试逻辑
"""

import asyncio
import os
import random
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Awaitable, Callable, Dict, List, Optional, TypeVar
from collections import defaultdict

from twikit import Client
from src.config import Config
from src.storage.database import Database
from src.utils.logger import setup_logger

logger = setup_logger(module_name="account_health_manager")
config = Config()
db = Database(config)

T = TypeVar('T')

# 自定义异常类
class AccountHealthError(Exception):
    """账号健康度管理相关的基础异常"""
    pass

class NoAvailableAccountsError(AccountHealthError):
    """没有可用账号的异常"""
    pass

class AccountValidationError(AccountHealthError):
    """账号数据验证异常"""
    pass

class HealthMonitorError(AccountHealthError):
    """健康度监控异常"""
    pass

class AccountHealthStatus(Enum):
    """账号健康状态枚举"""
    HEALTHY = "healthy"          # 健康：成功率>90%，无近期限制
    WARNING = "warning"          # 警告：成功率70-90%，或有轻微限制
    CRITICAL = "critical"        # 危险：成功率50-70%，或频繁限制
    SUSPENDED = "suspended"      # 暂停：临时不可用，等待恢复
    BANNED = "banned"           # 封禁：永久不可用

class RequestPriority(Enum):
    """请求优先级枚举"""
    LOW = 1          # 低优先级：可以等待，使用健康度较低的账号
    NORMAL = 2       # 普通优先级：正常处理
    HIGH = 3         # 高优先级：优先使用健康账号
    CRITICAL = 4     # 关键优先级：只使用最健康的账号

class RiskLevel(Enum):
    """风险等级枚举"""
    LOW = "low"          # 低风险
    MEDIUM = "medium"    # 中等风险
    HIGH = "high"        # 高风险
    EXTREME = "extreme"  # 极高风险

@dataclass
class AccountHealth:
    """账号健康度数据结构

    包含账号的健康度评估、统计信息和状态管理数据。
    所有评分都在0-100范围内，计数器都是非负整数。
    """
    account_id: str
    status: AccountHealthStatus = AccountHealthStatus.HEALTHY
    health_score: float = 100.0  # 0-100分，100为最健康
    risk_score: float = 0.0      # 0-100分，0为最安全
    
    # 请求统计
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    
    # 错误统计
    rate_limit_count: int = 0
    auth_failure_count: int = 0
    network_error_count: int = 0
    consecutive_failures: int = 0
    
    # 时间记录
    last_success: Optional[datetime] = None
    last_failure: Optional[datetime] = None
    last_rate_limit: Optional[datetime] = None
    cooldown_until: Optional[datetime] = None
    
    # 使用频率控制
    requests_in_hour: int = 0
    requests_in_day: int = 0
    last_request_time: Optional[datetime] = None
    
    # 元数据
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self) -> None:
        """数据验证和初始化后处理"""
        self._validate_scores()
        self._validate_counters()

    def _validate_scores(self) -> None:
        """验证评分范围"""
        if not (0.0 <= self.health_score <= 100.0):
            raise AccountValidationError(f"health_score must be between 0 and 100, got {self.health_score}")
        if not (0.0 <= self.risk_score <= 100.0):
            raise AccountValidationError(f"risk_score must be between 0 and 100, got {self.risk_score}")

    def _validate_counters(self) -> None:
        """验证计数器的一致性"""
        if self.total_requests < 0:
            raise AccountValidationError(f"total_requests must be non-negative, got {self.total_requests}")
        if self.successful_requests < 0:
            raise AccountValidationError(f"successful_requests must be non-negative, got {self.successful_requests}")
        if self.failed_requests < 0:
            raise AccountValidationError(f"failed_requests must be non-negative, got {self.failed_requests}")
        if self.total_requests != self.successful_requests + self.failed_requests:
            raise AccountValidationError(
                f"total_requests ({self.total_requests}) must equal "
                f"successful_requests ({self.successful_requests}) + "
                f"failed_requests ({self.failed_requests})"
            )

    def get_success_rate(self) -> float:
        """计算成功率

        Returns:
            float: 成功率，范围0.0-1.0，没有请求时返回1.0
        """
        if self.total_requests == 0:
            return 1.0
        return self.successful_requests / self.total_requests
    
    def get_risk_level(self) -> RiskLevel:
        """获取风险等级"""
        if self.risk_score >= 80:
            return RiskLevel.EXTREME
        elif self.risk_score >= 60:
            return RiskLevel.HIGH
        elif self.risk_score >= 30:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    def is_available(self) -> bool:
        """检查账号是否可用"""
        if self.status in [AccountHealthStatus.BANNED, AccountHealthStatus.SUSPENDED]:
            return False
        
        if self.cooldown_until and datetime.now() < self.cooldown_until:
            return False
            
        return True
    
    def can_handle_priority(self, priority: RequestPriority) -> bool:
        """检查是否能处理指定优先级的请求"""
        if not self.is_available():
            return False
            
        if priority == RequestPriority.CRITICAL:
            return self.status == AccountHealthStatus.HEALTHY and self.health_score >= 90
        elif priority == RequestPriority.HIGH:
            return self.status in [AccountHealthStatus.HEALTHY, AccountHealthStatus.WARNING] and self.health_score >= 70
        elif priority == RequestPriority.NORMAL:
            return self.status != AccountHealthStatus.BANNED and self.health_score >= 50
        else:  # LOW priority
            return self.status != AccountHealthStatus.BANNED

@dataclass
class RequestContext:
    """请求上下文"""
    priority: RequestPriority = RequestPriority.NORMAL
    max_retries: int = 10
    timeout: float = 30.0
    allow_degraded_accounts: bool = True
    preferred_account_id: Optional[str] = None
    exclude_account_ids: List[str] = field(default_factory=list)

class AccountHealthManager:
    """账号健康度管理器
    
    专注于账号健康度评估、监控和智能选择的核心功能。
    提供底层的账号管理逻辑，包括：
    - 账号健康度评估和监控
    - 智能账号选择算法
    - 后台健康度监控任务
    - 请求统计和失败分析
    - 账号状态管理和冷却期控制
    """
    
    def __init__(self):
        self._account_health: Dict[str, AccountHealth] = {}
        self._account_locks: Dict[str, asyncio.Lock] = defaultdict(asyncio.Lock)
        self._global_lock = asyncio.Lock()

        # 配置参数
        self.max_requests_per_hour = int(os.getenv("MAX_REQUESTS_PER_HOUR", "100"))
        self.max_requests_per_day = int(os.getenv("MAX_REQUESTS_PER_DAY", "1000"))
        self.health_check_interval = int(os.getenv("HEALTH_CHECK_INTERVAL", "300"))  # 5分钟

        # 统计信息
        self._stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "account_switches": 0,
            "priority_upgrades": 0
        }

        # 后台任务控制
        self._health_monitor_task: Optional[asyncio.Task] = None
        self._is_monitoring = False

        # 性能优化：客户端缓存
        self._client_cache: Dict[str, tuple[Client, datetime]] = {}
        self._cache_ttl = 300  # 客户端缓存5分钟

        # 初始化
        self._load_account_health()

        logger.info(f"AccountHealthManager initialized with {len(self._account_health)} accounts")
    
    async def start_health_monitor(self) -> bool:
        """启动健康度监控后台任务"""
        if self._is_monitoring:
            logger.warning("Health monitor is already running")
            return False
        
        try:
            self._health_monitor_task = asyncio.create_task(self._health_monitor_loop())
            self._is_monitoring = True
            logger.info("Health monitor started successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to start health monitor: {e}")
            return False
    
    async def stop_health_monitor(self) -> bool:
        """停止健康度监控后台任务"""
        if not self._is_monitoring or not self._health_monitor_task:
            logger.warning("Health monitor is not running")
            return False
        
        try:
            self._health_monitor_task.cancel()
            await self._health_monitor_task
            self._is_monitoring = False
            self._health_monitor_task = None
            logger.info("Health monitor stopped successfully")
            return True
        except asyncio.CancelledError:
            self._is_monitoring = False
            self._health_monitor_task = None
            logger.info("Health monitor stopped successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to stop health monitor: {e}")
            return False
    
    def is_monitoring(self) -> bool:
        """检查健康度监控是否正在运行"""
        return self._is_monitoring
    
    def _load_account_health(self):
        """加载账号健康度数据"""
        try:
            # 从数据库加载现有的健康度数据
            health_data_list = db.get_all_account_health()
            for health_data in health_data_list:
                account_id = health_data["account_id"]
                
                # 将数据库数据转换为 AccountHealth 对象
                health = AccountHealth(
                    account_id=account_id,
                    status=AccountHealthStatus(health_data.get("status", "healthy")),
                    health_score=float(health_data.get("health_score", 100.0)),
                    risk_score=float(health_data.get("risk_score", 0.0)),
                    total_requests=int(health_data.get("total_requests", 0)),
                    successful_requests=int(health_data.get("successful_requests", 0)),
                    failed_requests=int(health_data.get("failed_requests", 0)),
                    requests_in_hour=int(health_data.get("requests_in_hour", 0)),
                    requests_in_day=int(health_data.get("requests_in_day", 0)),
                    rate_limit_count=int(health_data.get("rate_limit_count", 0)),
                    auth_failure_count=int(health_data.get("auth_failure_count", 0)),
                    network_error_count=int(health_data.get("network_error_count", 0)),
                    consecutive_failures=int(health_data.get("consecutive_failures", 0)),
                )
                
                # 转换时间字段（如果存在）
                for time_field in ['last_success', 'last_failure', 'last_rate_limit', 'last_request_time', 'cooldown_until', 'hour_window_start', 'day_window_start']:
                    if health_data.get(time_field):
                        try:
                            setattr(health, time_field, datetime.fromisoformat(str(health_data[time_field]).replace('Z', '+00:00')))
                        except (ValueError, TypeError):
                            # 如果时间解析失败，设置为 None
                            setattr(health, time_field, None)
                
                self._account_health[account_id] = health
                logger.debug(f"📈 Loaded health data for account {account_id}: score={health.health_score}, status={health.status.value}")
            
            # 为所有数据库中的账户创建健康度记录（如果尚不存在）
            accounts = db.get_active_accounts()
            for account in accounts:
                account_id = account["account_id"]
                if account_id not in self._account_health:
                    self._account_health[account_id] = AccountHealth(account_id=account_id)
                    logger.debug(f"🆕 Created new health record for account {account_id}")
            
            logger.info(f"📈 Loaded health data for {len(self._account_health)} accounts from database")
        except Exception as e:
            logger.error(f"❌ Failed to load account health data: {e}")
            # 如果加载失败，使用传统方式创建默认健康度记录
            try:
                accounts = db.get_active_accounts()
                for account in accounts:
                    account_id = account["account_id"]
                    if account_id not in self._account_health:
                        self._account_health[account_id] = AccountHealth(account_id=account_id)
                logger.info(f"🔄 Created default health records for {len(self._account_health)} accounts")
            except Exception as fallback_error:
                logger.error(f"❌ Fallback account loading also failed: {fallback_error}")
    
    async def _health_monitor_loop(self):
        """后台健康度监控任务循环"""
        while True:
            try:
                await self._update_all_health_scores()
                await self._cleanup_expired_cooldowns()
                await self._reset_hourly_counters()
                await asyncio.sleep(self.health_check_interval)
            except asyncio.CancelledError:
                logger.info("Health monitor task cancelled")
                break
            except Exception as e:
                logger.error(f"Health monitor task error: {e}")
                await asyncio.sleep(60)  # 出错时等待1分钟再重试

    async def _update_all_health_scores(self) -> None:
        """更新所有账号的健康度评分

        优化版本：批量更新，减少重复计算
        """
        current_time = datetime.now()

        # 批量更新，减少异步开销
        for health in self._account_health.values():
            # 只更新需要更新的账号（距离上次更新超过一定时间）
            if self._should_update_health(health, current_time):
                self._calculate_health_score_sync(health, current_time)
                self._calculate_risk_score_sync(health)
                self._update_account_status_sync(health)

    def _should_update_health(self, health: AccountHealth, current_time: datetime) -> bool:
        """判断是否需要更新健康度

        Args:
            health: 账号健康度对象
            current_time: 当前时间

        Returns:
            bool: 是否需要更新
        """
        # 如果从未更新过，需要更新
        if not health.updated_at:
            return True

        # 如果距离上次更新超过5分钟，需要更新
        time_since_update = (current_time - health.updated_at).total_seconds()
        return time_since_update > 300  # 5分钟

    async def _calculate_health_score(self, health: AccountHealth) -> None:
        """计算账号健康度评分

        Args:
            health: 要更新的账号健康度对象

        Side Effects:
            更新 health.health_score 和 health.updated_at
        """
        score = 100.0

        # 基于成功率的评分 (权重: 40%)
        success_rate = health.get_success_rate()
        score *= (0.6 + 0.4 * success_rate)

        # 基于连续失败次数的惩罚 (权重: 20%)
        if health.consecutive_failures > 0:
            failure_penalty = min(0.8, health.consecutive_failures * 0.1)
            score *= (1 - failure_penalty)

        # 基于限流频率的惩罚 (权重: 20%)
        if health.rate_limit_count > 0:
            rate_limit_penalty = min(0.6, health.rate_limit_count * 0.05)
            score *= (1 - rate_limit_penalty)

        # 基于最近活动的评分 (权重: 20%)
        if health.last_success:
            hours_since_success = (datetime.now() - health.last_success).total_seconds() / 3600
            if hours_since_success > 24:
                score *= 0.8  # 超过24小时没有成功请求，降低评分

        health.health_score = max(0.0, min(100.0, score))
        health.updated_at = datetime.now()

    def _calculate_health_score_sync(self, health: AccountHealth, current_time: datetime) -> None:
        """同步版本的健康度评分计算，避免异步开销

        Args:
            health: 要更新的账号健康度对象
            current_time: 当前时间（避免重复获取）
        """
        score = 100.0

        # 基于成功率的评分 (权重: 40%)
        success_rate = health.get_success_rate()
        score *= (0.6 + 0.4 * success_rate)

        # 基于连续失败次数的惩罚 (权重: 20%)
        if health.consecutive_failures > 0:
            failure_penalty = min(0.8, health.consecutive_failures * 0.1)
            score *= (1 - failure_penalty)

        # 基于限流频率的惩罚 (权重: 20%)
        if health.rate_limit_count > 0:
            rate_limit_penalty = min(0.6, health.rate_limit_count * 0.05)
            score *= (1 - rate_limit_penalty)

        # 基于最近活动的评分 (权重: 20%)
        if health.last_success:
            hours_since_success = (current_time - health.last_success).total_seconds() / 3600
            if hours_since_success > 24:
                score *= 0.8  # 超过24小时没有成功请求，降低评分

        health.health_score = max(0.0, min(100.0, score))
        health.updated_at = current_time

    async def _calculate_risk_score(self, health: AccountHealth) -> None:
        """计算账号风险评分

        Args:
            health: 要更新的账号健康度对象

        Side Effects:
            更新 health.risk_score
        """
        risk = 0.0

        # 基于失败率的风险 (权重: 30%)
        failure_rate = 1 - health.get_success_rate()
        risk += failure_rate * 30

        # 基于限流频率的风险 (权重: 25%)
        if health.total_requests > 0:
            rate_limit_rate = health.rate_limit_count / health.total_requests
            risk += rate_limit_rate * 25

        # 基于认证失败的风险 (权重: 25%)
        if health.total_requests > 0:
            auth_failure_rate = health.auth_failure_count / health.total_requests
            risk += auth_failure_rate * 25

        # 基于连续失败的风险 (权重: 20%)
        if health.consecutive_failures > 0:
            consecutive_risk = min(20, health.consecutive_failures * 2)
            risk += consecutive_risk

        health.risk_score = max(0.0, min(100.0, risk))

    def _calculate_risk_score_sync(self, health: AccountHealth) -> None:
        """同步版本的风险评分计算

        Args:
            health: 要更新的账号健康度对象
        """
        risk = 0.0

        # 基于失败率的风险 (权重: 30%)
        failure_rate = 1 - health.get_success_rate()
        risk += failure_rate * 30

        # 基于限流频率的风险 (权重: 25%)
        if health.total_requests > 0:
            rate_limit_rate = health.rate_limit_count / health.total_requests
            risk += rate_limit_rate * 25

        # 基于认证失败的风险 (权重: 25%)
        if health.total_requests > 0:
            auth_failure_rate = health.auth_failure_count / health.total_requests
            risk += auth_failure_rate * 25

        # 基于连续失败的风险 (权重: 20%)
        if health.consecutive_failures > 0:
            consecutive_risk = min(20, health.consecutive_failures * 2)
            risk += consecutive_risk

        health.risk_score = max(0.0, min(100.0, risk))

    async def _update_account_status(self, health: AccountHealth):
        """更新账号状态"""
        old_status = health.status

        # 基于健康度评分更新状态
        if health.health_score >= 90:
            health.status = AccountHealthStatus.HEALTHY
        elif health.health_score >= 70:
            health.status = AccountHealthStatus.WARNING
        elif health.health_score >= 50:
            health.status = AccountHealthStatus.CRITICAL
        else:
            health.status = AccountHealthStatus.SUSPENDED

        # 特殊情况处理
        if health.consecutive_failures >= 10:
            health.status = AccountHealthStatus.SUSPENDED
            health.cooldown_until = datetime.now() + timedelta(hours=1)

        if health.auth_failure_count >= 5:
            health.status = AccountHealthStatus.BANNED

        if old_status != health.status:
            logger.info(f"🔄 Account {health.account_id} status changed: {old_status.value} -> {health.status.value}")

    def _update_account_status_sync(self, health: AccountHealth) -> None:
        """同步版本的账号状态更新

        Args:
            health: 要更新的账号健康度对象
        """
        old_status = health.status

        # 基于健康度评分更新状态
        if health.health_score >= 90:
            health.status = AccountHealthStatus.HEALTHY
        elif health.health_score >= 70:
            health.status = AccountHealthStatus.WARNING
        elif health.health_score >= 50:
            health.status = AccountHealthStatus.CRITICAL
        else:
            health.status = AccountHealthStatus.SUSPENDED

        # 特殊情况处理
        if health.consecutive_failures >= 10:
            health.status = AccountHealthStatus.SUSPENDED
            health.cooldown_until = datetime.now() + timedelta(hours=1)

        if health.auth_failure_count >= 5:
            health.status = AccountHealthStatus.BANNED

        if old_status != health.status:
            logger.info(f"Account {health.account_id} status changed: {old_status.value} -> {health.status.value}")

    async def _cleanup_expired_cooldowns(self):
        """清理过期的冷却期"""
        now = datetime.now()
        for health in self._account_health.values():
            if health.cooldown_until and now >= health.cooldown_until:
                health.cooldown_until = None
                if health.status == AccountHealthStatus.SUSPENDED:
                    health.status = AccountHealthStatus.WARNING
                logger.info(f"Account {health.account_id} cooldown expired, status restored")

    async def _reset_hourly_counters(self):
        """重置小时计数器"""
        now = datetime.now()
        for health in self._account_health.values():
            if health.last_request_time:
                hours_diff = (now - health.last_request_time).total_seconds() / 3600
                if hours_diff >= 1:
                    health.requests_in_hour = 0
                if hours_diff >= 24:
                    health.requests_in_day = 0

    async def get_best_account(self, context: RequestContext) -> str:
        """获取最适合的账号

        根据请求上下文和账号健康度，智能选择最适合的账号。
        选择算法考虑以下因素：
        - 账号健康度评分
        - 请求优先级要求
        - 频率限制状态
        - 冷却期状态
        - 首选账号设置
        - 排除账号列表

        Args:
            context: 请求上下文，包含优先级、重试次数、首选账号等信息

        Returns:
            str: 选中的账号ID

        Raises:
            NoAvailableAccountsError: 没有可用账号时抛出

        算法说明：
        - CRITICAL优先级：选择健康度最高的账号
        - HIGH优先级：从健康度前25%的账号中随机选择
        - NORMAL/LOW优先级：基于健康度加权随机选择，降低最近使用账号的权重
        """
        # 使用缓存的当前时间，避免重复调用
        current_time = datetime.now()

        # 预先计算排除账号集合，提高查找效率
        exclude_set = set(context.exclude_account_ids)

        # 筛选可用账号 - 优化版本，减少重复计算
        available_accounts = []
        for account_id, health in self._account_health.items():
            # 快速排除检查
            if account_id in exclude_set:
                continue

            # 批量检查可用性条件
            if not self._is_account_available_optimized(health, context.priority, current_time):
                continue

            available_accounts.append((account_id, health))

        if not available_accounts:
            logger.warning(f"No available accounts found for priority {context.priority.value}")
            raise NoAvailableAccountsError(f"No accounts available for priority level {context.priority.value}")

        # 优先使用指定账号 - 优化查找
        if context.preferred_account_id and context.preferred_account_id not in exclude_set:
            for account_id, health in available_accounts:
                if account_id == context.preferred_account_id:
                    return account_id

        # 根据优先级选择账号 - 优化算法
        return self._select_account_by_priority(available_accounts, context.priority, current_time)

    def _is_account_available_optimized(self, health: AccountHealth, priority: RequestPriority, current_time: datetime) -> bool:
        """优化的账号可用性检查

        Args:
            health: 账号健康度对象
            priority: 请求优先级
            current_time: 当前时间（避免重复获取）

        Returns:
            bool: 账号是否可用
        """
        # 检查基本可用性
        if health.status in [AccountHealthStatus.BANNED, AccountHealthStatus.SUSPENDED]:
            return False

        # 检查冷却期
        if health.cooldown_until and current_time < health.cooldown_until:
            return False

        # 检查频率限制
        if (health.requests_in_hour >= self.max_requests_per_hour or
            health.requests_in_day >= self.max_requests_per_day):
            return False

        # 检查优先级要求
        return health.can_handle_priority(priority)

    def _select_account_by_priority(self, available_accounts: List[tuple[str, AccountHealth]],
                                  priority: RequestPriority, current_time: datetime) -> str:
        """根据优先级选择账号的优化算法

        Args:
            available_accounts: 可用账号列表
            priority: 请求优先级
            current_time: 当前时间

        Returns:
            str: 选中的账号ID
        """
        if priority == RequestPriority.CRITICAL:
            # 关键请求：选择健康度最高的账号
            best_account = max(available_accounts, key=lambda x: x[1].health_score)
            return best_account[0]

        elif priority == RequestPriority.HIGH:
            # 高优先级：选择健康度前25%的账号
            sorted_accounts = sorted(available_accounts, key=lambda x: x[1].health_score, reverse=True)
            top_quarter = max(1, len(sorted_accounts) // 4)
            return random.choice(sorted_accounts[:top_quarter])[0]

        else:
            # 普通和低优先级：加权随机选择 - 优化版本
            return self._weighted_random_selection(available_accounts, current_time)

    def _weighted_random_selection(self, available_accounts: List[tuple[str, AccountHealth]],
                                 current_time: datetime) -> str:
        """优化的加权随机选择算法

        Args:
            available_accounts: 可用账号列表
            current_time: 当前时间

        Returns:
            str: 选中的账号ID
        """
        # 预计算权重，避免重复计算
        weighted_accounts = []
        for account_id, health in available_accounts:
            weight = health.health_score

            # 降低最近使用过的账号的权重
            if health.last_request_time:
                minutes_since_last = (current_time - health.last_request_time).total_seconds() / 60
                if minutes_since_last < 5:  # 5分钟内使用过的账号权重降低
                    weight *= 0.5

            weighted_accounts.append((account_id, weight))

        # 使用累积权重进行快速选择
        total_weight = sum(weight for _, weight in weighted_accounts)
        if total_weight <= 0:
            # 如果所有权重都为0，随机选择
            return random.choice(available_accounts)[0]

        r = random.uniform(0, total_weight)
        cumulative = 0
        for account_id, weight in weighted_accounts:
            cumulative += weight
            if r <= cumulative:
                return account_id

        # 备用选择（理论上不应该到达这里）
        return weighted_accounts[-1][0]

    async def get_client(self, account_id: str) -> Client:
        """获取指定账号的客户端（带缓存优化）

        Args:
            account_id: 账号ID

        Returns:
            Client: twikit 客户端实例

        Raises:
            AccountHealthError: 账号不存在时抛出
        """
        current_time = datetime.now()

        # 检查缓存
        if account_id in self._client_cache:
            cached_client, cache_time = self._client_cache[account_id]
            if (current_time - cache_time).total_seconds() < self._cache_ttl:
                return cached_client
            else:
                # 缓存过期，删除
                del self._client_cache[account_id]

        # 创建新客户端
        account = db.get_account(account_id)
        if not account:
            raise AccountHealthError(f"Account {account_id} not found in database")

        client = Client(language="en-US", proxy=account.get("proxy"))
        client.set_cookies(account["cookies"])
        client._account_id = account_id  # type: ignore
        client._proxy = account.get("proxy") # type: ignore

        # 缓存客户端
        self._client_cache[account_id] = (client, current_time)

        # 清理过期缓存（避免内存泄漏）
        self._cleanup_expired_cache(current_time)
        logger.debug(f'🌐use client with account_id: {account_id}, proxy: {account.get("proxy")}')
        return client

    def _cleanup_expired_cache(self, current_time: datetime) -> None:
        """清理过期的客户端缓存

        Args:
            current_time: 当前时间
        """
        expired_keys = []
        for account_id, (_, cache_time) in self._client_cache.items():
            if (current_time - cache_time).total_seconds() >= self._cache_ttl:
                expired_keys.append(account_id)

        for key in expired_keys:
            del self._client_cache[key]

    async def execute_with_smart_retry(
        self,
        func: Callable[..., Awaitable[T]],
        context: RequestContext,
        *args,
        **kwargs
    ) -> T:
        """智能重试执行API调用"""
        last_error = None
        tried_accounts = set()

        for attempt in range(context.max_retries):
            try:
                # 获取最佳账号
                account_id = await self.get_best_account(context)
                if not account_id:
                    raise ValueError("No available accounts")

                if account_id in tried_accounts:
                    # 如果已经尝试过这个账号，更新上下文排除它
                    context.exclude_account_ids.append(account_id)
                    account_id = await self.get_best_account(context)
                    if not account_id:
                        raise ValueError("No more available accounts")

                tried_accounts.add(account_id)

                # 获取客户端并执行请求
                client = await self.get_client(account_id)

                # 记录请求开始
                await self._record_request_start(account_id)

                # 执行API调用
                result = await asyncio.wait_for(
                    func(client, *args, **kwargs),
                    timeout=context.timeout
                )

                # 记录成功
                await self._record_request_success(account_id)
                self._stats["successful_requests"] += 1

                return result

            except Exception as e:
                last_error = e
                current_account = getattr(client, "_account_id", None) if 'client' in locals() else account_id

                # 记录失败
                if current_account:
                    await self._record_request_failure(current_account, e)

                self._stats["failed_requests"] += 1

                # 分析错误类型并决定是否重试
                should_retry, delay = await self._analyze_error_and_get_delay(e, attempt, current_account)

                if not should_retry or attempt >= context.max_retries - 1:
                    break

                # 等待后重试
                if delay > 0:
                    await asyncio.sleep(delay)

        # 所有重试都失败了
        if last_error:
            raise last_error
        raise ValueError("No result returned from API call")

    async def _record_request_start(self, account_id: str):
        """记录请求开始"""
        if account_id not in self._account_health:
            self._account_health[account_id] = AccountHealth(account_id=account_id)

        health = self._account_health[account_id]
        health.total_requests += 1
        health.requests_in_hour += 1
        health.requests_in_day += 1
        health.last_request_time = datetime.now()

        self._stats["total_requests"] += 1

    async def _record_request_success(self, account_id: str):
        """记录请求成功"""
        health = self._account_health[account_id]
        health.successful_requests += 1
        health.consecutive_failures = 0
        health.last_success = datetime.now()

        # 更新健康度
        await self._calculate_health_score(health)
        await self._calculate_risk_score(health)
        await self._update_account_status(health)
        
        # ✨ 立即持久化到数据库
        await self._persist_health_data(health)

    async def _record_request_failure(self, account_id: str, error: Exception):
        """记录请求失败"""
        health = self._account_health[account_id]
        health.failed_requests += 1
        health.consecutive_failures += 1
        health.last_failure = datetime.now()

        # 根据错误类型更新统计
        error_str = str(error).lower()
        if "429" in error_str or "rate limit" in error_str:
            health.rate_limit_count += 1
            health.last_rate_limit = datetime.now()
            # 设置冷却期
            health.cooldown_until = datetime.now() + timedelta(minutes=15)
            logger.warning(f"🚫 Rate limit hit for account {account_id}, cooldown until {health.cooldown_until}")
        elif "401" in error_str or "403" in error_str or "unauthorized" in error_str:
            health.auth_failure_count += 1
            logger.warning(f"🔒 Auth failure for account {account_id}")
        elif "timeout" in error_str or "connection" in error_str:
            health.network_error_count += 1
            logger.warning(f"🌐 Network error for account {account_id}")

        # 更新健康度
        await self._calculate_health_score(health)
        await self._calculate_risk_score(health)
        await self._update_account_status(health)
        
        # ✨ 立即持久化到数据库（失败后更重要）
        await self._persist_health_data(health)

    async def _analyze_error_and_get_delay(
        self,
        error: Exception,
        attempt: int,
        account_id: Optional[str] = None
    ) -> tuple[bool, float]:
        """分析错误并返回是否应该重试和延迟时间

        Args:
            error: 发生的异常
            attempt: 当前重试次数
            account_id: 账号ID（可选，用于日志记录）

        Returns:
            tuple[bool, float]: (是否应该重试, 延迟时间秒数)
        """
        error_str = str(error).lower()

        # 不应该重试的错误
        if any(keyword in error_str for keyword in ["banned", "suspended", "invalid token"]):
            return False, 0

        # 需要长时间等待的错误
        if "429" in error_str or "rate limit" in error_str:
            delay = min(300, 60 * (2 ** attempt))  # 最多等待5分钟
            return True, delay

        # 认证错误 - 可能是临时的
        if "401" in error_str or "403" in error_str:
            delay = min(60, 10 * (2 ** attempt))  # 最多等待1分钟
            return True, delay

        # 网络错误 - 快速重试
        if "timeout" in error_str or "connection" in error_str:
            delay = min(30, 2 ** attempt)  # 最多等待30秒
            return True, delay

        # 其他错误 - 标准指数退避
        delay = min(60, 5 * (2 ** attempt))  # 最多等待1分钟
        return True, delay

    async def get_account_health_report(self) -> Dict[str, Any]:
        """获取账号健康度报告

        生成包含所有账号健康度信息的详细报告，用于监控和分析。

        Returns:
            Dict[str, Any]: 包含以下信息的报告：
                - total_accounts: 总账号数
                - healthy_accounts: 健康账号数
                - warning_accounts: 警告账号数
                - critical_accounts: 危险账号数
                - suspended_accounts: 暂停账号数
                - banned_accounts: 封禁账号数
                - average_health_score: 平均健康度评分
                - average_risk_score: 平均风险评分
                - accounts: 每个账号的详细信息列表
                - stats: 全局统计信息
        """
        report = {
            "total_accounts": len(self._account_health),
            "healthy_accounts": 0,
            "warning_accounts": 0,
            "critical_accounts": 0,
            "suspended_accounts": 0,
            "banned_accounts": 0,
            "average_health_score": 0,
            "average_risk_score": 0,
            "accounts": []
        }

        total_health = 0
        total_risk = 0

        for health in self._account_health.values():
            # 统计状态分布
            if health.status == AccountHealthStatus.HEALTHY:
                report["healthy_accounts"] += 1
            elif health.status == AccountHealthStatus.WARNING:
                report["warning_accounts"] += 1
            elif health.status == AccountHealthStatus.CRITICAL:
                report["critical_accounts"] += 1
            elif health.status == AccountHealthStatus.SUSPENDED:
                report["suspended_accounts"] += 1
            elif health.status == AccountHealthStatus.BANNED:
                report["banned_accounts"] += 1

            total_health += health.health_score
            total_risk += health.risk_score

            # 添加详细信息
            report["accounts"].append({
                "account_id": health.account_id,
                "status": health.status.value,
                "health_score": round(health.health_score, 2),
                "risk_score": round(health.risk_score, 2),
                "success_rate": round(health.get_success_rate() * 100, 2),
                "total_requests": health.total_requests,
                "consecutive_failures": health.consecutive_failures,
                "last_success": health.last_success.isoformat() if health.last_success else None,
                "cooldown_until": health.cooldown_until.isoformat() if health.cooldown_until else None
            })

        if len(self._account_health) > 0:
            report["average_health_score"] = round(total_health / len(self._account_health), 2)
            report["average_risk_score"] = round(total_risk / len(self._account_health), 2)

        report["stats"] = self._stats.copy()

        return report

    async def trigger_health_update(self) -> bool:
        """手动触发一次健康度更新"""
        try:
            await self._update_all_health_scores()
            await self._cleanup_expired_cooldowns()
            await self._reset_hourly_counters()
            logger.info("Manual health update completed")
            return True
        except Exception as e:
            logger.error(f"Manual health update failed: {e}")
            return False

    async def get_monitor_status(self) -> Dict[str, Any]:
        """获取监控任务状态"""
        return {
            "is_monitoring": self._is_monitoring,
            "health_check_interval": self.health_check_interval,
            "task_running": self._health_monitor_task is not None and not self._health_monitor_task.done(),
            "task_cancelled": self._health_monitor_task.cancelled() if self._health_monitor_task else False,
            "task_exception": str(self._health_monitor_task.exception()) if self._health_monitor_task and self._health_monitor_task.done() and self._health_monitor_task.exception() else None
        }

    async def _persist_health_data(self, health: AccountHealth) -> None:
        """持久化账户健康度数据到数据库"""
        try:
            # 将 AccountHealth 对象转换为字典
            health_data = {
                'account_id': health.account_id,
                'status': health.status.value,
                'health_score': health.health_score,
                'risk_score': health.risk_score,
                'total_requests': health.total_requests,
                'successful_requests': health.successful_requests,
                'failed_requests': health.failed_requests,
                'requests_in_hour': health.requests_in_hour,
                'requests_in_day': health.requests_in_day,
                'rate_limit_count': health.rate_limit_count,
                'auth_failure_count': health.auth_failure_count,
                'network_error_count': health.network_error_count,
                'consecutive_failures': health.consecutive_failures,
            }
            
            # 转换时间字段
            for time_field in ['last_success', 'last_failure', 'last_rate_limit', 'last_request_time', 'cooldown_until', 'hour_window_start', 'day_window_start']:
                time_value = getattr(health, time_field)
                if time_value:
                    health_data[time_field] = time_value.isoformat()
                else:
                    health_data[time_field] = None
            
            # 保存到数据库
            db.save_account_health(health.account_id, health_data)
            logger.debug(f"💾 Successfully persisted health data for account {health.account_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to persist health data for account {health.account_id}: {e}")
            # 持久化失败不应该阻止正常操作，只记录错误


# 创建全局实例
account_health_manager = AccountHealthManager()

