import json
from typing import Any, Dict, List, Optional

from pydantic import BaseModel
from pathlib import Path
from datetime import datetime
from twikit import Tweet, User

# ===== 遗留模型和函数 =====
# 注意：以下模型和函数主要用于工具脚本（如 applyProxy.py）和独立后端（backend/main.py）
# 主项目使用数据库存储，不依赖这些 JSON 文件模型
# 保留这些定义是为了向后兼容和工具脚本的正常运行

class Proxy(BaseModel):
    """代理模型 - 主要用于 JSON 文件操作和工具脚本"""
    id: str
    username: str
    password: str
    proxy_address: str
    port: int
    valid: bool = True
    last_verification: str
    country_code: str
    city_name: str
    asn_name: str
    asn_number: int
    high_country_confidence: bool
    created_at: str
    status: str = "unknown"
    lastCheck: str = datetime.now().isoformat()
    responseTime: Optional[int] = None
    error: Optional[str] = None

class Account(BaseModel):
    """账户模型 - 主要用于 JSON 文件操作和工具脚本"""
    account_id: str
    auth_info_1: str
    password: str
    auth_info_2: str
    totp_secret: str
    proxy: Optional[Dict[str, Any]] = None
    status: str = "unknown"
    lastCheck: str = datetime.now().isoformat()
    proxyStatus: Optional[str] = None
    error: Optional[str] = None

def same_proxy(proxy1: Proxy, proxy2: Proxy) -> bool:
    """比较两个代理是否相同 - 用于去重"""
    props = ["id", "proxy_address", "port"]
    return all(getattr(proxy1, prop) == getattr(proxy2, prop) for prop in props)

def same_account(account1: Account, account2: Account) -> bool:
    """比较两个账户是否相同 - 用于去重"""
    props = ["account_id", "auth_info_1", "auth_info_2", "password", "totp_secret"]
    return all(getattr(account1, prop) == getattr(account2, prop) for prop in props)

def load_proxies(path: Path) -> List[Proxy]:
    """从 JSON 文件加载代理列表 - 主要用于工具脚本"""
    proxies = []
    with open(path, "r") as f:
        proxy_data = json.load(f)
        for p in proxy_data:
            _proxy = Proxy(**p)
            if not any(same_proxy(proxy, _proxy) for proxy in proxies):
                proxies.append(_proxy)
    return proxies

def load_accounts(path: Path) -> List[Account]:
    """从 JSON 文件加载账户列表 - 主要用于工具脚本"""
    accounts = []
    with open(path, "r") as f:
        account_data = json.load(f)
        for a in account_data:
            _account = Account(**a)
            if not any(same_account(_account, account) for account in accounts):
                accounts.append(_account)
    return accounts

# ===== 核心数据转换函数 =====
# 以下函数是项目的核心工具函数，被广泛使用

def tweet_to_dict(tweet: Tweet) -> Dict[str, Any]:
    return {
        "id": tweet.id,
        "text": getattr(tweet, "text", getattr(tweet, "full_text", "")),
        "created_at": tweet.created_at,
        "favorite_count": getattr(tweet, "favorite_count", 0),
        "retweet_count": getattr(tweet, "retweet_count", 0),
        "reply_count": getattr(tweet, "reply_count", 0),
        "quote_count": getattr(tweet, "quote_count", 0),
        "language": getattr(tweet, "lang", ""),
        "user": {
            "id": tweet.user.id,
            "name": tweet.user.name,
            "screen_name": tweet.user.screen_name,
            "description": tweet.user.description,
            "followers_count": tweet.user.followers_count,
            "following_count": tweet.user.following_count,
            "statuses_count": tweet.user.statuses_count,
            "profile_image_url": tweet.user.profile_image_url,
            "profile_banner_url": tweet.user.profile_banner_url,
        },
        "media": [{
            "type": m.type,
            "media_url": m.media_url,
            "expanded_url": m.expanded_url,
            "original_url": getattr(m, "original_info", None),
            "url": m.url,
            "display_url": m.display_url,
            "height": m.height,
            "width": m.width,
            "video_info": getattr(m, "video_info", {}),
            "streams": [{
                "url": stream.url,
                "bitrate": stream.bitrate,
                "content_type": getattr(stream, "content_type", None),
            } for stream in getattr(m, "streams", [])]
        } for m in getattr(tweet, "media", [])] if hasattr(tweet, "media") else []
    } 
    
def user_to_dict(user: User) -> Dict[str, Any]:
    return {
        "id": user.id,
        "name": user.name,
        "screen_name": user.screen_name,
        "description": getattr(user, 'description', ''),
        "followers_count": user.followers_count,
        "following_count": user.following_count,
        "statuses_count": user.statuses_count,
        "profile_image_url": getattr(user, 'profile_image_url', ''),
        "profile_banner_url": getattr(user, 'profile_banner_url', ''),
    }

def media_to_dict(media: Any) -> Dict[str, Any]:
    return {
        "type": getattr(media, 'type', None),
        "media_url": getattr(media, 'media_url', None),
        "expanded_url": getattr(media, 'expanded_url', None),
        "original_url": getattr(media, 'original_url', None),
        "url": getattr(media, 'url', None),
        "display_url": getattr(media, 'display_url', None),
        "height": getattr(media, 'height', None),
        "width": getattr(media, 'width', None),
        "video_info": getattr(media, "video_info", None),
        "streams": [{
            "url": getattr(stream, 'url', None),
            "bitrate": getattr(stream, 'bitrate', None),
            "content_type": getattr(stream, "content_type", None),
        } for stream in getattr(media, "streams", [])] if hasattr(media, "streams") else []
    }