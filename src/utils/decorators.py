from functools import wraps
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from fastapi import HTTPException
from src.utils.logger import setup_logger

logger = setup_logger(module_name="decorators")

# 重试装饰器
def async_retry(retries: int = 3, delay: int = 1):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {str(e)}")
                    if attempt < retries - 1:
                        await asyncio.sleep(delay)
            raise last_exception or Exception(f"Failed after {retries} attempts")
        return wrapper
    return decorator

# 缓存装饰器
class Cache:
    _cache: Dict[str, tuple] = {}
    
    @classmethod
    def cache_response(cls, expire_minutes: int = 5):
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                cache_key = f"{func.__name__}:{str(args)}:{str(kwargs)}"
                
                # 检查缓存
                if cache_key in cls._cache:
                    result, timestamp = cls._cache[cache_key]
                    if datetime.now() - timestamp < timedelta(minutes=expire_minutes):
                        return result
                
                # 执行函数
                result = await func(*args, **kwargs)
                cls._cache[cache_key] = (result, datetime.now())
                
                # 清理过期缓存
                cls._clean_expired_cache(expire_minutes)
                return result
            return wrapper
        return decorator
    
    @classmethod
    def _clean_expired_cache(cls, expire_minutes: int):
        current_time = datetime.now()
        expired_keys = [
            key for key, (_, timestamp) in cls._cache.items()
            if current_time - timestamp > timedelta(minutes=expire_minutes)
        ]
        for key in expired_keys:
            del cls._cache[key]

# 速率限制器
class RateLimiter:
    def __init__(self, requests_per_minute: int = 60):
        self.requests: Dict[str, List[float]] = {}
        self.rate_limit = requests_per_minute
    
    async def check_rate_limit(self, client_ip: str) -> bool:
        current_time = time.time()
        
        # 清理过期请求记录
        if client_ip in self.requests:
            self.requests[client_ip] = [
                t for t in self.requests[client_ip]
                if current_time - t < 60
            ]
        
        # 检查速率限制
        if client_ip in self.requests and len(self.requests[client_ip]) >= self.rate_limit:
            return False
        
        # 记录新请求
        if client_ip not in self.requests:
            self.requests[client_ip] = []
        self.requests[client_ip].append(current_time)
        return True

# 速率限制装饰器
def rate_limit(requests_per_minute: int = 60):
    limiter = RateLimiter(requests_per_minute)
    
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            client_ip = kwargs.get("client_ip", "default")
            if not await limiter.check_rate_limit(client_ip):
                raise HTTPException(
                    status_code=429,
                    detail="Rate limit exceeded. Please try again later."
                )
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# 性能监控装饰器
def monitor_performance():
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger.info(f"{func.__name__} executed in {execution_time:.2f} seconds")
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"{func.__name__} failed after {execution_time:.2f} seconds: {str(e)}")
                raise
        return wrapper
    return decorator 