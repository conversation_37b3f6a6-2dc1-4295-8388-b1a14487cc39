import logging
import sys
import argparse
from pathlib import Path
from typing import Optional

# 颜色代码
class LogColors:
    """日志颜色配置"""
    # ANSI 颜色代码
    RESET = '\033[0m'
    BOLD = '\033[1m'
    
    # 颜色定义
    BLACK = '\033[30m'
    RED = '\033[31m'
    GREEN = '\033[32m'
    YELLOW = '\033[33m'
    BLUE = '\033[34m'
    MAGENTA = '\033[35m'
    CYAN = '\033[36m'
    WHITE = '\033[37m'
    
    # 明亮颜色
    BRIGHT_RED = '\033[91m'
    BRIGHT_GREEN = '\033[92m'
    BRIGHT_YELLOW = '\033[93m'
    BRIGHT_BLUE = '\033[94m'
    BRIGHT_MAGENTA = '\033[95m'
    BRIGHT_CYAN = '\033[96m'
    
    # 背景颜色
    BG_RED = '\033[41m'
    BG_GREEN = '\033[42m'
    BG_YELLOW = '\033[43m'
    
    # 级别颜色映射
    LEVEL_COLORS = {
        'DEBUG': CYAN,
        'INFO': GREEN,
        'WARNING': YELLOW,
        'ERROR': RED,
        'CRITICAL': BOLD + BRIGHT_RED
    }
    
    # smart_execute 相关的特殊颜色
    SMART_EXECUTE = BRIGHT_BLUE
    ACCOUNT_SWITCH = BRIGHT_MAGENTA
    SUCCESS = BRIGHT_GREEN
    FAILURE = BRIGHT_RED
    RETRY = YELLOW

class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    def __init__(self, *args, use_colors=True, **kwargs):
        super().__init__(*args, **kwargs)
        self.use_colors = use_colors and self._supports_color()
    
    def _supports_color(self) -> bool:
        """检查终端是否支持颜色"""
        return (
            hasattr(sys.stdout, 'isatty') and sys.stdout.isatty() and
            sys.platform != 'win32'
        )
    
    def format(self, record):
        if not self.use_colors:
            return super().format(record)
        
        # 获取基础格式化消息
        formatted = super().format(record)
        
        # 为日志级别添加颜色
        level_color = LogColors.LEVEL_COLORS.get(record.levelname, '')
        if level_color:
            formatted = formatted.replace(
                f'[{record.levelname}]',
                f'[{level_color}{record.levelname}{LogColors.RESET}]'
            )
        
        # 为特殊内容添加颜色
        formatted = self._colorize_smart_execute_content(formatted)
        
        return formatted
    
    def _colorize_smart_execute_content(self, message: str) -> str:
        """为 smart_execute 相关内容添加特殊颜色"""
        # 账户切换相关
        if 'account' in message.lower() and ('switch' in message.lower() or 'change' in message.lower()):
            return f"{LogColors.ACCOUNT_SWITCH}{message}{LogColors.RESET}"
        
        # 成功标识符
        if message.startswith('✅'):
            return f"{LogColors.SUCCESS}{message}{LogColors.RESET}"
        
        # 失败标识符
        if message.startswith('❌') or message.startswith('🚫'):
            return f"{LogColors.FAILURE}{message}{LogColors.RESET}"
        
        # 重试标识符
        if message.startswith('🔄') or 'retry' in message.lower():
            return f"{LogColors.RETRY}{message}{LogColors.RESET}"
        
        # smart_execute 执行标识符
        if message.startswith('🎬') or message.startswith('🚀'):
            return f"{LogColors.SMART_EXECUTE}{message}{LogColors.RESET}"
        
        return message

# 全局变量存储 debug 状态
_debug_mode = False

def check_debug_args():
    """检查命令行参数中是否有 debug 标志"""
    parser = argparse.ArgumentParser(add_help=False)
    parser.add_argument("--debug", "-d", action="store_true")
    
    # 解析已知参数，忽略未知参数
    args, _ = parser.parse_known_args()
    
    global _debug_mode
    _debug_mode = args.debug
    return _debug_mode

# 初始化时检查一次命令行参数
check_debug_args()

def setup_logger(
    debug: bool = False,
    log_file: Optional[str] = None,
    module_name: Optional[str] = None
) -> logging.Logger:
    """
    设置统一的日志配置
    
    Args:
        debug: 是否启用调试模式
        log_file: 日志文件路径（可选）
        module_name: 模块名称（可选）
    
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    # 确保日志目录存在
    if log_file:
        log_dir = Path(log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取或创建logger
    logger = logging.getLogger(module_name if module_name else __name__)
    
    # 如果logger已经有处理器，说明已经配置过，直接返回
    if logger.handlers:
        return logger
    
    # 设置日志级别（优先使用全局 debug 模式）
    logger.setLevel(logging.DEBUG if (_debug_mode or debug) else logging.INFO)
    
    # 创建格式化器（优先使用彩色格式化器）
    formatter = ColoredFormatter(
        "%(asctime)s [%(levelname)s] %(name)s: %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
        use_colors=True
    )
    
    # 文件格式化器（不使用颜色）
    file_formatter = logging.Formatter(
        "%(asctime)s [%(levelname)s] %(name)s: %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    
    # 添加控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 如果指定了日志文件，添加文件处理器
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(file_formatter)  # 文件中不使用颜色
        logger.addHandler(file_handler)
    
    return logger

# 创建默认logger
default_logger = setup_logger() 