from typing import List, Dict, Any
from datetime import datetime, timedelta
from collections import Counter
import pandas as pd
import numpy as np
from textblob import TextBlob
from src.utils.logger import setup_logger

logger = setup_logger(module_name="analytics")

class TwitterAnalytics:
    @staticmethod
    def calculate_engagement_rate(tweets: List[Dict[str, Any]], followers_count: int) -> float:
        """计算互动率"""
        if not tweets or followers_count == 0:
            return 0.0
            
        total_engagement = sum(
            tweet.get("favorite_count", 0) + 
            tweet.get("retweet_count", 0) + 
            tweet.get("reply_count", 0)
            for tweet in tweets
        )
        return (total_engagement / (len(tweets) * followers_count)) * 100

    @staticmethod
    def analyze_posting_times(tweets: List[Dict[str, Any]], timezone: str = "UTC") -> List[Dict[str, Any]]:
        """分析最佳发布时间"""
        if not tweets:
            return []
            
        # 转换为 pandas DataFrame
        df = pd.DataFrame(tweets)
        df["created_at"] = pd.to_datetime(df["created_at"])
        df["hour"] = df["created_at"].dt.hour
        df["day_of_week"] = df["created_at"].dt.day_name()
        
        # 计算每个时间段的平均互动数
        engagement = df.groupby(["day_of_week", "hour"]).agg({
            "favorite_count": "mean",
            "retweet_count": "mean",
            "reply_count": "mean"
        }).reset_index()
        
        # 计算总体互动分数
        engagement["total_score"] = (
            engagement["favorite_count"] + 
            engagement["retweet_count"] * 2 + 
            engagement["reply_count"] * 3
        )
        
        # 获取前10个最佳时间
        best_times = engagement.nlargest(10, "total_score")
        
        return best_times.to_dict("records")

    @staticmethod
    def extract_hashtags(tweets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """提取热门话题标签"""
        hashtags = []
        for tweet in tweets:
            if "entities" in tweet and "hashtags" in tweet["entities"]:
                hashtags.extend([tag["text"].lower() for tag in tweet["entities"]["hashtags"]])
        
        # 统计频率
        hashtag_counts = Counter(hashtags)
        return [{"hashtag": tag, "count": count} for tag, count in hashtag_counts.most_common(10)]

    @staticmethod
    def perform_sentiment_analysis(tweets: List[Dict[str, Any]]) -> Dict[str, Any]:
        """情感分析"""
        if not tweets:
            return {"positive": 0, "neutral": 0, "negative": 0, "average_score": 0}
            
        sentiments = []
        for tweet in tweets:
            text = tweet.get("text", "")
            blob = TextBlob(text)
            sentiments.append(blob.sentiment.polarity)
        
        # 统计情感分布
        sentiment_counts = {
            "positive": len([s for s in sentiments if s > 0]),
            "neutral": len([s for s in sentiments if s == 0]),
            "negative": len([s for s in sentiments if s < 0]),
            "average_score": np.mean(sentiments) if sentiments else 0
        }
        
        return sentiment_counts

    @staticmethod
    def analyze_follower_growth(
        historical_data: List[Dict[str, Any]], 
        time_range: str = "7d"
    ) -> Dict[str, Any]:
        """分析粉丝增长趋势"""
        if not historical_data:
            return {"growth_rate": 0, "trend": "stable"}
            
        df = pd.DataFrame(historical_data)
        df["timestamp"] = pd.to_datetime(df["timestamp"])
        df = df.sort_values("timestamp")
        
        # 计算增长率
        initial_count = df["followers_count"].iloc[0]
        final_count = df["followers_count"].iloc[-1]
        days = (df["timestamp"].iloc[-1] - df["timestamp"].iloc[0]).days or 1
        
        growth_rate = ((final_count - initial_count) / initial_count) * 100
        daily_growth = (final_count - initial_count) / days
        
        # 确定趋势
        trend = "stable"
        if growth_rate > 5:
            trend = "increasing"
        elif growth_rate < -5:
            trend = "decreasing"
            
        return {
            "growth_rate": growth_rate,
            "daily_growth": daily_growth,
            "trend": trend,
            "initial_count": initial_count,
            "final_count": final_count
        }

    @staticmethod
    def analyze_content_topics(tweets: List[Dict[str, Any]], top_n: int = 10) -> List[Dict[str, Any]]:
        """内容主题分析"""
        from sklearn.feature_extraction.text import TfidfVectorizer
        from sklearn.cluster import KMeans
        
        if not tweets:
            return []
            
        # 提取文本
        texts = [tweet.get("text", "") for tweet in tweets]
        
        # TF-IDF 特征提取
        vectorizer = TfidfVectorizer(
            max_features=100,
            stop_words="english",
            ngram_range=(1, 2)
        )
        
        try:
            tfidf_matrix = vectorizer.fit_transform(texts)
            
            # K-means 聚类
            num_clusters = min(top_n, len(texts))
            kmeans = KMeans(n_clusters=num_clusters, random_state=42)
            kmeans.fit(tfidf_matrix)
            
            # 获取每个聚类的关键词
            order_centroids = kmeans.cluster_centers_.argsort()[:, ::-1]
            terms = vectorizer.get_feature_names_out()
            
            topics = []
            for i in range(num_clusters):
                top_terms = [terms[ind] for ind in order_centroids[i, :5]]
                topics.append({
                    "topic_id": i,
                    "keywords": top_terms,
                    "tweet_count": np.sum(kmeans.labels_ == i)
                })
                
            return topics
            
        except Exception as e:
            logger.error(f"Topic analysis failed: {str(e)}")
            return []

    @staticmethod
    def calculate_user_metrics(user_data: Dict[str, Any], tweets: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算用户综合指标"""
        metrics = {
            "influence_score": 0.0,
            "engagement_rate": 0.0,
            "activity_level": "low",
            "account_age_days": 0
        }
        
        try:
            # 计算账户年龄
            created_at = datetime.strptime(user_data.get("created_at", ""), "%Y-%m-%d %H:%M:%S")
            account_age = (datetime.now() - created_at).days
            metrics["account_age_days"] = account_age
            
            # 计算影响力分数
            followers = user_data.get("followers_count", 0)
            following = user_data.get("following_count", 0)
            ratio = followers / (following + 1)
            metrics["influence_score"] = min(100, ratio * 10)
            
            # 计算互动率
            metrics["engagement_rate"] = TwitterAnalytics.calculate_engagement_rate(
                tweets, followers
            )
            
            # 判断活跃度
            if len(tweets) >= 100:
                metrics["activity_level"] = "high"
            elif len(tweets) >= 30:
                metrics["activity_level"] = "medium"
                
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to calculate user metrics: {str(e)}")
            return metrics 