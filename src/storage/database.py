import sqlite3
import json
import os
from pathlib import Path
from typing import Optional, List, Dict, Any
from abc import ABC, abstractmethod
from cryptography.fernet import Fernet
from dotenv import load_dotenv
load_dotenv()
from src.utils.logger import setup_logger

logger = setup_logger(module_name="database")

# 尝试导入 PostgreSQL 驱动
try:
    import psycopg2
    import psycopg2.extras
    from psycopg2 import pool
    POSTGRESQL_AVAILABLE = True
except ImportError:
    POSTGRESQL_AVAILABLE = False
    logger.debug("psycopg2 not available, PostgreSQL support disabled")
class DatabaseInterface(ABC):
    """数据库接口抽象基类"""

    @abstractmethod
    async def save_account(
        self,
        account_id: str,
        auth_info_1: str,
        auth_info_2: str,
        password: str,
        totp_secret: str,
        proxy: str,
        cookies: Optional[Dict[str, Any]] = None,
        status: str = 'active'
    ):
        """保存账户信息"""
        pass

    @abstractmethod
    def get_account(self, account_id: str) -> Optional[Dict[str, Any]]:
        """获取指定账户信息"""
        pass

    @abstractmethod
    def get_active_accounts(self) -> List[Dict[str, Any]]:
        """获取所有活跃账户"""
        pass

    @abstractmethod
    def update_account_status(self, account_id: str, status: str):
        """更新账户状态"""
        pass

    @abstractmethod
    def save_account_health(self, account_id: str, health_data: Dict[str, Any]):
        """保存账户健康度数据"""
        pass

    @abstractmethod
    def get_account_health(self, account_id: str) -> Optional[Dict[str, Any]]:
        """获取账户健康度数据"""
        pass

    @abstractmethod
    def get_all_account_health(self) -> List[Dict[str, Any]]:
        """获取所有账户健康度数据"""
        pass

class SQLiteDatabase(DatabaseInterface):
    """SQLite 数据库实现"""

    def __init__(self, db_path: str):
        self.db_path = db_path
        self._init_cipher()
        self._init_db()
        self._ensure_schema_compliance()

    def _ensure_schema_compliance(self):
        """确保数据库符合最新的模式定义"""
        try:
            from scripts.database_manager import DatabaseManager
            import tempfile
            import json
            
            # 创建临时配置来进行模式检查
            temp_config = {
                "database": {
                    "type": "sqlite",
                    "sqlite": {"path": self.db_path}
                }
            }
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(temp_config, f)
                temp_config_path = f.name
            
            try:
                manager = DatabaseManager(temp_config_path)
                manager.migrate_and_upgrade()
            finally:
                os.unlink(temp_config_path)
                
        except Exception as e:
            logger.warning(f"模式合规性检查失败，使用传统方式: {e}")
            self._migrate_db()

    def _init_cipher(self):
        """初始化加密器"""
        fernet_key = os.getenv("FERNET_KEY")
        if not fernet_key:
            logger.error("FERNET_KEY not set in environment")
            raise ValueError("FERNET_KEY environment variable is required")
        try:
            self.cipher = Fernet(fernet_key.encode())
        except Exception as e:
            logger.error(f"Failed to initialize Fernet cipher: {e}")
            raise

    def _init_db(self):
        """初始化数据库表"""
        logger.debug("Initializing SQLite database tables")
        # 确保数据库目录存在
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)

        with sqlite3.connect(self.db_path) as conn:
            # 创建 accounts 表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS accounts (
                    account_id TEXT PRIMARY KEY,
                    auth_info_1 TEXT,
                    auth_info_2 TEXT,
                    password TEXT,
                    totp_secret TEXT,
                    cookies TEXT,
                    proxy TEXT,
                    status TEXT DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建 account_health 表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS account_health (
                    account_id TEXT PRIMARY KEY,
                    status TEXT DEFAULT 'healthy',
                    health_score REAL DEFAULT 100.0,
                    risk_score REAL DEFAULT 0.0,
                    
                    -- 请求统计
                    total_requests INTEGER DEFAULT 0,
                    successful_requests INTEGER DEFAULT 0,
                    failed_requests INTEGER DEFAULT 0,
                    
                    -- 频率限制统计
                    requests_in_hour INTEGER DEFAULT 0,
                    requests_in_day INTEGER DEFAULT 0,
                    hour_window_start TIMESTAMP,
                    day_window_start TIMESTAMP,
                    
                    -- 错误统计
                    rate_limit_count INTEGER DEFAULT 0,
                    auth_failure_count INTEGER DEFAULT 0,
                    network_error_count INTEGER DEFAULT 0,
                    consecutive_failures INTEGER DEFAULT 0,
                    
                    -- 时间记录
                    last_success TIMESTAMP,
                    last_failure TIMESTAMP,
                    last_rate_limit TIMESTAMP,
                    last_request_time TIMESTAMP,
                    cooldown_until TIMESTAMP,
                    
                    -- 元数据
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (account_id) REFERENCES accounts(account_id) ON DELETE CASCADE
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_account_health_status ON account_health(status)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_account_health_score ON account_health(health_score)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_account_health_cooldown ON account_health(cooldown_until)")
            
            logger.debug("SQLite database tables initialized")
            
    def _migrate_db(self):
        """迁移数据库结构"""
        logger.debug("Checking SQLite database structure")
        with sqlite3.connect(self.db_path) as conn:
            # 检查 accounts 表的列结构
            cursor = conn.execute("PRAGMA table_info(accounts)")
            columns = {row[1] for row in cursor.fetchall()}

            # 添加 status 列
            if "status" not in columns:
                logger.info("Adding status column to accounts table")
                try:
                    conn.execute("ALTER TABLE accounts ADD COLUMN status TEXT DEFAULT 'active'")
                    conn.execute("UPDATE accounts SET status = 'active' WHERE status IS NULL")
                    logger.info("Successfully added status column")
                except sqlite3.Error as e:
                    logger.error(f"Failed to add status column: {e}")
                    raise

            # 添加 created_at 列
            if "created_at" not in columns:
                logger.info("Adding created_at column to accounts table")
                try:
                    conn.execute("ALTER TABLE accounts ADD COLUMN created_at TIMESTAMP")
                    conn.execute("UPDATE accounts SET created_at = datetime('now') WHERE created_at IS NULL")
                    logger.info("Successfully added created_at column")
                except sqlite3.Error as e:
                    logger.error(f"Failed to add created_at column: {e}")
                    raise

            # 添加 updated_at 列
            if "updated_at" not in columns:
                logger.info("Adding updated_at column to accounts table")
                try:
                    conn.execute("ALTER TABLE accounts ADD COLUMN updated_at TIMESTAMP")
                    conn.execute("UPDATE accounts SET updated_at = datetime('now') WHERE updated_at IS NULL")
                    logger.info("Successfully added updated_at column")
                except sqlite3.Error as e:
                    logger.error(f"Failed to add updated_at column: {e}")
                    raise

            conn.commit()
            logger.debug("SQLite database migration completed")

    def _encrypt_data(self, data: str) -> str:
        """加密字符串数据"""
        return self.cipher.encrypt(data.encode()).decode()

    def _decrypt_data(self, encrypted_data: str) -> str:
        """解密字符串数据"""
        return self.cipher.decrypt(encrypted_data.encode()).decode()

    async def save_account(
        self,
        account_id: str,
        auth_info_1: str,
        auth_info_2: str,
        password: str,
        totp_secret: str,
        proxy: str,
        cookies: Optional[Dict[str, Any]] = None,
        status: str = 'active'
    ):
        """保存账户信息到 SQLite 数据库"""
        logger.debug(f"Saving account {account_id} to SQLite database")

        if cookies:
            encrypted_cookies = self._encrypt_data(json.dumps(cookies))
        else:
            encrypted_cookies = None

        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO accounts (
                    account_id, auth_info_1, auth_info_2, password,
                    totp_secret, cookies, proxy, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                account_id, auth_info_1, auth_info_2, password,
                totp_secret, encrypted_cookies, proxy, status
            ))
            logger.debug(f"Account {account_id} saved to SQLite with status '{status}'")

    def get_account(self, account_id: str) -> Optional[Dict[str, Any]]:
        """获取指定账号的信息"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM accounts WHERE account_id = ? AND status = 'active'",
                (account_id,)
            )
            row = cursor.fetchone()
            if row:
                account = dict(row)
                try:
                    if account["cookies"]:
                        account["cookies"] = json.loads(self.cipher.decrypt(account["cookies"].encode()).decode())
                    return account
                except Exception as e:
                    logger.error(f"Failed to decrypt cookies for account {account_id}: {str(e)}")
                    return None
        return None

    def get_active_accounts(self) -> List[Dict[str, Any]]:
        """获取所有活跃状态的账号"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM accounts WHERE status = 'active'")
            accounts = []
            for row in cursor.fetchall():
                account = dict(row)
                try:
                    if account["cookies"]:
                        account["cookies"] = json.loads(self.cipher.decrypt(account["cookies"].encode()).decode())
                    accounts.append(account)
                except Exception as e:
                    logger.error(f"Failed to decrypt cookies for account {account['account_id']}: {str(e)}")
                    continue
            return accounts

    def update_account_status(self, account_id: str, status: str):
        """更新账号状态"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                "UPDATE accounts SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE account_id = ?",
                (status, account_id)
            )
            conn.commit()
            logger.debug(f"Updated SQLite account {account_id} status to {status}")

    def save_account_health(self, account_id: str, health_data: Dict[str, Any]):
        """保存账户健康度数据"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 准备字段和值
            fields = [
                'account_id', 'status', 'health_score', 'risk_score',
                'total_requests', 'successful_requests', 'failed_requests',
                'requests_in_hour', 'requests_in_day', 'hour_window_start', 'day_window_start',
                'rate_limit_count', 'auth_failure_count', 'network_error_count', 'consecutive_failures',
                'last_success', 'last_failure', 'last_rate_limit', 'last_request_time', 'cooldown_until'
            ]
            
            # 准备数据，使用默认值处理缺失的字段
            values: List[str|int|float|None] = [account_id]  # account_id 始终存在
            placeholders = ['?']
            
            for field in fields[1:]:  # 跳过 account_id
                if field in health_data:
                    values.append(health_data[field])
                    placeholders.append('?')
                else:
                    # 为缺失字段使用默认值
                    if field == 'status':
                        values.append('healthy')
                    elif field in ['health_score']:
                        values.append(100.0)
                    elif field in ['risk_score']:
                        values.append(0.0)
                    elif field.endswith('_count') or field.endswith('_requests') or field == 'consecutive_failures':
                        values.append(0)
                    else:
                        values.append(None)
                    placeholders.append('?')
            
            # 执行 INSERT OR REPLACE
            query = f"""
                INSERT OR REPLACE INTO account_health 
                ({', '.join(fields)}, updated_at)
                VALUES ({', '.join(placeholders)}, CURRENT_TIMESTAMP)
            """
            
            cursor.execute(query, values)
            conn.commit()
            logger.debug(f"💾 Saved health data for account {account_id}")

    def get_account_health(self, account_id: str) -> Optional[Dict[str, Any]]:
        """获取账户健康度数据"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM account_health WHERE account_id = ?",
                (account_id,)
            )
            row = cursor.fetchone()
            
            if row:
                # 获取列名
                columns = [desc[0] for desc in cursor.description]
                health_data = dict(zip(columns, row))
                logger.debug(f"📈 Retrieved health data for account {account_id}")
                return health_data
            else:
                logger.debug(f"🔍 No health data found for account {account_id}")
                return None

    def get_all_account_health(self) -> List[Dict[str, Any]]:
        """获取所有账户健康度数据"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM account_health ORDER BY health_score DESC")
            rows = cursor.fetchall()
            
            if rows:
                columns = [desc[0] for desc in cursor.description]
                health_data_list = [dict(zip(columns, row)) for row in rows]
                logger.debug(f"📈 Retrieved health data for {len(health_data_list)} accounts")
                return health_data_list
            else:
                logger.debug("🔍 No health data found")
                return []


class PostgreSQLDatabase(DatabaseInterface):
    """PostgreSQL 数据库实现"""

    def __init__(self, connection_string: str, pool_size: int = 10, max_overflow: int = 20):
        if not POSTGRESQL_AVAILABLE:
            raise ImportError("psycopg2 is required for PostgreSQL support. Install with: pip install psycopg2-binary")

        self.connection_string = connection_string
        self._init_cipher()
        self._init_connection_pool(pool_size, max_overflow)
        self._init_db()
        self._migrate_db()

    def _init_cipher(self):
        """初始化加密器"""
        fernet_key = os.getenv("FERNET_KEY")
        if not fernet_key:
            logger.error("FERNET_KEY not set in environment")
            raise ValueError("FERNET_KEY environment variable is required")
        try:
            self.cipher = Fernet(fernet_key.encode())
        except Exception as e:
            logger.error(f"Failed to initialize Fernet cipher: {e}")
            raise

    def _init_connection_pool(self, pool_size: int, max_overflow: int):
        """初始化连接池"""
        try:
            self.connection_pool = psycopg2.pool.ThreadedConnectionPool(
                1, pool_size + max_overflow,
                self.connection_string
            )
            logger.info(f"PostgreSQL connection pool initialized with {pool_size} connections")
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL connection pool: {e}")
            raise

    def _get_connection(self):
        """从连接池获取连接"""
        return self.connection_pool.getconn()

    def _put_connection(self, conn):
        """将连接返回到连接池"""
        self.connection_pool.putconn(conn)

    def _init_db(self):
        """初始化数据库表"""
        logger.debug("Initializing PostgreSQL database tables")
        conn = self._get_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS accounts (
                        account_id VARCHAR(255) PRIMARY KEY,
                        auth_info_1 TEXT,
                        auth_info_2 TEXT,
                        password TEXT,
                        totp_secret TEXT,
                        cookies TEXT,
                        proxy TEXT,
                        status VARCHAR(50) DEFAULT 'active',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                conn.commit()
                logger.debug("PostgreSQL accounts table initialized")
        finally:
            self._put_connection(conn)

    def _migrate_db(self):
        """迁移数据库结构"""
        logger.debug("Checking PostgreSQL database structure")
        conn = self._get_connection()
        try:
            with conn.cursor() as cursor:
                # 检查 status 列是否存在
                cursor.execute("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_name = 'accounts' AND column_name = 'status'
                """)
                if not cursor.fetchone():
                    logger.info("Adding status column to PostgreSQL accounts table")
                    cursor.execute("ALTER TABLE accounts ADD COLUMN status VARCHAR(50) DEFAULT 'active'")
                    cursor.execute("UPDATE accounts SET status = 'active' WHERE status IS NULL")
                    conn.commit()
                    logger.info("Successfully added status column to PostgreSQL accounts table")
        except Exception as e:
            logger.error(f"Failed to migrate PostgreSQL database: {e}")
            raise
        finally:
            self._put_connection(conn)

    def _encrypt_data(self, data: str) -> str:
        """加密字符串数据"""
        return self.cipher.encrypt(data.encode()).decode()

    def _decrypt_data(self, encrypted_data: str) -> str:
        """解密字符串数据"""
        return self.cipher.decrypt(encrypted_data.encode()).decode()

    async def save_account(
        self,
        account_id: str,
        auth_info_1: str,
        auth_info_2: str,
        password: str,
        totp_secret: str,
        proxy: str,
        cookies: Optional[Dict[str, Any]] = None,
        status: str = 'active'
    ):
        """保存账户信息到 PostgreSQL 数据库"""
        logger.debug(f"Saving account {account_id} to PostgreSQL database")

        if cookies:
            encrypted_cookies = self._encrypt_data(json.dumps(cookies))
        else:
            encrypted_cookies = None

        conn = self._get_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO accounts (
                        account_id, auth_info_1, auth_info_2, password,
                        totp_secret, cookies, proxy, status
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (account_id) DO UPDATE SET
                        auth_info_1 = EXCLUDED.auth_info_1,
                        auth_info_2 = EXCLUDED.auth_info_2,
                        password = EXCLUDED.password,
                        totp_secret = EXCLUDED.totp_secret,
                        cookies = EXCLUDED.cookies,
                        proxy = EXCLUDED.proxy,
                        status = EXCLUDED.status,
                        updated_at = CURRENT_TIMESTAMP
                """, (
                    account_id, auth_info_1, auth_info_2, password,
                    totp_secret, encrypted_cookies, proxy, status
                ))
                conn.commit()
                logger.debug(f"Account {account_id} saved to PostgreSQL with status '{status}'")
        finally:
            self._put_connection(conn)

    def get_account(self, account_id: str) -> Optional[Dict[str, Any]]:
        """获取指定账号的信息"""
        conn = self._get_connection()
        try:
            with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                cursor.execute(
                    "SELECT * FROM accounts WHERE account_id = %s AND status = 'active'",
                    (account_id,)
                )
                row = cursor.fetchone()
                if row:
                    account = dict(row)
                    try:
                        if account["cookies"]:
                            account["cookies"] = json.loads(self.cipher.decrypt(account["cookies"].encode()).decode())
                        return account
                    except Exception as e:
                        logger.error(f"Failed to decrypt cookies for account {account_id}: {str(e)}")
                        return None
        finally:
            self._put_connection(conn)
        return None

    def get_active_accounts(self) -> List[Dict[str, Any]]:
        """获取所有活跃状态的账号"""
        conn = self._get_connection()
        try:
            with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                cursor.execute("SELECT * FROM accounts WHERE status = 'active'")
                accounts = []
                for row in cursor.fetchall():
                    account = dict(row)
                    try:
                        if account["cookies"]:
                            account["cookies"] = json.loads(self.cipher.decrypt(account["cookies"].encode()).decode())
                        accounts.append(account)
                    except Exception as e:
                        logger.error(f"Failed to decrypt cookies for account {account['account_id']}: {str(e)}")
                        continue
                return accounts
        finally:
            self._put_connection(conn)

    def update_account_status(self, account_id: str, status: str):
        """更新账号状态"""
        conn = self._get_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    "UPDATE accounts SET status = %s, updated_at = CURRENT_TIMESTAMP WHERE account_id = %s",
                    (status, account_id)
                )
                conn.commit()
                logger.debug(f"Updated PostgreSQL account {account_id} status to {status}")
        finally:
            self._put_connection(conn)
    def save_account_health(self):
        """保存账户健康度数据"""
        pass

    def get_account_health(self):
        """获取账户健康度数据"""
        pass

    def get_all_account_health(self):
        """获取所有账户健康度数据"""
        pass


class JSONDatabase(DatabaseInterface):
    """JSON 文件数据库实现"""

    def __init__(self, json_path: str):
        self.json_path = Path(json_path)
        self.json_path.parent.mkdir(parents=True, exist_ok=True)
        self._init_cipher()
        self._init_json_file()

    def _init_cipher(self):
        """初始化加密器"""
        fernet_key = os.getenv("FERNET_KEY")
        if not fernet_key:
            logger.error("FERNET_KEY not set in environment")
            raise ValueError("FERNET_KEY environment variable is required")
        try:
            self.cipher = Fernet(fernet_key.encode())
        except Exception as e:
            logger.error(f"Failed to initialize Fernet cipher: {e}")
            raise

    def _init_json_file(self):
        """初始化 JSON 文件"""
        if not self.json_path.exists():
            self._save_data({"accounts": []})
            logger.debug("JSON database file initialized")

    def _load_data(self) -> Dict[str, Any]:
        """加载 JSON 数据"""
        try:
            with open(self.json_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {"accounts": []}

    def _save_data(self, data: Dict[str, Any]):
        """保存 JSON 数据"""
        with open(self.json_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

    def _encrypt_data(self, data: str) -> str:
        """加密字符串数据"""
        return self.cipher.encrypt(data.encode()).decode()

    def _decrypt_data(self, encrypted_data: str) -> str:
        """解密字符串数据"""
        return self.cipher.decrypt(encrypted_data.encode()).decode()

    async def save_account(
        self,
        account_id: str,
        auth_info_1: str,
        auth_info_2: str,
        password: str,
        totp_secret: str,
        proxy: str,
        cookies: Optional[Dict[str, Any]] = None,
        status: str = 'active'
    ):
        """保存账户信息到 JSON 文件"""
        logger.debug(f"Saving account {account_id} to JSON database")

        data = self._load_data()

        # 查找现有账户或创建新账户
        account_found = False
        for i, account in enumerate(data["accounts"]):
            if account["account_id"] == account_id:
                data["accounts"][i] = {
                    "account_id": account_id,
                    "auth_info_1": auth_info_1,
                    "auth_info_2": auth_info_2,
                    "password": password,
                    "totp_secret": totp_secret,
                    "cookies": self._encrypt_data(json.dumps(cookies)) if cookies else None,
                    "proxy": proxy,
                    "status": status,
                    "updated_at": json.dumps({"$date": None})  # 简化的时间戳
                }
                account_found = True
                break

        if not account_found:
            data["accounts"].append({
                "account_id": account_id,
                "auth_info_1": auth_info_1,
                "auth_info_2": auth_info_2,
                "password": password,
                "totp_secret": totp_secret,
                "cookies": self._encrypt_data(json.dumps(cookies)) if cookies else None,
                "proxy": proxy,
                "status": status,
                "created_at": json.dumps({"$date": None}),
                "updated_at": json.dumps({"$date": None})
            })

        self._save_data(data)
        logger.debug(f"Account {account_id} saved to JSON with status '{status}'")

    def get_account(self, account_id: str) -> Optional[Dict[str, Any]]:
        """获取指定账号的信息"""
        data = self._load_data()
        for account in data["accounts"]:
            if account["account_id"] == account_id and account.get("status") == "active":
                try:
                    if account["cookies"]:
                        account["cookies"] = json.loads(self._decrypt_data(account["cookies"]))
                    return account
                except Exception as e:
                    logger.error(f"Failed to decrypt cookies for account {account_id}: {str(e)}")
                    return None
        return None

    def get_active_accounts(self) -> List[Dict[str, Any]]:
        """获取所有活跃状态的账号"""
        data = self._load_data()
        accounts = []
        for account in data["accounts"]:
            if account.get("status") == "active":
                try:
                    if account["cookies"]:
                        account["cookies"] = json.loads(self._decrypt_data(account["cookies"]))
                    accounts.append(account)
                except Exception as e:
                    logger.error(f"Failed to decrypt cookies for account {account['account_id']}: {str(e)}")
                    continue
        return accounts

    def update_account_status(self, account_id: str, status: str):
        """更新账号状态"""
        data = self._load_data()
        for account in data["accounts"]:
            if account["account_id"] == account_id:
                account["status"] = status
                account["updated_at"] = json.dumps({"$date": None})
                break
        self._save_data(data)
        logger.debug(f"Updated JSON account {account_id} status to {status}")
    def save_account_health(self):
        """保存账户健康度数据"""
        pass

    def get_account_health(self):
        """获取账户健康度数据"""
        pass

    def get_all_account_health(self):
        """获取所有账户健康度数据"""
        pass

class Database:
    """数据库工厂类和统一接口"""
    _instance = None
    _initialized = False

    def __new__(cls, config_or_path=None):
        if cls._instance is None:
            cls._instance = super(Database, cls).__new__(cls)
        return cls._instance

    def __init__(self, config_or_path=None):
        if not self._initialized:
            if config_or_path is None:
                # 使用默认配置
                from src.config import Config
                config_obj = Config()
                self.db_impl = self._create_database_impl(config_obj)
            elif isinstance(config_or_path, str):
                # 向后兼容：如果传入的是字符串路径，假设是 SQLite
                self.db_impl = SQLiteDatabase(config_or_path)
            else:
                # 传入的是配置对象
                self.db_impl = self._create_database_impl(config_or_path)
            self._initialized = True

    def _create_database_impl(self, config) -> DatabaseInterface:
        """根据配置创建数据库实现"""
        db_type = config.get_database_type()

        if db_type == "postgresql":
            if not POSTGRESQL_AVAILABLE:
                logger.error("PostgreSQL requested but psycopg2 not available")
                raise ImportError("psycopg2 is required for PostgreSQL support")

            pg_config = config.get_database_config("postgresql")
            connection_string = config.get_postgresql_connection_string()
            pool_size = pg_config.get("pool_size", 10)
            max_overflow = pg_config.get("max_overflow", 20)

            logger.info(f"Initializing PostgreSQL database: {connection_string}")
            return PostgreSQLDatabase(connection_string, pool_size, max_overflow)

        elif db_type == "json":
            json_path = config.get_json_storage_path() + "/accounts.json"
            logger.info(f"Initializing JSON database: {json_path}")
            return JSONDatabase(json_path)

        else:  # 默认使用 SQLite
            sqlite_path = config.get_sqlite_path()
            logger.info(f"Initializing SQLite database: {sqlite_path}")
            return SQLiteDatabase(sqlite_path)

    # 代理所有方法到具体实现
    async def save_account(self, *args, **kwargs):
        return await self.db_impl.save_account(*args, **kwargs)

    def get_account(self, *args, **kwargs):
        return self.db_impl.get_account(*args, **kwargs)

    def get_active_accounts(self, *args, **kwargs):
        return self.db_impl.get_active_accounts(*args, **kwargs)

    def update_account_status(self, *args, **kwargs):
        return self.db_impl.update_account_status(*args, **kwargs)
    def get_all_account_health(self, *args, **kwargs):
        return self.db_impl.get_all_account_health(*args, **kwargs)
    def save_account_health(self, *args, **kwargs):
        return self.db_impl.save_account_health(*args, **kwargs)