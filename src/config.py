import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
from src.utils.logger import setup_logger

logger = setup_logger(module_name="config")

class Config:
    _instance = None
    _initialized = False

    def __new__(cls, config_path="config.json"):
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
        return cls._instance

    def __init__(self, config_path="config.json"):
        if not self._initialized:
            self.config_path = Path(config_path)
            self.load_config()
            self._initialized = True

    def load_config(self):
        """加载配置文件，支持环境变量覆盖"""
        if not self.config_path.exists():
            raise FileNotFoundError("config.json not found")

        with self.config_path.open() as f:
            config = json.load(f)

        # 基础配置
        self.accounts = config.get("accounts", [])
        self.proxy_pool = config.get("proxy_pool", [])
        self.backup = config.get("backup", {})

        # 数据库配置 - 支持多种数据库类型
        self.database = self._load_database_config(config.get("database", {}))

        # JSON 存储配置
        self.json_storage = config.get("json_storage", {
            "enabled": True,
            "path": "data/json",
            "backup_enabled": True
        })

        # 加载 accountsFile 中的账户数据
        accounts_file = config.get("accountsFile")
        if accounts_file:
            self._load_accounts_file(accounts_file)

    def _load_database_config(self, db_config: Dict[str, Any]) -> Dict[str, Any]:
        """加载数据库配置，支持环境变量覆盖"""
        # 默认配置
        default_config = {
            "type": "sqlite",  # sqlite, postgresql, json
            "sqlite": {
                "path": "data/sqlite/twx.db"
            },
            "postgresql": {
                "host": "localhost",
                "port": 5432,
                "database": "twx",
                "username": "postgres",
                "password": "",
                "pool_size": 10,
                "max_overflow": 20
            }
        }

        # 合并配置文件中的设置
        config = {**default_config, **db_config}

        # 环境变量覆盖
        # 数据库类型
        if os.getenv("DATABASE_TYPE"):
            config["type"] = os.getenv("DATABASE_TYPE")

        # PostgreSQL 配置
        if os.getenv("DATABASE_URL"):
            # 解析 DATABASE_URL (格式: postgresql://user:pass@host:port/dbname)
            config["type"] = "postgresql"
            config["postgresql"]["url"] = os.getenv("DATABASE_URL")
        else:
            # 单独的环境变量
            if os.getenv("POSTGRES_HOST"):
                config["postgresql"]["host"] = os.getenv("POSTGRES_HOST")
            postgres_port = os.getenv("POSTGRES_PORT")
            if postgres_port:
                config["postgresql"]["port"] = int(postgres_port)
            if os.getenv("POSTGRES_DB"):
                config["postgresql"]["database"] = os.getenv("POSTGRES_DB")
            if os.getenv("POSTGRES_USER"):
                config["postgresql"]["username"] = os.getenv("POSTGRES_USER")
            if os.getenv("POSTGRES_PASSWORD"):
                config["postgresql"]["password"] = os.getenv("POSTGRES_PASSWORD")

        # SQLite 配置
        if os.getenv("SQLITE_PATH"):
            config["sqlite"]["path"] = os.getenv("SQLITE_PATH")

        logger.info(f"数据库配置加载完成 - 类型: {config['type']}")
        return config

    def _load_accounts_file(self, accounts_file: str):
        """加载账户文件"""
        accounts_file_path = Path(accounts_file)
        if accounts_file_path.exists():
            with accounts_file_path.open() as f:
                file_accounts = json.load(f)
                if isinstance(file_accounts, list):
                    self.accounts.extend(file_accounts)
                    logger.info(f"从 {accounts_file} 加载了 {len(file_accounts)} 个账户")
                else:
                    logger.warning(f"Invalid format in {accounts_file}: expected a list")
        else:
            logger.warning(f"Accounts file {accounts_file} not found")

    def get_account(self, account_id: str) -> Optional[Dict[str, Any]]:
        """获取指定账户信息"""
        for account in self.accounts:
            if account["account_id"] == account_id:
                return account
        return None

    def get_database_type(self) -> str:
        """获取当前数据库类型"""
        return self.database.get("type", "sqlite")

    def get_database_config(self, db_type: Optional[str] = None) -> Dict[str, Any]:
        """获取指定数据库类型的配置"""
        if db_type is None:
            db_type = self.get_database_type()
        return self.database.get(db_type, {})

    def is_json_storage_enabled(self) -> bool:
        """检查是否启用 JSON 存储"""
        return self.json_storage.get("enabled", True)

    def get_json_storage_path(self) -> str:
        """获取 JSON 存储路径"""
        return self.json_storage.get("path", "data/json")

    def is_json_backup_enabled(self) -> bool:
        """检查是否启用 JSON 备份"""
        return self.json_storage.get("backup_enabled", True)

    def get_postgresql_connection_string(self) -> str:
        """获取 PostgreSQL 连接字符串"""
        pg_config = self.get_database_config("postgresql")

        # 如果有完整的 URL，直接使用
        if "url" in pg_config:
            return pg_config["url"]

        # 否则构建连接字符串
        host = pg_config.get("host", "localhost")
        port = pg_config.get("port", 5432)
        database = pg_config.get("database", "twx")
        username = pg_config.get("username", "postgres")
        password = pg_config.get("password", "")

        if password:
            return f"postgresql://{username}:{password}@{host}:{port}/{database}"
        else:
            return f"postgresql://{username}@{host}:{port}/{database}"

    def get_sqlite_path(self) -> str:
        """获取 SQLite 数据库路径"""
        sqlite_config = self.get_database_config("sqlite")
        return sqlite_config.get("path", "data/sqlite/twx.db")