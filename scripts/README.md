# Twx 数据库管理脚本

这个目录包含了 Twx 项目的数据库管理工具，用于初始化、迁移和维护数据库。

## 📁 文件说明

- `database_manager.py` - 统一数据库管理工具
- `schema.sql` - 完整的数据库结构定义文件
- `README.md` - 本使用说明文档

## 🚀 快速开始

### 1. 初始化数据库

第一次使用时，需要初始化数据库结构：

```bash
# 设置数据库（自动验证）
python scripts/database_manager.py setup

# 设置数据库（跳过验证，加快速度）
python scripts/database_manager.py setup --no-verify

# 指定配置文件
python scripts/database_manager.py setup --config /path/to/config.json
```

### 2. 检查数据库状态

```bash
# 检查当前数据库类型和状态
python scripts/database_manager.py status

# 验证数据库结构完整性
python scripts/database_manager.py verify
```

### 3. 数据库迁移

当更新了数据库结构时，运行迁移：

```bash
# 自动检测并执行迁移
python scripts/database_manager.py migrate
```

### 4. 从 SQLite 迁移到 PostgreSQL

```bash
# 迁移数据（自动备份）
python scripts/database_manager.py migrate-to-pg

# 迁移数据（跳过备份）
python scripts/database_manager.py migrate-to-pg --no-backup
```

## 📋 支持的命令

| 命令 | 描述 | 选项 |
|------|------|------|
| `setup` | 初始化所有数据库表和索引 | `--no-verify`, `--config` |
| `migrate` | 检测并执行数据库结构迁移 | `--config` |
| `verify` | 验证数据库结构完整性 | `--config` |
| `migrate-to-pg` | 从 SQLite 迁移到 PostgreSQL | `--no-backup`, `--config` |
| `status` | 显示当前数据库类型和状态 | `--config` |

## 🛠️ 支持的数据库类型

### SQLite (默认)
- 文件路径：`data/accounts.db`
- 适合：开发环境、小规模部署
- 配置示例：
```json
{
  "database": {
    "type": "sqlite",
    "sqlite": {
      "path": "data/accounts.db"
    }
  }
}
```

### PostgreSQL
- 适合：生产环境、大规模部署
- 支持连接池和高并发
- 配置示例：
```json
{
  "database": {
    "type": "postgresql",
    "postgresql": {
      "host": "localhost",
      "port": 5432,
      "database": "Twx",
      "user": "username",
      "password": "password",
      "pool_size": 10
    }
  }
}
```

### JSON 存储
- 适合：简单部署、无数据库环境
- 配置示例：
```json
{
  "database": {
    "type": "json",
    "json": {
      "storage_path": "data"
    }
  }
}
```

## 📊 数据库表结构

### accounts - 主要账户表
- `account_id` (主键) - 账户ID
- `auth_info_1/2` - 认证信息
- `password` - 密码
- `totp_secret` - 双因子认证密钥
- `cookies` - 加密存储的登录cookies
- `proxy` - 代理设置
- `status` - 账户状态
- `proxy_status` - 代理状态
- `last_check` - 最后检查时间
- `proxy_latency` - 代理延迟

### proxies - 代理服务器表
- `id` (主键) - 代理ID
- `ip/port` - 代理地址和端口
- `username/password` - 代理认证
- `country_code/city_name` - 地理位置
- `assigned_to` - 分配给的账户
- `use_count` - 使用次数
- `status` - 代理状态

### proxy_status - 代理状态跟踪
- 用于详细的代理健康监控
- 记录响应时间和错误信息

### schema_versions - 版本管理
- 跟踪数据库模式版本
- 支持自动迁移

## 🔧 常见用法场景

### 开发环境初始化
```bash
# 首次设置开发环境
python scripts/database_manager.py setup --config config.json
python scripts/database_manager.py verify
```

### 生产环境部署
```bash
# 1. 配置 PostgreSQL
# 2. 初始化数据库
python scripts/database_manager.py setup --config production.json
python scripts/database_manager.py verify

# 3. 如果从 SQLite 迁移
python scripts/database_manager.py migrate-to-pg --config production.json
```

### 版本更新后
```bash
# 检查是否需要迁移
python scripts/database_manager.py status

# 执行迁移（如果需要）
python scripts/database_manager.py migrate
```

## ⚠️ 注意事项

1. **备份重要性**: 生产环境迁移前务必备份数据
2. **权限要求**: 确保有数据库创建表和索引的权限
3. **环境变量**: 需要设置 `FERNET_KEY` 用于数据加密
4. **依赖安装**: PostgreSQL 需要安装 `psycopg2-binary`

## 🐛 故障排除

### 常见错误

**错误**: `psycopg2 not available`
```bash
# 解决方案：安装 PostgreSQL 驱动
pip install psycopg2-binary
```

**错误**: `FERNET_KEY not set`
```bash
# 解决方案：设置加密密钥
export FERNET_KEY="your-fernet-key-here"
```

**错误**: `Permission denied`
```bash
# 解决方案：检查数据库权限或文件路径权限
chmod 755 data/
```

### 日志查看
脚本运行时会输出详细日志，包含：
- ✅ 成功操作
- ⚠️ 警告信息  
- ❌ 错误详情

### 获取帮助
```bash
python scripts/database_manager.py --help
```

## 📞 技术支持

如果遇到问题，请检查：
1. 配置文件格式是否正确
2. 数据库连接是否正常
3. 必要的环境变量是否设置
4. 日志输出中的详细错误信息