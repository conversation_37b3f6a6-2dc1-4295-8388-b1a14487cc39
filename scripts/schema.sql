-- Twx 统一数据库 Schema
-- 此文件定义了项目的完整数据库结构
-- 版本: 1.0

-- 主要账户表
CREATE TABLE IF NOT EXISTS accounts (
    account_id VARCHAR(255) PRIMARY KEY,
    auth_info_1 TEXT,
    auth_info_2 TEXT,
    password TEXT,
    totp_secret TEXT,
    cookies TEXT,                     -- 加密存储的 cookies JSON
    proxy TEXT,                       -- 代理设置
    status VARCHAR(50) DEFAULT 'active',
    proxy_status VARCHAR(50),         -- 代理状态（健康检查使用）
    last_check TIMESTAMP,             -- 最后检查时间
    proxy_latency INTEGER,            -- 代理延迟（毫秒）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);


-- 代理服务器表
CREATE TABLE IF NOT EXISTS proxies (
    id SERIAL PRIMARY KEY,
    ip VARCHAR(45) NOT NULL,          -- IP地址
    port INTEGER NOT NULL,            -- 端口号
    username TEXT,                    -- 代理用户名
    password TEXT,                    -- 代理密码
    proxy_url TEXT,                   -- 完整代理URL
    country_code VARCHAR(10),         -- 国家代码
    city_name VARCHAR(100),           -- 城市名称
    assigned_to VARCHAR(255),         -- 分配给的账户ID
    assigned_at TIMESTAMP,            -- 分配时间
    use_count INTEGER DEFAULT 0,      -- 使用次数
    last_used TIMESTAMP,              -- 最后使用时间
    status VARCHAR(50) DEFAULT 'active', -- 代理状态
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (assigned_to) REFERENCES accounts(account_id)
);

-- 代理状态跟踪表（可选，主要用于 PostgreSQL）
CREATE TABLE IF NOT EXISTS proxy_status (
    id SERIAL PRIMARY KEY,
    account_id VARCHAR(255) REFERENCES accounts(account_id),
    proxy_url TEXT,
    status VARCHAR(50),               -- 'working', 'failed', 'unknown'
    response_time INTEGER,            -- 响应时间（毫秒）
    error_message TEXT,
    last_check TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 数据库版本管理表
CREATE TABLE IF NOT EXISTS schema_versions (
    component VARCHAR(50) PRIMARY KEY,
    version INTEGER NOT NULL,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 账户健康度表
CREATE TABLE IF NOT EXISTS account_health (
    account_id VARCHAR(255) PRIMARY KEY,
    status VARCHAR(50) DEFAULT 'healthy',
    health_score DECIMAL(5,2) DEFAULT 100.0,
    risk_score DECIMAL(5,2) DEFAULT 0.0,
    
    -- 请求统计
    total_requests INTEGER DEFAULT 0,
    successful_requests INTEGER DEFAULT 0,
    failed_requests INTEGER DEFAULT 0,
    
    -- 频率限制统计
    requests_in_hour INTEGER DEFAULT 0,
    requests_in_day INTEGER DEFAULT 0,
    hour_window_start TIMESTAMP,
    day_window_start TIMESTAMP,
    
    -- 错误统计
    rate_limit_count INTEGER DEFAULT 0,
    auth_failure_count INTEGER DEFAULT 0,
    network_error_count INTEGER DEFAULT 0,
    consecutive_failures INTEGER DEFAULT 0,
    
    -- 时间记录
    last_success TIMESTAMP,
    last_failure TIMESTAMP,
    last_rate_limit TIMESTAMP,
    last_request_time TIMESTAMP,
    cooldown_until TIMESTAMP,
    
    -- 元数据
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (account_id) REFERENCES accounts(account_id) ON DELETE CASCADE
);

-- 性能优化索引
CREATE INDEX IF NOT EXISTS idx_accounts_status ON accounts(status);
CREATE INDEX IF NOT EXISTS idx_accounts_created_at ON accounts(created_at);
CREATE INDEX IF NOT EXISTS idx_accounts_proxy_status ON accounts(proxy_status);
CREATE INDEX IF NOT EXISTS idx_accounts_last_check ON accounts(last_check);
CREATE INDEX IF NOT EXISTS idx_proxies_ip_port ON proxies(ip, port);
CREATE INDEX IF NOT EXISTS idx_proxies_assigned_to ON proxies(assigned_to);
CREATE INDEX IF NOT EXISTS idx_proxies_status ON proxies(status);
CREATE INDEX IF NOT EXISTS idx_proxies_country_code ON proxies(country_code);
CREATE INDEX IF NOT EXISTS idx_proxy_status_account_id ON proxy_status(account_id);
CREATE INDEX IF NOT EXISTS idx_proxy_status_status ON proxy_status(status);
CREATE INDEX IF NOT EXISTS idx_proxy_status_last_check ON proxy_status(last_check);

-- 账户健康度表索引
CREATE INDEX IF NOT EXISTS idx_account_health_status ON account_health(status);
CREATE INDEX IF NOT EXISTS idx_account_health_score ON account_health(health_score);
CREATE INDEX IF NOT EXISTS idx_account_health_cooldown ON account_health(cooldown_until);
CREATE INDEX IF NOT EXISTS idx_account_health_updated_at ON account_health(updated_at);
