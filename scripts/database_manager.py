#!/usr/bin/env python3
"""
Twx 统一数据库管理工具
"""

import sys
import json
import sqlite3
import time
from pathlib import Path
from typing import Dict, List, Any
import argparse

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config import Config
from src.utils.logger import setup_logger

logger = setup_logger(module_name="database_manager")

# 尝试导入 PostgreSQL 驱动
try:
    import psycopg2
    import psycopg2.extras
    POSTGRESQL_AVAILABLE = True
except ImportError:
    POSTGRESQL_AVAILABLE = False

CURRENT_SCHEMA_VERSION = 1

# 统一的表结构定义
DATABASE_SCHEMA = {
    "accounts": {
        "columns": {
            "account_id": {"type": "VARCHAR(255)", "constraints": "PRIMARY KEY"},
            "auth_info_1": {"type": "TEXT", "constraints": ""},
            "auth_info_2": {"type": "TEXT", "constraints": ""},
            "password": {"type": "TEXT", "constraints": ""},
            "totp_secret": {"type": "TEXT", "constraints": ""},
            "cookies": {"type": "TEXT", "constraints": ""},
            "proxy": {"type": "TEXT", "constraints": ""},
            "status": {"type": "VARCHAR(50)", "constraints": "DEFAULT 'active'"},
            "proxy_status": {"type": "VARCHAR(50)", "constraints": ""},
            "last_check": {"type": "TIMESTAMP", "constraints": ""},
            "proxy_latency": {"type": "INTEGER", "constraints": ""},
            "created_at": {"type": "TIMESTAMP", "constraints": "DEFAULT CURRENT_TIMESTAMP"},
            "updated_at": {"type": "TIMESTAMP", "constraints": "DEFAULT CURRENT_TIMESTAMP"}
        },
        "indexes": [
            "CREATE INDEX IF NOT EXISTS idx_accounts_status ON accounts(status)",
            "CREATE INDEX IF NOT EXISTS idx_accounts_created_at ON accounts(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_accounts_proxy_status ON accounts(proxy_status)",
            "CREATE INDEX IF NOT EXISTS idx_accounts_last_check ON accounts(last_check)"
        ]
    },
    "proxies": {
        "columns": {
            "id": {"type": "SERIAL", "constraints": "", "sqlite_type": "INTEGER PRIMARY KEY AUTOINCREMENT"},
            "ip": {"type": "VARCHAR(45)", "constraints": "NOT NULL"},
            "port": {"type": "INTEGER", "constraints": "NOT NULL"},
            "username": {"type": "TEXT", "constraints": ""},
            "password": {"type": "TEXT", "constraints": ""},
            "proxy_url": {"type": "TEXT", "constraints": ""},
            "country_code": {"type": "VARCHAR(10)", "constraints": ""},
            "city_name": {"type": "VARCHAR(100)", "constraints": ""},
            "assigned_to": {"type": "VARCHAR(255)", "constraints": "REFERENCES accounts(account_id)"},
            "assigned_at": {"type": "TIMESTAMP", "constraints": ""},
            "use_count": {"type": "INTEGER", "constraints": "DEFAULT 0"},
            "last_used": {"type": "TIMESTAMP", "constraints": ""},
            "status": {"type": "VARCHAR(50)", "constraints": "DEFAULT 'active'"},
            "created_at": {"type": "TIMESTAMP", "constraints": "DEFAULT CURRENT_TIMESTAMP"}
        },
        "indexes": [
            "CREATE INDEX IF NOT EXISTS idx_proxies_ip_port ON proxies(ip, port)",
            "CREATE INDEX IF NOT EXISTS idx_proxies_assigned_to ON proxies(assigned_to)",
            "CREATE INDEX IF NOT EXISTS idx_proxies_status ON proxies(status)",
            "CREATE INDEX IF NOT EXISTS idx_proxies_country_code ON proxies(country_code)"
        ]
    },
    "proxy_status": {
        "columns": {
            "id": {"type": "SERIAL", "constraints": "", "sqlite_type": "INTEGER PRIMARY KEY AUTOINCREMENT"},
            "account_id": {"type": "VARCHAR(255)", "constraints": "REFERENCES accounts(account_id)"},
            "proxy_url": {"type": "TEXT", "constraints": ""},
            "status": {"type": "VARCHAR(50)", "constraints": ""},
            "response_time": {"type": "INTEGER", "constraints": ""},
            "error_message": {"type": "TEXT", "constraints": ""},
            "last_check": {"type": "TIMESTAMP", "constraints": "DEFAULT CURRENT_TIMESTAMP"}
        },
        "indexes": [
            "CREATE INDEX IF NOT EXISTS idx_proxy_status_account_id ON proxy_status(account_id)",
            "CREATE INDEX IF NOT EXISTS idx_proxy_status_status ON proxy_status(status)",
            "CREATE INDEX IF NOT EXISTS idx_proxy_status_last_check ON proxy_status(last_check)"
        ]
    },
    "schema_versions": {
        "columns": {
            "component": {"type": "VARCHAR(50)", "constraints": "PRIMARY KEY"},
            "version": {"type": "INTEGER", "constraints": "NOT NULL"},
            "applied_at": {"type": "TIMESTAMP", "constraints": "DEFAULT CURRENT_TIMESTAMP"}
        },
        "indexes": []
    },
    "account_health": {
        "columns": {
            "account_id": {"type": "VARCHAR(255)", "constraints": "PRIMARY KEY REFERENCES accounts(account_id) ON DELETE CASCADE"},
            "status": {"type": "VARCHAR(50)", "constraints": "DEFAULT 'healthy'"},
            "health_score": {"type": "DECIMAL(5,2)", "constraints": "DEFAULT 100.0", "sqlite_type": "REAL"},
            "risk_score": {"type": "DECIMAL(5,2)", "constraints": "DEFAULT 0.0", "sqlite_type": "REAL"},
            "total_requests": {"type": "INTEGER", "constraints": "DEFAULT 0"},
            "successful_requests": {"type": "INTEGER", "constraints": "DEFAULT 0"},
            "failed_requests": {"type": "INTEGER", "constraints": "DEFAULT 0"},
            "requests_in_hour": {"type": "INTEGER", "constraints": "DEFAULT 0"},
            "requests_in_day": {"type": "INTEGER", "constraints": "DEFAULT 0"},
            "hour_window_start": {"type": "TIMESTAMP", "constraints": ""},
            "day_window_start": {"type": "TIMESTAMP", "constraints": ""},
            "rate_limit_count": {"type": "INTEGER", "constraints": "DEFAULT 0"},
            "auth_failure_count": {"type": "INTEGER", "constraints": "DEFAULT 0"},
            "network_error_count": {"type": "INTEGER", "constraints": "DEFAULT 0"},
            "consecutive_failures": {"type": "INTEGER", "constraints": "DEFAULT 0"},
            "last_success": {"type": "TIMESTAMP", "constraints": ""},
            "last_failure": {"type": "TIMESTAMP", "constraints": ""},
            "last_rate_limit": {"type": "TIMESTAMP", "constraints": ""},
            "last_request_time": {"type": "TIMESTAMP", "constraints": ""},
            "cooldown_until": {"type": "TIMESTAMP", "constraints": ""},
            "created_at": {"type": "TIMESTAMP", "constraints": "DEFAULT CURRENT_TIMESTAMP"},
            "updated_at": {"type": "TIMESTAMP", "constraints": "DEFAULT CURRENT_TIMESTAMP"}
        },
        "indexes": [
            "CREATE INDEX IF NOT EXISTS idx_account_health_status ON account_health(status)",
            "CREATE INDEX IF NOT EXISTS idx_account_health_score ON account_health(health_score)",
            "CREATE INDEX IF NOT EXISTS idx_account_health_cooldown ON account_health(cooldown_until)",
            "CREATE INDEX IF NOT EXISTS idx_account_health_updated_at ON account_health(updated_at)"
        ]
    }
}


class DatabaseManager:
    """统一数据库管理器"""

    def __init__(self, config_path: str = "config.json"):
        self.config = Config(config_path)
        self.db_type = self.config.get_database_type()

    def setup_all_databases(self, verify: bool = True) -> bool:
        """设置所有配置的数据库"""
        logger.info(f"设置数据库类型: {self.db_type}")
        success = self._execute_db_operation("setup")
        if success and verify:
            success = self._execute_db_operation("verify")
        return success

    def migrate_and_upgrade(self) -> bool:
        """自动检测并执行数据库迁移和升级"""
        logger.info(f"检查数据库迁移需求: {self.db_type}")
        return self._execute_db_operation("migrate")

    def _execute_db_operation(self, operation: str) -> bool:
        """统一的数据库操作执行器"""
        operations = {
            "sqlite": {
                "setup": self._setup_sqlite,
                "migrate": self._migrate_sqlite,
                "verify": self._verify_sqlite
            },
            "postgresql": {
                "setup": self._setup_postgresql,
                "migrate": self._migrate_postgresql,
                "verify": self._verify_postgresql
            },
            "json": {
                "setup": self._setup_json,
                "migrate": self._migrate_json,
                "verify": self._verify_json
            }
        }

        if self.db_type not in operations:
            logger.error(f"不支持的数据库类型: {self.db_type}")
            return False

        return operations[self.db_type][operation]()
    
    def _with_sqlite_connection(self, operation_func):
        """SQLite 连接管理器"""
        try:
            sqlite_path = self.config.get_sqlite_path()
            Path(sqlite_path).parent.mkdir(parents=True, exist_ok=True)

            with sqlite3.connect(sqlite_path) as conn:
                conn.execute("PRAGMA foreign_keys = ON")
                return operation_func(conn)
        except Exception as e:
            logger.error(f"SQLite 操作失败: {e}")
            return False

    def _setup_sqlite(self) -> bool:
        """设置 SQLite 数据库"""
        def setup_operation(conn):
            for table_name, table_def in DATABASE_SCHEMA.items():
                self._create_sqlite_table(conn, table_name, table_def)
            self._set_schema_version(conn, "sqlite", CURRENT_SCHEMA_VERSION)
            logger.info(f"✓ SQLite 数据库设置完成: {self.config.get_sqlite_path()}")
            return True

        return self._with_sqlite_connection(setup_operation)
    
    def _setup_postgresql(self) -> bool:
        """设置 PostgreSQL 数据库"""
        if not POSTGRESQL_AVAILABLE:
            logger.error("psycopg2 不可用")
            return False

        try:
            with psycopg2.connect(self.config.get_postgresql_connection_string()) as conn:
                with conn.cursor() as cursor:
                    for table_name, table_def in DATABASE_SCHEMA.items():
                        self._create_postgresql_table(cursor, table_name, table_def)
                    self._create_postgresql_triggers(cursor)
                    self._set_schema_version_pg(cursor, "postgresql", CURRENT_SCHEMA_VERSION)
                conn.commit()
            logger.info("✓ PostgreSQL 数据库设置完成")
            return True
        except Exception as e:
            logger.error(f"PostgreSQL 设置失败: {e}")
            return False
    
    def _setup_json(self) -> bool:
        """设置 JSON 存储"""
        try:
            json_path = self.config.get_json_storage_path()
            json_dir = Path(json_path)
            json_dir.mkdir(parents=True, exist_ok=True)
            
            # 初始化 JSON 结构
            accounts_file = json_dir / "accounts.json"
            if not accounts_file.exists():
                initial_data = {
                    "accounts": [],
                    "proxy_status": [],
                    "schema_versions": {
                        "json": CURRENT_SCHEMA_VERSION,
                        "applied_at": int(time.time())
                    }
                }
                with open(accounts_file, 'w', encoding='utf-8') as f:
                    json.dump(initial_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✓ JSON 存储设置完成: {json_path}")
            return True
            
        except Exception as e:
            logger.error(f"JSON 设置失败: {e}")
            return False
    
    def _convert_type_for_sqlite(self, col_type: str, constraints: str) -> tuple:
        """转换数据类型为 SQLite 兼容格式"""
        type_mapping = {
            "SERIAL": ("INTEGER PRIMARY KEY AUTOINCREMENT", constraints.replace("PRIMARY KEY", "").strip()),
            "TIMESTAMP": ("DATETIME", constraints),
            "DECIMAL": ("REAL", constraints)
        }

        if col_type in type_mapping:
            return type_mapping[col_type]
        elif col_type.startswith("VARCHAR") or col_type.startswith("DECIMAL"):
            return ("TEXT" if col_type.startswith("VARCHAR") else "REAL", constraints)
        return (col_type, constraints)

    def _create_sqlite_table(self, conn: sqlite3.Connection, table_name: str, table_def: Dict) -> None:
        """创建 SQLite 表"""
        columns = []
        for col_name, col_def in table_def["columns"].items():
            col_type = col_def.get("sqlite_type", col_def["type"])
            col_type, constraints = self._convert_type_for_sqlite(col_type, col_def["constraints"])
            columns.append(f"{col_name} {col_type} {constraints}".strip())

        conn.execute(f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(columns)})")
        for index_sql in table_def.get("indexes", []):
            conn.execute(index_sql)
    
    def _create_postgresql_table(self, cursor, table_name: str, table_def: Dict) -> None:
        """创建 PostgreSQL 表"""
        columns = []
        for col_name, col_def in table_def["columns"].items():
            col_type = col_def["type"]
            # 简化类型转换逻辑
            if col_type == "TEXT" and (col_name == "account_id" or "status" in col_name):
                col_type = "VARCHAR(255)" if col_name == "account_id" else "VARCHAR(50)"
            columns.append(f"{col_name} {col_type} {col_def['constraints']}".strip())

        cursor.execute(f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(columns)})")
        for index_sql in table_def.get("indexes", []):
            cursor.execute(index_sql)
    
    def _create_postgresql_triggers(self, cursor) -> None:
        """创建 PostgreSQL 触发器"""
        trigger_function = """
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = CURRENT_TIMESTAMP;
            RETURN NEW;
        END;
        $$ language 'plpgsql';
        """
        cursor.execute(trigger_function)
        
        # 为 accounts 表创建触发器
        trigger_sql = """
        DROP TRIGGER IF EXISTS update_accounts_updated_at ON accounts;
        CREATE TRIGGER update_accounts_updated_at
            BEFORE UPDATE ON accounts
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
        """
        cursor.execute(trigger_sql)
    
    def _migrate_sqlite(self) -> bool:
        """SQLite 数据库迁移"""
        sqlite_path = self.config.get_sqlite_path()
        if not Path(sqlite_path).exists():
            logger.info("SQLite 数据库不存在，跳过迁移")
            return True

        def migrate_operation(conn):
            current_version = self._get_schema_version(conn, "sqlite")
            if current_version < CURRENT_SCHEMA_VERSION:
                logger.info(f"执行 SQLite 迁移: {current_version} -> {CURRENT_SCHEMA_VERSION}")
                self._add_missing_columns_sqlite(conn)
                self._set_schema_version(conn, "sqlite", CURRENT_SCHEMA_VERSION)
                logger.info("✓ SQLite 迁移完成")
            else:
                logger.info("SQLite 数据库已是最新版本")
            return True

        return self._with_sqlite_connection(migrate_operation)
    
    def _migrate_postgresql(self) -> bool:
        """PostgreSQL 数据库迁移"""
        if not POSTGRESQL_AVAILABLE:
            return False
        try:
            with psycopg2.connect(self.config.get_postgresql_connection_string()) as conn:
                with conn.cursor() as cursor:
                    current_version = self._get_schema_version_pg(cursor, "postgresql")
                    if current_version < CURRENT_SCHEMA_VERSION:
                        logger.info(f"执行 PostgreSQL 迁移: {current_version} -> {CURRENT_SCHEMA_VERSION}")
                        self._add_missing_columns_postgresql(cursor)
                        self._set_schema_version_pg(cursor, "postgresql", CURRENT_SCHEMA_VERSION)
                        logger.info("✓ PostgreSQL 迁移完成")
                    else:
                        logger.info("PostgreSQL 数据库已是最新版本")
                conn.commit()
            return True
        except Exception as e:
            logger.error(f"PostgreSQL 迁移失败: {e}")
            return False
    
    def _migrate_json(self) -> bool:
        """JSON 存储迁移"""
        try:
            accounts_file = Path(self.config.get_json_storage_path()) / "accounts.json"
            if not accounts_file.exists():
                logger.info("JSON 文件不存在，跳过迁移")
                return True

            with open(accounts_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            current_version = data.get("schema_versions", {}).get("json", 0)
            if current_version < CURRENT_SCHEMA_VERSION:
                logger.info(f"执行 JSON 迁移: {current_version} -> {CURRENT_SCHEMA_VERSION}")
                data.setdefault("proxy_status", [])
                data["schema_versions"] = {"json": CURRENT_SCHEMA_VERSION, "applied_at": int(time.time())}

                with open(accounts_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                logger.info("✓ JSON 迁移完成")
            else:
                logger.info("JSON 存储已是最新版本")
            return True
        except Exception as e:
            logger.error(f"JSON 迁移失败: {e}")
            return False
    
    def _add_missing_columns_sqlite(self, conn: sqlite3.Connection) -> None:
        """添加 SQLite 缺失的列"""
        for table_name, table_def in DATABASE_SCHEMA.items():
            if not conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,)).fetchone():
                self._create_sqlite_table(conn, table_name, table_def)
                continue

            # 特殊处理 proxies 表
            if table_name == "proxies":
                self._recreate_proxies_table_if_needed(conn, table_def)
                continue

            # 添加缺失的列
            existing_columns = {row[1] for row in conn.execute(f"PRAGMA table_info({table_name})")}
            for col_name, col_def in table_def["columns"].items():
                if col_name not in existing_columns and col_def["type"] != "SERIAL":
                    col_type, constraints = self._convert_type_for_sqlite(
                        col_def.get("sqlite_type", col_def["type"]), col_def['constraints']
                    )

                    # 跳过 NOT NULL 列（SQLite 限制）
                    if "NOT NULL" in constraints and "DEFAULT" not in constraints:
                        logger.warning(f"跳过添加NOT NULL列: {table_name}.{col_name}")
                        continue

                    try:
                        conn.execute(f"ALTER TABLE {table_name} ADD COLUMN {col_name} {col_type} {constraints}")
                        logger.info(f"添加列: {table_name}.{col_name}")
                    except sqlite3.Error as e:
                        logger.warning(f"添加列失败: {table_name}.{col_name}: {e}")

    def _recreate_proxies_table_if_needed(self, conn: sqlite3.Connection, table_def: Dict) -> None:
        """如果 proxies 表结构差异太大，重新创建表"""
        try:
            existing_columns = {row[1] for row in conn.execute("PRAGMA table_info(proxies)")}
            if 'id' not in existing_columns or 'port' not in existing_columns:
                logger.info("proxies表结构需要重新创建")
                # 简单重建，不保留旧数据（避免复杂的数据迁移逻辑）
                conn.execute("DROP TABLE proxies")
                self._create_sqlite_table(conn, "proxies", table_def)
                logger.info("proxies表重新创建完成")
        except Exception as e:
            logger.error(f"重新创建proxies表失败: {e}")


    
    def _add_missing_columns_postgresql(self, cursor) -> None:
        """添加 PostgreSQL 缺失的列"""
        for table_name, table_def in DATABASE_SCHEMA.items():
            if table_name == "schema_versions":
                continue

            # 检查表是否存在
            cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = %s", (table_name,))
            if not cursor.fetchone():
                self._create_postgresql_table(cursor, table_name, table_def)
                continue

            # 获取现有列并添加缺失的列
            cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name = %s", (table_name,))
            existing_columns = {row[0] for row in cursor.fetchall()}

            for col_name, col_def in table_def["columns"].items():
                if col_name not in existing_columns and col_def["type"] != "SERIAL":
                    col_type = col_def["type"]
                    if col_type == "TEXT" and ("status" in col_name or col_name == "account_id"):
                        col_type = "VARCHAR(50)" if "status" in col_name else "VARCHAR(255)"

                    try:
                        cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {col_name} {col_type} {col_def['constraints']}")
                        logger.info(f"添加列: {table_name}.{col_name}")
                    except Exception as e:
                        logger.warning(f"添加列失败: {table_name}.{col_name}: {e}")
    
    def _get_schema_version(self, conn_or_cursor, component: str, is_pg: bool = False) -> int:
        """获取模式版本"""
        try:
            if is_pg:
                conn_or_cursor.execute("SELECT version FROM schema_versions WHERE component = %s", (component,))
            else:
                conn_or_cursor.execute("SELECT version FROM schema_versions WHERE component = ?", (component,))
            result = conn_or_cursor.fetchone()
            return result[0] if result else 0
        except Exception:
            return 0

    def _get_schema_version_pg(self, cursor, component: str) -> int:
        return self._get_schema_version(cursor, component, is_pg=True)

    def _set_schema_version(self, conn: sqlite3.Connection, component: str, version: int) -> None:
        """设置 SQLite 模式版本"""
        conn.execute("INSERT OR REPLACE INTO schema_versions (component, version, applied_at) VALUES (?, ?, CURRENT_TIMESTAMP)", (component, version))

    def _set_schema_version_pg(self, cursor, component: str, version: int) -> None:
        """设置 PostgreSQL 模式版本"""
        cursor.execute("""INSERT INTO schema_versions (component, version, applied_at) VALUES (%s, %s, CURRENT_TIMESTAMP)
                         ON CONFLICT (component) DO UPDATE SET version = EXCLUDED.version, applied_at = CURRENT_TIMESTAMP""", (component, version))
    
    def _verify_sqlite(self) -> bool:
        """验证 SQLite 数据库"""
        sqlite_path = self.config.get_sqlite_path()
        if not Path(sqlite_path).exists():
            logger.error("SQLite 数据库文件不存在")
            return False

        def verify_operation(conn):
            for table_name in DATABASE_SCHEMA.keys():
                if not conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,)).fetchone():
                    logger.error(f"表不存在: {table_name}")
                    return False
            logger.info("✓ SQLite 数据库验证通过")
            return True

        return self._with_sqlite_connection(verify_operation)

    def _verify_postgresql(self) -> bool:
        """验证 PostgreSQL 数据库"""
        if not POSTGRESQL_AVAILABLE:
            return False
        try:
            with psycopg2.connect(self.config.get_postgresql_connection_string()) as conn:
                with conn.cursor() as cursor:
                    for table_name in DATABASE_SCHEMA.keys():
                        cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = %s", (table_name,))
                        if not cursor.fetchone():
                            logger.error(f"表不存在: {table_name}")
                            return False
            logger.info("✓ PostgreSQL 数据库验证通过")
            return True
        except Exception as e:
            logger.error(f"PostgreSQL 验证失败: {e}")
            return False

    def _verify_json(self) -> bool:
        """验证 JSON 存储"""
        try:
            accounts_file = Path(self.config.get_json_storage_path()) / "accounts.json"
            if not accounts_file.exists():
                logger.error("JSON 文件不存在")
                return False

            with open(accounts_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if not isinstance(data, dict) or "accounts" not in data:
                    logger.error("JSON 文件格式错误")
                    return False
            logger.info("✓ JSON 存储验证通过")
            return True
        except Exception as e:
            logger.error(f"JSON 验证失败: {e}")
            return False

    def migrate_sqlite_to_postgresql(self, backup: bool = True) -> bool:
        """从 SQLite 迁移到 PostgreSQL"""
        if not POSTGRESQL_AVAILABLE:
            logger.error("PostgreSQL 不可用")
            return False
        
        try:
            sqlite_path = self.config.get_sqlite_path()
            if not Path(sqlite_path).exists():
                logger.error("SQLite 数据库不存在")
                return False
            
            # 读取 SQLite 数据
            accounts = []
            with sqlite3.connect(sqlite_path) as conn:
                conn.execute("PRAGMA foreign_keys = ON")
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM accounts")
                accounts = [dict(row) for row in cursor.fetchall()]
            
            if not accounts:
                logger.info("没有数据需要迁移")
                return True
            
            logger.info(f"准备迁移 {len(accounts)} 个账户")
            
            # 备份现有 PostgreSQL 数据
            if backup:
                self._backup_postgresql_data()
            
            # 写入 PostgreSQL
            connection_string = self.config.get_postgresql_connection_string()
            with psycopg2.connect(connection_string) as conn:
                with conn.cursor() as cursor:
                    for account in accounts:
                        cursor.execute("""
                            INSERT INTO accounts (
                                account_id, auth_info_1, auth_info_2, password,
                                totp_secret, cookies, proxy, status,
                                proxy_status, last_check, proxy_latency,
                                created_at, updated_at
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            ON CONFLICT (account_id) DO UPDATE SET
                                auth_info_1 = EXCLUDED.auth_info_1,
                                auth_info_2 = EXCLUDED.auth_info_2,
                                password = EXCLUDED.password,
                                totp_secret = EXCLUDED.totp_secret,
                                cookies = EXCLUDED.cookies,
                                proxy = EXCLUDED.proxy,
                                status = EXCLUDED.status,
                                proxy_status = EXCLUDED.proxy_status,
                                last_check = EXCLUDED.last_check,
                                proxy_latency = EXCLUDED.proxy_latency,
                                updated_at = CURRENT_TIMESTAMP
                        """, tuple(account.get(key) for key in [
                            'account_id', 'auth_info_1', 'auth_info_2', 'password',
                            'totp_secret', 'cookies', 'proxy', 'status',
                            'proxy_status', 'last_check', 'proxy_latency',
                            'created_at', 'updated_at'
                        ]))
                    
                conn.commit()
            
            logger.info("✓ 数据迁移完成")
            return True
            
        except Exception as e:
            logger.error(f"迁移失败: {e}")
            return False
    
    def _backup_postgresql_data(self) -> None:
        """备份 PostgreSQL 数据"""
        try:
            backup_file = Path(f"backup_postgresql_{int(time.time())}.json")
            connection_string = self.config.get_postgresql_connection_string()
            
            with psycopg2.connect(connection_string) as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                    cursor.execute("SELECT * FROM accounts")
                    existing_data = [dict(row) for row in cursor.fetchall()]
            
            if existing_data:
                with open(backup_file, 'w', encoding='utf-8') as f:
                    json.dump(existing_data, f, indent=2, default=str, ensure_ascii=False)
                logger.info(f"PostgreSQL 数据已备份: {backup_file}")
                
        except Exception as e:
            logger.warning(f"备份失败: {e}")


def main():
    parser = argparse.ArgumentParser(description="Twx 统一数据库管理工具")
    parser.add_argument("command", choices=[
        "setup", "migrate", "verify", "migrate-to-pg", "status"
    ], help="要执行的操作")
    parser.add_argument("--config", help="配置文件路径", default="config.json")
    parser.add_argument("--no-verify", action="store_true", help="跳过验证")
    parser.add_argument("--no-backup", action="store_true", help="跳过备份")
    
    args = parser.parse_args()
    
    try:
        manager = DatabaseManager(args.config)
        
        if args.command == "setup":
            success = manager.setup_all_databases(verify=not args.no_verify)
            logger.info("✅ 数据库设置完成" if success else "❌ 数据库设置失败")
            
        elif args.command == "migrate":
            success = manager.migrate_and_upgrade()
            logger.info("✅ 数据库迁移完成" if success else "❌ 数据库迁移失败")
            
        elif args.command == "verify":
            success = manager._execute_db_operation("verify")
            logger.info("✅ 数据库验证通过" if success else "❌ 数据库验证失败")

        elif args.command == "migrate-to-pg":
            success = manager.migrate_sqlite_to_postgresql(backup=not args.no_backup)
            logger.info("✅ 迁移到 PostgreSQL 完成" if success else "❌ 迁移失败")

        elif args.command == "status":
            logger.info(f"当前数据库类型: {manager.db_type}")
            success = manager._execute_db_operation("verify")
        
        return 0 if success else 1
        
    except Exception as e:
        logger.error(f"操作失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())