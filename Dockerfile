FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制项目文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

# 创建必要的目录
RUN mkdir -p data/sqlite data/cookies data/accounts logs

# 设置环境变量
ENV PYTHONUNBUFFERED=1

# 暴露API端口
EXPOSE 8000

# 启动命令
CMD ["python", "main.py"]