"""
API测试脚本 - 智能账号管理系统
测试所有智能账号管理相关的API端点
"""

import asyncio
import json
import httpx
from datetime import datetime

BASE_URL = "http://localhost:8000"

async def test_health_report():
    """测试健康度报告API"""
    print("🏥 测试健康度报告API...")
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BASE_URL}/smart-accounts/health-report")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ 健康度报告获取成功")
                
                if data['success']:
                    report = data['data']
                    print(f"   总账号数: {report['total_accounts']}")
                    print(f"   健康账号: {report['healthy_accounts']}")
                    print(f"   警告账号: {report['warning_accounts']}")
                    print(f"   平均健康度: {report['average_health_score']:.1f}分")
                else:
                    print("❌ API返回失败状态")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

async def test_best_account():
    """测试最佳账号选择API"""
    print("\n🎯 测试最佳账号选择API...")
    
    priorities = ["low", "normal", "high", "critical"]
    
    for priority in priorities:
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{BASE_URL}/smart-accounts/best-account",
                    params={"priority": priority}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data['success'] and data['account_id']:
                        print(f"✅ {priority}优先级: 选择账号 {data['account_id']}")
                    else:
                        print(f"⚠️ {priority}优先级: 无可用账号")
                else:
                    print(f"❌ {priority}优先级请求失败: {response.status_code}")
                    
        except Exception as e:
            print(f"❌ {priority}优先级测试失败: {str(e)}")

async def test_smart_request():
    """测试智能请求API"""
    print("\n🧠 测试智能请求API...")
    
    test_data = {
        "username": "twitter",
        "request_config": {
            "priority": "normal",
            "max_retries": 3,
            "timeout": 30.0
        }
    }
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                f"{BASE_URL}/smart-accounts/test-request",
                json=test_data
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    user_data = data['data']
                    print("✅ 智能请求成功")
                    print(f"   用户名: {user_data['username']}")
                    print(f"   显示名: {user_data['display_name']}")
                    print(f"   粉丝数: {user_data['followers_count']:,}")
                    print(f"   认证状态: {'是' if user_data['verified'] else '否'}")
                else:
                    print(f"❌ 智能请求失败: {data.get('error', '未知错误')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
    except Exception as e:
        print(f"❌ 智能请求测试失败: {str(e)}")

async def test_comprehensive_report():
    """测试综合报告API"""
    print("\n📊 测试综合报告API...")
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BASE_URL}/smart-accounts/comprehensive-report")
            
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    report = data['data']
                    print("✅ 综合报告获取成功")
                    
                    # 显示关键信息
                    account_health = report['account_health']
                    print(f"   账号状态分布:")
                    print(f"     健康: {account_health['status_distribution']['healthy']}")
                    print(f"     警告: {account_health['status_distribution']['warning']}")
                    print(f"     危险: {account_health['status_distribution']['critical']}")
                    
                    if 'recommendations' in report:
                        print(f"   系统建议:")
                        for i, rec in enumerate(report['recommendations'][:3], 1):
                            print(f"     {i}. {rec}")
                else:
                    print("❌ 综合报告获取失败")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
    except Exception as e:
        print(f"❌ 综合报告测试失败: {str(e)}")

async def test_risk_report():
    """测试风险报告API"""
    print("\n🛡️ 测试风险报告API...")
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BASE_URL}/smart-accounts/risk-report")
            
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    report = data['data']
                    print("✅ 风险报告获取成功")
                    print(f"   监控账号数: {report['total_accounts']}")
                    
                    # 显示风险分布
                    risk_dist = report['risk_distribution']
                    if risk_dist:
                        print(f"   风险分布:")
                        for level, count in risk_dist.items():
                            print(f"     {level}: {count} 个账号")
                    
                    # 显示保护统计
                    global_stats = report['global_stats']
                    print(f"   保护统计:")
                    print(f"     隔离账号: {global_stats['accounts_quarantined']}")
                    print(f"     保护动作: {global_stats['protection_actions_taken']}")
                else:
                    print("❌ 风险报告获取失败")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
    except Exception as e:
        print(f"❌ 风险报告测试失败: {str(e)}")

async def test_stats():
    """测试统计信息API"""
    print("\n📈 测试统计信息API...")
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BASE_URL}/smart-accounts/stats")
            
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    stats = data['data']
                    print("✅ 统计信息获取成功")
                    
                    # 账号摘要
                    summary = stats['account_summary']
                    print(f"   账号摘要:")
                    print(f"     总数: {summary['total']}")
                    print(f"     健康: {summary['healthy']}")
                    print(f"     警告: {summary['warning']}")
                    print(f"     危险: {summary['critical']}")
                    
                    # 性能指标
                    performance = stats['performance']
                    print(f"   性能指标:")
                    print(f"     平均健康度: {performance['average_health_score']:.1f}分")
                    print(f"     平均风险度: {performance['average_risk_score']:.1f}分")
                    
                    # 请求统计
                    request_stats = stats['request_stats']
                    if request_stats['total_requests'] > 0:
                        success_rate = (request_stats['successful_requests'] / 
                                      request_stats['total_requests']) * 100
                        print(f"   请求统计:")
                        print(f"     总请求: {request_stats['total_requests']}")
                        print(f"     成功率: {success_rate:.1f}%")
                else:
                    print("❌ 统计信息获取失败")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
    except Exception as e:
        print(f"❌ 统计信息测试失败: {str(e)}")

async def test_user_api_with_smart_management():
    """测试用户API的智能管理功能"""
    print("\n👤 测试用户API智能管理...")
    
    test_users = ["twitter", "elonmusk"]
    priorities = ["normal", "high"]
    
    for username in test_users:
        for priority in priorities:
            try:
                async with httpx.AsyncClient(timeout=60.0) as client:
                    response = await client.get(
                        f"{BASE_URL}/users/{username}",
                        params={"priority": priority}
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        if data['success']:
                            user_data = data['data']
                            print(f"✅ {username} ({priority}): {user_data['display_name']}")
                        else:
                            print(f"❌ {username} ({priority}): 请求失败")
                    else:
                        print(f"❌ {username} ({priority}): HTTP {response.status_code}")
                        
                # 添加延迟避免过于频繁
                await asyncio.sleep(1)
                
            except Exception as e:
                print(f"❌ {username} ({priority}) 测试失败: {str(e)}")

async def main():
    """主测试函数"""
    print("🚀 智能账号管理系统 API 测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"服务器地址: {BASE_URL}")
    print("=" * 50)
    
    # 检查服务器是否运行
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BASE_URL}/docs", timeout=5.0)
            if response.status_code == 200:
                print("✅ 服务器运行正常")
            else:
                print("⚠️ 服务器可能存在问题")
    except Exception as e:
        print(f"❌ 无法连接到服务器: {str(e)}")
        print("💡 请确保服务器正在运行: python main.py")
        return
    
    # 运行所有测试
    test_functions = [
        test_health_report,
        test_best_account,
        test_smart_request,
        test_comprehensive_report,
        test_risk_report,
        test_stats,
        test_user_api_with_smart_management
    ]
    
    for test_func in test_functions:
        try:
            await test_func()
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 失败: {str(e)}")
        
        await asyncio.sleep(0.5)  # 短暂延迟
    
    print("\n" + "=" * 50)
    print("🏁 API测试完成")
    print("\n💡 提示:")
    print("- 如果某些测试失败，可能是因为没有配置账号或账号状态不佳")
    print("- 可以通过 /smart-accounts/health-report 查看详细的账号状态")
    print("- 使用 /smart-accounts/comprehensive-report 获取完整的系统报告")

if __name__ == "__main__":
    asyncio.run(main())
