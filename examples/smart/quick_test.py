"""
快速测试脚本 - 智能账号管理系统
简单快速地测试核心功能
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def quick_test():
    """快速测试核心功能"""
    print("🔍 快速测试智能账号管理系统...")
    
    try:
        # 1. 测试基础功能
        print("\n1️⃣ 测试基础用户查询...")
        from src.api.twitter_api_facade import smart_execute
        
        async def get_user(client, username):
            user = await client.get_user_by_username(username)
            return {
                "username": user.username,
                "followers": user.followers_count,
                "verified": getattr(user, 'verified', False)
            }
        
        # 测试获取Twitter官方账号信息
        result = await smart_execute(
            get_user,
            priority="normal",
            username="twitter"
        )
        
        print(f"✅ 成功获取用户信息:")
        print(f"   用户名: {result['username']}")
        print(f"   粉丝数: {result['followers']:,}")
        print(f"   认证状态: {'是' if result['verified'] else '否'}")
        
    except Exception as e:
        print(f"❌ 基础测试失败: {str(e)}")
        return False
    
    try:
        # 2. 测试健康度报告
        print("\n2️⃣ 测试健康度报告...")
        from src.api.twitter_api_facade import twitter_api_facade
        
        report = await twitter_api_facade.get_comprehensive_report()
        
        print(f"✅ 系统状态:")
        print(f"   总账号数: {report['account_health']['total_accounts']}")
        print(f"   平均健康度: {report['account_health']['performance']['average_health_score']:.1f}分")
        
        if report['request_stats']['total_requests'] > 0:
            success_rate = (report['request_stats']['successful_requests'] / 
                          report['request_stats']['total_requests']) * 100
            print(f"   成功率: {success_rate:.1f}%")
        
    except Exception as e:
        print(f"❌ 健康度报告测试失败: {str(e)}")
    
    try:
        # 3. 测试账号选择
        print("\n3️⃣ 测试智能账号选择...")
        from src.api.account_health_manager import account_health_manager, RequestContext, RequestPriority
        
        # 测试不同优先级
        for priority_name, priority in [("普通", RequestPriority.NORMAL), ("高", RequestPriority.HIGH)]:
            context = RequestContext(priority=priority)
            account_id = await account_health_manager.get_best_account(context)
            
            if account_id:
                print(f"✅ {priority_name}优先级选择账号: {account_id}")
            else:
                print(f"⚠️ {priority_name}优先级无可用账号")
                
    except Exception as e:
        print(f"❌ 账号选择测试失败: {str(e)}")
    
    print("\n🎉 快速测试完成!")
    return True

async def test_api_endpoints():
    """测试API端点"""
    print("\n🌐 测试API端点...")
    
    try:
        import httpx
        
        base_url = "http://localhost:8000"
        
        # 测试健康度报告端点
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{base_url}/smart-accounts/health-report")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 健康度报告API正常: {data['success']}")
            else:
                print(f"❌ 健康度报告API失败: {response.status_code}")
                
    except ImportError:
        print("⚠️ 需要安装httpx来测试API端点: pip install httpx")
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        print("💡 请确保服务器正在运行: python main.py")

def print_usage_examples():
    """打印使用示例"""
    print("\n📚 使用示例:")
    print("""
# 基础使用
from src.api.enhanced_account_manager import smart_execute

async def example():
    # 获取用户信息
    async def get_user(client, username):
        return await client.get_user_by_username(username)
    
    result = await smart_execute(
        get_user,
        priority="normal",    # 优先级: low, normal, high, critical
        max_retries=3,        # 最大重试次数
        timeout=30.0,         # 超时时间
        username="elonmusk"   # 传递给函数的参数
    )
    return result

# 高级使用
from src.api.twitter_api_facade import twitter_api_facade

async def advanced_example():
    result = await twitter_api_facade.execute_smart_request(
        func=get_user,
        priority=RequestPriority.HIGH,
        preferred_account_id="account_123",  # 首选账号
        exclude_account_ids=["account_456"], # 排除账号
        username="elonmusk"
    )
    return result
""")

async def main():
    """主函数"""
    print("🚀 智能账号管理系统 - 快速测试")
    print("=" * 40)
    
    # 检查是否在正确的目录
    if not os.path.exists("src/api/twitter_api_facade.py"):
        print("❌ 错误: 请在项目根目录运行此脚本")
        print("💡 正确的运行方式: python examples/quick_test.py")
        return
    
    # 运行快速测试
    success = await quick_test()
    
    if success:
        # 测试API端点
        await test_api_endpoints()
        
        # 显示使用示例
        print_usage_examples()
        
        print("\n✨ 系统运行正常! 可以开始使用智能账号管理功能了。")
    else:
        print("\n⚠️ 系统可能存在问题，请检查配置和账号设置。")

if __name__ == "__main__":
    asyncio.run(main())
