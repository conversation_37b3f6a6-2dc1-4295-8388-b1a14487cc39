#!/usr/bin/env python3
"""
风险控制管理器使用示例
演示如何手动控制风险控制后台任务
"""

import asyncio
import json
from datetime import datetime

from src.api.risk_control import risk_control_manager
from src.utils.logger import setup_logger

logger = setup_logger(module_name="risk_control_example")

async def main():
    """主函数"""
    print("=== 风险控制管理器后台任务控制示例 ===\n")
    
    # 1. 检查初始状态
    print("1. 检查风险控制后台任务初始状态:")
    status = await risk_control_manager.get_background_tasks_status()
    print(f"   风险监控状态: {status['risk_monitor']['is_running']}")
    print(f"   清理任务状态: {status['cleanup_task']['is_running']}")
    print()
    
    # 2. 手动触发一次风险评估
    print("2. 手动触发风险评估:")
    success = await risk_control_manager.trigger_risk_assessment()
    print(f"   评估结果: {'成功' if success else '失败'}")
    print()
    
    # 3. 手动触发一次数据清理
    print("3. 手动触发数据清理:")
    success = await risk_control_manager.trigger_cleanup()
    print(f"   清理结果: {'成功' if success else '失败'}")
    print()
    
    # 4. 启动风险监控任务
    print("4. 启动风险监控任务:")
    success = await risk_control_manager.start_risk_monitor()
    print(f"   启动结果: {'成功' if success else '失败'}")
    
    # 检查启动后的状态
    status = await risk_control_manager.get_background_tasks_status()
    print(f"   风险监控状态: {status['risk_monitor']['is_running']}")
    print(f"   任务运行: {status['risk_monitor']['task_running']}")
    print()
    
    # 5. 启动清理任务
    print("5. 启动清理任务:")
    success = await risk_control_manager.start_cleanup_task()
    print(f"   启动结果: {'成功' if success else '失败'}")
    
    # 检查启动后的状态
    status = await risk_control_manager.get_background_tasks_status()
    print(f"   清理任务状态: {status['cleanup_task']['is_running']}")
    print(f"   任务运行: {status['cleanup_task']['task_running']}")
    print()
    
    # 6. 等待一段时间让任务运行
    print("6. 等待后台任务运行 (5秒)...")
    await asyncio.sleep(5)
    
    # 7. 获取全局风险报告
    print("7. 获取全局风险报告:")
    report = await risk_control_manager.get_global_risk_report()
    print(f"   监控账号数: {report['total_accounts']}")
    print(f"   总请求数: {report['global_stats']['total_requests']}")
    print(f"   总失败数: {report['global_stats']['total_failures']}")
    print(f"   隔离账号数: {report['global_stats']['accounts_quarantined']}")
    print(f"   保护动作数: {report['global_stats']['protection_actions_taken']}")
    
    # 显示风险分布
    risk_dist = report['risk_distribution']
    if risk_dist:
        print(f"   风险分布:")
        for level, count in risk_dist.items():
            print(f"     {level}: {count} 个账号")
    print()
    
    # 8. 再次检查后台任务状态
    print("8. 检查后台任务运行状态:")
    status = await risk_control_manager.get_background_tasks_status()
    print(f"   风险监控:")
    print(f"     运行状态: {status['risk_monitor']['is_running']}")
    print(f"     任务运行: {status['risk_monitor']['task_running']}")
    print(f"     任务取消: {status['risk_monitor']['task_cancelled']}")
    if status['risk_monitor']['task_exception']:
        print(f"     任务异常: {status['risk_monitor']['task_exception']}")
    
    print(f"   清理任务:")
    print(f"     运行状态: {status['cleanup_task']['is_running']}")
    print(f"     任务运行: {status['cleanup_task']['task_running']}")
    print(f"     任务取消: {status['cleanup_task']['task_cancelled']}")
    if status['cleanup_task']['task_exception']:
        print(f"     任务异常: {status['cleanup_task']['task_exception']}")
    print()
    
    # 9. 停止风险监控任务
    print("9. 停止风险监控任务:")
    success = await risk_control_manager.stop_risk_monitor()
    print(f"   停止结果: {'成功' if success else '失败'}")
    
    # 检查停止后的状态
    status = await risk_control_manager.get_background_tasks_status()
    print(f"   风险监控状态: {status['risk_monitor']['is_running']}")
    print()
    
    # 10. 停止清理任务
    print("10. 停止清理任务:")
    success = await risk_control_manager.stop_cleanup_task()
    print(f"    停止结果: {'成功' if success else '失败'}")
    
    # 检查停止后的状态
    status = await risk_control_manager.get_background_tasks_status()
    print(f"    清理任务状态: {status['cleanup_task']['is_running']}")
    print()
    
    # 11. 再次手动触发操作
    print("11. 再次手动触发风险评估:")
    success = await risk_control_manager.trigger_risk_assessment()
    print(f"    评估结果: {'成功' if success else '失败'}")
    
    print("12. 再次手动触发数据清理:")
    success = await risk_control_manager.trigger_cleanup()
    print(f"    清理结果: {'成功' if success else '失败'}")
    print()
    
    print("=== 示例完成 ===")

async def api_test_example():
    """API接口测试示例"""
    print("=== 风险控制API接口测试示例 ===\n")
    
    # 模拟API调用
    import requests
    
    base_url = "http://localhost:8000/smart-accounts"
    
    # 1. 启动风险控制任务
    print("1. 启动风险控制后台任务:")
    response = requests.post(f"{base_url}/risk-control/start")
    print(f"   状态码: {response.status_code}")
    print(f"   响应: {response.json()}")
    print()
    
    # 2. 检查状态
    print("2. 检查风险控制状态:")
    response = requests.get(f"{base_url}/risk-control/status")
    print(f"   状态码: {response.status_code}")
    print(f"   响应: {response.json()}")
    print()
    
    # 3. 获取风险报告
    print("3. 获取风险报告:")
    response = requests.get(f"{base_url}/risk-report")
    print(f"   状态码: {response.status_code}")
    data = response.json()
    if data['success']:
        report = data['data']
        print(f"   监控账号数: {report['total_accounts']}")
        print(f"   隔离账号数: {report['global_stats']['accounts_quarantined']}")
    print()
    
    # 4. 手动触发风险评估
    print("4. 手动触发风险评估:")
    response = requests.post(f"{base_url}/risk-control/trigger-assessment")
    print(f"   状态码: {response.status_code}")
    print(f"   响应: {response.json()}")
    print()
    
    # 5. 手动触发数据清理
    print("5. 手动触发数据清理:")
    response = requests.post(f"{base_url}/risk-control/trigger-cleanup")
    print(f"   状态码: {response.status_code}")
    print(f"   响应: {response.json()}")
    print()
    
    # 6. 停止风险控制任务
    print("6. 停止风险控制后台任务:")
    response = requests.post(f"{base_url}/risk-control/stop")
    print(f"   状态码: {response.status_code}")
    print(f"   响应: {response.json()}")
    print()
    
    print("=== API测试完成 ===")

if __name__ == "__main__":
    # 运行直接调用示例
    asyncio.run(main())
    
    # 运行API测试示例 (需要启动服务器)
    # asyncio.run(api_test_example()) 