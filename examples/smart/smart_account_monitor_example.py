#!/usr/bin/env python3
"""
智能账号管理器使用示例
演示如何手动控制健康度监控后台任务
"""

import asyncio
import json
from datetime import datetime

from src.api.account_health_manager import account_health_manager, RequestContext, RequestPriority
from src.utils.logger import setup_logger

logger = setup_logger(module_name="smart_account_monitor_example")

async def main():
    """主函数"""
    print("=== 智能账号管理器监控任务控制示例 ===\n")
    
    # 1. 检查初始状态
    print("1. 检查监控任务初始状态:")
    status = await account_health_manager.get_monitor_status()
    print(f"   监控状态: {status['is_monitoring']}")
    print(f"   任务运行: {status['task_running']}")
    print()

    # 2. 手动触发一次健康度更新
    print("2. 手动触发健康度更新:")
    success = await account_health_manager.trigger_health_update()
    print(f"   更新结果: {'成功' if success else '失败'}")
    print()

    # 3. 启动监控任务
    print("3. 启动健康度监控任务:")
    success = await account_health_manager.start_health_monitor()
    print(f"   启动结果: {'成功' if success else '失败'}")

    # 检查启动后的状态
    status = await account_health_manager.get_monitor_status()
    print(f"   监控状态: {status['is_monitoring']}")
    print(f"   任务运行: {status['task_running']}")
    print()
    
    # 4. 等待一段时间让监控任务运行
    print("4. 等待监控任务运行 (5秒)...")
    await asyncio.sleep(5)
    
    # 5. 获取健康度报告
    print("5. 获取账号健康度报告:")
    report = await account_health_manager.get_account_health_report()
    print(f"   总账号数: {report['total_accounts']}")
    print(f"   健康账号: {report['healthy_accounts']}")
    print(f"   警告账号: {report['warning_accounts']}")
    print(f"   危险账号: {report['critical_accounts']}")
    print(f"   暂停账号: {report['suspended_accounts']}")
    print(f"   封禁账号: {report['banned_accounts']}")
    print(f"   平均健康度: {report['average_health_score']}")
    print(f"   平均风险度: {report['average_risk_score']}")
    print()
    
    # 6. 测试智能请求功能
    print("6. 测试智能请求功能:")
    try:
        context = RequestContext(
            priority=RequestPriority.NORMAL,
            max_retries=3,
            timeout=30.0
        )
        
        # 获取最佳账号
        best_account = await account_health_manager.get_best_account(context)
        if best_account:
            print(f"   最佳账号: {best_account}")
        else:
            print("   没有可用的账号")
    except Exception as e:
        print(f"   获取最佳账号失败: {e}")
    print()
    
    # 7. 再次检查监控状态
    print("7. 检查监控任务运行状态:")
    status = await account_health_manager.get_monitor_status()
    print(f"   监控状态: {status['is_monitoring']}")
    print(f"   任务运行: {status['task_running']}")
    print(f"   任务取消: {status['task_cancelled']}")
    if status['task_exception']:
        print(f"   任务异常: {status['task_exception']}")
    print()
    
    # 8. 停止监控任务
    print("8. 停止健康度监控任务:")
    success = await account_health_manager.stop_health_monitor()
    print(f"   停止结果: {'成功' if success else '失败'}")
    
    # 检查停止后的状态
    status = await account_health_manager.get_monitor_status()
    print(f"   监控状态: {status['is_monitoring']}")
    print(f"   任务运行: {status['task_running']}")
    print()
    
    # 9. 再次手动触发更新
    print("9. 再次手动触发健康度更新:")
    success = await account_health_manager.trigger_health_update()
    print(f"   更新结果: {'成功' if success else '失败'}")
    print()
    
    print("=== 示例完成 ===")

async def api_test_example():
    """API接口测试示例"""
    print("=== API接口测试示例 ===\n")
    
    # 模拟API调用
    import requests
    
    base_url = "http://localhost:8000/smart-accounts"
    
    # 1. 启动监控
    print("1. 启动监控任务:")
    response = requests.post(f"{base_url}/monitor/start")
    print(f"   状态码: {response.status_code}")
    print(f"   响应: {response.json()}")
    print()
    
    # 2. 检查状态
    print("2. 检查监控状态:")
    response = requests.get(f"{base_url}/monitor/status")
    print(f"   状态码: {response.status_code}")
    print(f"   响应: {response.json()}")
    print()
    
    # 3. 获取健康报告
    print("3. 获取健康报告:")
    response = requests.get(f"{base_url}/health-report")
    print(f"   状态码: {response.status_code}")
    data = response.json()
    print(f"   总账号数: {data['data']['total_accounts']}")
    print(f"   健康账号: {data['data']['healthy_accounts']}")
    print()
    
    # 4. 手动触发更新
    print("4. 手动触发更新:")
    response = requests.post(f"{base_url}/monitor/trigger-update")
    print(f"   状态码: {response.status_code}")
    print(f"   响应: {response.json()}")
    print()
    
    # 5. 停止监控
    print("5. 停止监控任务:")
    response = requests.post(f"{base_url}/monitor/stop")
    print(f"   状态码: {response.status_code}")
    print(f"   响应: {response.json()}")
    print()
    
    print("=== API测试完成 ===")

if __name__ == "__main__":
    # 运行直接调用示例
    asyncio.run(main())
    
    # 运行API测试示例 (需要启动服务器)
    # asyncio.run(api_test_example()) 