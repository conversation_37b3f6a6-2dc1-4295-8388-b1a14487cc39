"""
智能账号管理系统测试示例
演示如何使用新的智能账号管理功能
"""

import asyncio
import json
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_basic_smart_request():
    """测试基础智能请求功能"""
    print("=== 测试基础智能请求 ===")
    
    try:
        from src.api.enhanced_account_manager import smart_execute
        
        # 定义一个简单的测试函数
        async def get_user_info(client, username):
            """获取用户信息"""
            user = await client.get_user_by_username(username)
            return {
                "id": user.id,
                "username": user.username,
                "display_name": user.name,
                "followers_count": user.followers_count,
                "following_count": user.following_count,
                "verified": getattr(user, 'verified', False)
            }
        
        # 使用不同优先级测试
        test_users = ["elonmusk", "twitter", "openai"]
        priorities = ["low", "normal", "high"]
        
        for i, username in enumerate(test_users):
            priority = priorities[i % len(priorities)]
            print(f"\n测试用户: {username} (优先级: {priority})")
            
            try:
                result = await smart_execute(
                    get_user_info,
                    priority=priority,
                    max_retries=3,
                    timeout=30.0,
                    username=username
                )
                
                print(f"✅ 成功获取用户信息:")
                print(f"   用户名: {result['username']}")
                print(f"   显示名: {result['display_name']}")
                print(f"   粉丝数: {result['followers_count']:,}")
                print(f"   关注数: {result['following_count']:,}")
                print(f"   认证状态: {'是' if result['verified'] else '否'}")
                
            except Exception as e:
                print(f"❌ 获取用户 {username} 失败: {str(e)}")
                
            # 添加延迟避免过于频繁的请求
            await asyncio.sleep(2)
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保智能账号管理模块已正确安装")

async def test_account_health_monitoring():
    """测试账号健康度监控"""
    print("\n=== 测试账号健康度监控 ===")
    
    try:
        from src.api.twitter_api_facade import twitter_api_facade
        
        # 获取综合报告
        report = await twitter_api_facade.get_comprehensive_report()
        
        print("📊 系统健康度报告:")
        print(f"   总账号数: {report['account_health']['total_accounts']}")
        print(f"   健康账号: {report['account_health']['status_distribution']['healthy']}")
        print(f"   警告账号: {report['account_health']['status_distribution']['warning']}")
        print(f"   危险账号: {report['account_health']['status_distribution']['critical']}")
        print(f"   暂停账号: {report['account_health']['status_distribution']['suspended']}")
        print(f"   封禁账号: {report['account_health']['status_distribution']['banned']}")
        print(f"   平均健康度: {report['account_health']['performance']['average_health_score']:.1f}分")
        print(f"   平均风险度: {report['account_health']['performance']['average_risk_score']:.1f}分")
        
        # 显示请求统计
        stats = report['request_stats']
        print(f"\n📈 请求统计:")
        print(f"   总请求数: {stats['total_requests']}")
        print(f"   成功请求: {stats['successful_requests']}")
        print(f"   失败请求: {stats['failed_requests']}")
        if stats['total_requests'] > 0:
            success_rate = (stats['successful_requests'] / stats['total_requests']) * 100
            print(f"   成功率: {success_rate:.1f}%")
        
        # 显示优化建议
        if 'recommendations' in report:
            print(f"\n💡 系统建议:")
            for i, recommendation in enumerate(report['recommendations'], 1):
                print(f"   {i}. {recommendation}")
                
    except Exception as e:
        print(f"❌ 获取健康度报告失败: {str(e)}")

async def test_risk_control():
    """测试风险控制功能"""
    print("\n=== 测试风险控制功能 ===")
    
    try:
        from src.api.risk_control import risk_control_manager
        
        # 获取全局风险报告
        risk_report = await risk_control_manager.get_global_risk_report()
        
        print("🛡️ 风险控制报告:")
        print(f"   监控账号数: {risk_report['total_accounts']}")
        
        # 显示风险分布
        risk_dist = risk_report['risk_distribution']
        print(f"   风险分布:")
        for risk_level, count in risk_dist.items():
            print(f"     {risk_level}: {count} 个账号")
        
        # 显示全局统计
        global_stats = risk_report['global_stats']
        print(f"   全局统计:")
        print(f"     总请求数: {global_stats['total_requests']}")
        print(f"     总失败数: {global_stats['total_failures']}")
        print(f"     隔离账号数: {global_stats['accounts_quarantined']}")
        print(f"     保护动作数: {global_stats['protection_actions_taken']}")
        
        # 显示阈值配置
        thresholds = risk_report['thresholds']
        print(f"   风险阈值:")
        print(f"     每分钟请求限制: {thresholds['requests_per_minute']}")
        print(f"     每小时请求限制: {thresholds['requests_per_hour']}")
        print(f"     每日请求限制: {thresholds['requests_per_day']}")
        print(f"     失败率警告阈值: {thresholds['failure_rate_warning']*100:.0f}%")
        print(f"     失败率危险阈值: {thresholds['failure_rate_critical']*100:.0f}%")
        
    except Exception as e:
        print(f"❌ 获取风险控制报告失败: {str(e)}")

async def test_best_account_selection():
    """测试最佳账号选择"""
    print("\n=== 测试最佳账号选择 ===")
    
    try:
        from src.api.account_health_manager import account_health_manager, RequestContext, RequestPriority
        
        # 测试不同优先级的账号选择
        priorities = [
            (RequestPriority.LOW, "低优先级"),
            (RequestPriority.NORMAL, "普通优先级"),
            (RequestPriority.HIGH, "高优先级"),
            (RequestPriority.CRITICAL, "关键优先级")
        ]
        
        for priority, desc in priorities:
            context = RequestContext(priority=priority)
            account_id = await account_health_manager.get_best_account(context)
            
            if account_id:
                print(f"✅ {desc}: 选择账号 {account_id}")
            else:
                print(f"❌ {desc}: 没有可用账号")
                
    except Exception as e:
        print(f"❌ 账号选择测试失败: {str(e)}")

async def test_optimization_suggestions():
    """测试优化建议功能"""
    print("\n=== 测试优化建议 ===")
    
    try:
        from src.api.twitter_api_facade import twitter_api_facade

        suggestions = await twitter_api_facade.optimize_account_usage()
        
        print("🔧 账号使用优化建议:")
        print(f"   健康账号数: {suggestions['healthy_accounts']}")
        print(f"   问题账号数: {suggestions['problematic_accounts']}")
        
        if suggestions['optimization_actions']:
            print("   优化动作:")
            for action in suggestions['optimization_actions']:
                print(f"     - {action['action']}: {action['reason']}")
                if 'accounts' in action:
                    print(f"       涉及账号: {', '.join(action['accounts'])}")
        
        print("   推荐优先级分配:")
        for priority, recommendation in suggestions['recommended_priority_distribution'].items():
            print(f"     {priority}: {recommendation}")
            
    except Exception as e:
        print(f"❌ 获取优化建议失败: {str(e)}")

async def test_error_handling():
    """测试错误处理机制"""
    print("\n=== 测试错误处理机制 ===")
    
    try:
        from src.api.enhanced_account_manager import smart_execute
        
        # 测试一个可能失败的请求
        async def risky_operation(client, invalid_username):
            """一个可能失败的操作"""
            # 尝试获取一个不存在的用户
            return await client.get_user_by_username(invalid_username)
        
        print("测试错误处理 - 尝试获取不存在的用户...")
        
        try:
            result = await smart_execute(
                risky_operation,
                priority="normal",
                max_retries=2,  # 减少重试次数以快速测试
                timeout=10.0,
                invalid_username="this_user_definitely_does_not_exist_12345"
            )
            print(f"意外成功: {result}")
            
        except Exception as e:
            print(f"✅ 正确捕获错误: {type(e).__name__}: {str(e)}")
            
    except Exception as e:
        print(f"❌ 错误处理测试失败: {str(e)}")

async def main():
    """主测试函数"""
    print("🚀 智能账号管理系统测试开始")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # 运行所有测试
    test_functions = [
        test_basic_smart_request,
        test_account_health_monitoring,
        test_risk_control,
        test_best_account_selection,
        test_optimization_suggestions,
        test_error_handling
    ]
    
    for test_func in test_functions:
        try:
            await test_func()
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 失败: {str(e)}")
        
        print()  # 添加空行分隔
    
    print("=" * 50)
    print("🏁 测试完成")

if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
