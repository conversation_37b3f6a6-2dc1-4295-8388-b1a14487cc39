# 智能账号管理系统示例脚本总览

本目录包含了智能账号管理系统的各类测试与演示脚本，分为基础用例、智能账号管理、API测试和高级/工具脚本。

---

## 目录结构与说明

### 1. basic/ —— 基础用例

- `get_user.py`：获取用户信息并保存状态
- `get_tweet.py`：获取推文及其回复
- `get_user_tweets.py`：获取指定用户的历史推文
- `tweet_utils.py`：推文对象与字典互转工具
- `get_hot_tweets.py`：获取热门推文（可指定周期/热度）
- `search_tweets.py`：按关键词/高级语法搜索推文

**适用场景**：

- 单账号基础功能演示
- 数据采集/分析脚本模板

**运行方式**（示例）：

```bash
python examples/basic/get_user.py elonmusk
python examples/basic/get_tweet.py <tweet_url_or_id>
```

---

### 2. smart/ —— 智能账号管理相关

- `quick_test.py`：快速验证系统与账号健康
- `smart_account_test.py`：全面测试智能账号管理功能
- `smart_account_monitor_example.py`：演示如何手动控制健康度监控后台任务
- `risk_control_example.py`：演示如何手动控制风险控制后台任务

**适用场景**：

- 智能账号池、健康度、风险控制等功能测试
- 日常健康检查与回归测试
- 后台任务控制与监控

**运行方式**（示例）：

```bash
python examples/smart/quick_test.py
python examples/smart/smart_account_test.py
python examples/smart/risk_control_example.py
```

---

### 3. api/ —— API端点测试

- `api_test.py`：自动化测试所有智能账号管理相关API接口

**适用场景**：

- 前后端联调、接口回归测试、API文档验证

**运行方式**：

```bash
# 启动后端服务
python main.py &
# 运行API测试
python examples/api/api_test.py
```

---

### 4. advanced/ —— 高级/监控/工具脚本

- `monitor.py`：监控指定用户的资料、推文、关系等变化

**适用场景**：

- 持续监控、自动化数据采集、定制化扩展

**运行方式**：

```bash
python examples/advanced/monitor.py <screen_name> [--interval 300]
```

---

## 使用前准备

1. **安装依赖**

```bash
pip install -r requirements.txt
pip install httpx  # 用于API测试
```

2. **导入账号**

```bash
python cli.py import-accounts
```

3. **（可选）设置环境变量**

```bash
export FERNET_KEY="your_fernet_key_here"
export MAX_REQUESTS_PER_HOUR=100
export MAX_REQUESTS_PER_DAY=1000
```

---

## 常见问题

- **No available accounts**：请先导入账号并确保账号健康
- **API测试连接错误**：请确认后端服务已启动且端口未被占用
- **账号健康度低/频繁切换**：请查看健康报告与风险报告，适当调整账号池
- **详细错误信息**：请查阅 logs/errors.log 或开启 debug 日志

---

## 参考文档

- `docs/smart_account_management_guide.md` —— 智能账号管理高级用法
- `docs/routes.md` —— API路由与参数说明

如需自定义测试或集成到CI/CD，请参考各脚本源码与注释。
