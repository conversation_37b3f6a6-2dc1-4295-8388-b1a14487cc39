import os
import random
import sys
import asyncio
import json
from pathlib import Path

# 添加项目根目录到 Python 路径
if __name__ == "__main__":
    project_root = Path(__file__).resolve().parent.parent.parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))


from src.api.helpers import collect_user_state
from src.utils.logger import setup_logger
from src.api.twitter_api_facade import smart_execute
# 配置日志
logger = setup_logger(module_name="get_user")

class SimpleConfig:
    """简单的配置类"""
    def __init__(self):
        self.monitor_tweets = True
        self.monitor_relations = False
        self.monitor_mentions = False
        self.monitor_likes = False
        self.monitor_retweets = False

async def get_user_info(screen_name: str):
    """获取用户详细信息
    
    Args:
        screen_name: 用户名（如 elonmusk）
    """
    try:
        # 使用 AccountManager 获取用户信息
        async def fetch_user(client, screen_name):
            user = await client.get_user_by_screen_name(screen_name)
            if not user:
                logger.error(f"找不到用户: {screen_name}")
                return None, None
                
            # 获取用户详细状态
            config = SimpleConfig()
            state = await collect_user_state(screen_name, config, client)
            
            return user, state
            
        logger.info(f"开始获取用户 {screen_name} 的信息...")
        result = await smart_execute(fetch_user, screen_name=screen_name)
        if not result or not result[0]:
            return
            
        user, state = result
        
        # 打印用户基本信息
        
        if user:
            logger.info("用户基本信息:")
            logger.info(f"- 用户名: {user.screen_name}")
            logger.info(f"- 显示名称: {user.name}")
            logger.info(f"- 简介: {user.description}")
            logger.info(f"- 关注者数: {user.followers_count}")
            logger.info(f"- 关注数: {user.following_count}")
            logger.info(f"- 创建时间: {user.created_at}")
            logger.info(f"- 位置: {user.location}")
            logger.info(f"- 网站: {user.url}")
            logger.info(f"- 头像: {user.profile_image_url}")
            logger.info(f"- 封面: {user.profile_banner_url}")
            logger.info(f"- 推文数: {user.statuses_count}")
            logger.info(f"- 媒体数: {user.media_count}")
            logger.info(f"- 列表数: {user.listed_count}")
            logger.info(f"- 认证: {user.verified}")
            logger.info(f"- 保护: {user.protected}")
        else:
            logger.warning("无法获取用户信息")
        # 保存状态到文件
        output_dir = Path("data/states")
        output_dir.mkdir(parents=True, exist_ok=True)
        output_file = output_dir / f"{screen_name}_state.json"
        
        class SetEncoder(json.JSONEncoder):
            """处理set类型的JSON编码器"""
            def default(self, obj):
                if isinstance(obj, set):
                    return list(obj)
                return json.JSONEncoder.default(self, obj)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(state, f, ensure_ascii=False, indent=2, cls=SetEncoder)
        logger.info(f"用户状态已保存到: {output_file}")
        
    except Exception as e:
        logger.error(f"获取用户信息时出错: {e}")

async def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法: python get_user.py <用户名>")
        print("示例: python get_user.py elonmusk")
        sys.exit(1)
        
    screen_name = sys.argv[1]
    
    # 确保存储目录存在
    Path("data/states").mkdir(parents=True, exist_ok=True)
    
    # 获取用户信息
    await get_user_info(screen_name)

if __name__ == "__main__":
    asyncio.run(main()) 