from pathlib import Path
import pathlib
import sys
from twikit import Tweet, Client


# 添加项目根目录到 Python 路径
if __name__ == "__main__":
    project_root = Path(__file__).resolve().parent.parent.parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))

from src.api.routes.tweet_routes import extract_tweet_id
from src.api.twitter_api_facade import smart_execute


MAX_REPLIES = 100
async def get_tweet_info(tweet_id: str):
  async def get_replies(replies: list[Tweet], replies_table):
    if replies:
      for reply in replies:
        reply_tweet = {
          "id": reply.id,
          "user": reply.user.screen_name,
          "description": reply.user.description,
          "followers_count": reply.user.followers_count,
          "following_count": reply.user.following_count,
          "statuses_count": reply.user.statuses_count,
          "profile_image_url": reply.user.profile_image_url,
          "profile_banner_url": reply.user.profile_banner_url,
          "text": reply.full_text,
          "full_text": reply.full_text,
          "favorite_count": reply.favorite_count,
          "retweet_count": reply.retweet_count,
          "reply_count": reply.reply_count,
          "quote_count": reply.quote_count,
          "language": reply.lang,
        }
        print("--------------------------------")
        print(reply_tweet)
        print("--------------------------------")
        if reply_tweet not in replies_table:
          replies_table.append(reply_tweet)
      print([reply.user.screen_name for reply in replies])
  replies_table = []
  async def fetch_tweet(client: Client, tweet_id: str) -> Tweet:
      tweet = await client.get_tweet_by_id(tweet_id)
      replies = tweet.replies

      if replies:
        
        await get_replies([r for r in replies], replies_table)
        cursor_file = pathlib.Path(__file__).parent  / f"{tweet_id}_cursor.txt"
        if cursor_file.exists():
            cursor = cursor_file.read_text()
            replies.next_cursor = cursor
            replies = await replies.next()

        while replies.next_cursor:
          if len(replies_table) > MAX_REPLIES:
            break
          await get_replies([r for r in replies], replies_table)
          replies = await replies.next()

      return tweet

  tweet_info = await smart_execute(
      fetch_tweet,
      tweet_id=tweet_id
  )
  return tweet_info, replies_table

async def main(urlOrId: str):
    tweet_id = extract_tweet_id(urlOrId)
    if tweet_id:
        tweet, replies_table = await get_tweet_info(tweet_id)
        if not tweet:
            print("Tweet not found")
        print(replies_table)
    else:
        raise ValueError("Invalid Twitter URL")


if __name__ == "__main__":
    import asyncio
    tweet_url = "https://x.com/nocontextfooty/status/1933881320712638829"
    MAX_REPLIES = 10
    asyncio.run(main(tweet_url)) 