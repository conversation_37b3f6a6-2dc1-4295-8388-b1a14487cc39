import os
import random
import sys
import asyncio
import json
from pathlib import Path
from typing import Literal, List, Dict, Any
from datetime import datetime

# 添加项目根目录到 Python 路径
if __name__ == "__main__":
    project_root = Path(__file__).resolve().parent.parent.parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))

from twikit import Client
from src.utils.logger import setup_logger
from src.api.twitter_api_facade import smart_execute
from src.utils.utils import tweet_to_dict

# 配置日志
logger = setup_logger(debug=True, module_name="search_tweets")

async def search_tweets(
    query: str,
    product: Literal["Top", "Latest", "Media"] = "Top",
    max_results: int = 10,
    save_to_file: bool = True
) -> List[Dict[str, Any]]:
    """搜索推文
    
    Args:
        query: 搜索关键词（可包含 since/until/min_faves 等 Twitter 高级语法）
        product: 搜索结果类型 (Top, Latest, Media)
        max_results: 最大结果数量
        save_to_file: 是否保存结果到文件
        
    Returns:
        符合条件的推文列表
    """
    try:
        logger.info(f"开始搜索推文: {query}")
        logger.info(f"搜索条件: 类型={product}")
        
        # 使用 AccountManager 搜索推文
        async def fetch_tweets(client: Client, query: str, product: Literal["Top", "Latest", "Media"]):
            tweets = []
            cursor = None
            total_fetched = 0
            
            while total_fetched < max_results:
                batch = await client.search_tweet(
                    query=query,
                    product=product,
                    count=min(20, max_results),  # API限制每次最多20条
                    cursor=cursor
                )
                
                if not batch:
                    break
                    
                for tweet in batch:
                    tweet_data = tweet_to_dict(tweet)
                    tweets.append(tweet_data)
                
                total_fetched += len(batch)
                
                # 获取下一页的cursor
                cursor = getattr(batch, "next_cursor", None)
                if not cursor:
                    break
                    
                # 避免频繁请求
                await asyncio.sleep(1)
            
            return tweets
            
        tweets = await smart_execute(
            fetch_tweets,
            query=query,
            product=product,
            max_retries=10
        )
        
        logger.info(f"找到 {len(tweets)} 条符合条件的推文")
        
        # 保存结果到文件
        if save_to_file and tweets:
            output_dir = Path("data/search_results")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{query.replace(' ', '_')}_{product.lower()}_{timestamp}.json"
            output_file = output_dir / filename
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(tweets, f, ensure_ascii=False, indent=2)
            logger.info(f"搜索结果已保存到: {output_file}")
        
        return tweets
        
    except Exception as e:
        logger.error(f"搜索推文时出错: {e}")
        return []

async def main():
    """
    主函数
    exmaple
    python example/search_tweets.py 'AI min_faves:100 since:2024-06-01 until:2025-06-15 has:media'
    'from:turingou -filter:replies since:2024-06-12 until:2024-06-14' --max-results 500
    (blockchain OR web3 OR crypto OR DeFi OR NFT OR metaverse) min_faves:500 since:2025-06-11 until:2025-06-18 has:links has:media lang:en -is:retweet
    这些关键词之一：blockchain web3 crypto DeFi NFT metaverse
    最低点赞数：500
    起始日期：2025-06-11
    结束日期：2025-06-18
    语言：English
    高级过滤：勾选"包含链接"和"包含媒体"，排除"仅显示转发"。

    """
    
    import argparse
    parser = argparse.ArgumentParser(description="搜索Twitter推文")
    parser.add_argument("query", help="搜索关键词（可包含 since/until/min_faves 等 Twitter 高级语法）")
    parser.add_argument("--type", choices=["Top", "Latest", "Media"], default="Top", help="搜索结果类型")
    parser.add_argument("--max-results", type=int, default=10, help="最大结果数量")
    parser.add_argument("--no-save", action="store_true", help="不保存结果到文件")
    parser.add_argument("--debug", action="store_true", help="debug")
    
    args = parser.parse_args()
    
    # 确保存储目录存在
    Path("data/search_results").mkdir(parents=True, exist_ok=True)
    
    # 搜索推文
    tweets = await search_tweets(
        query=args.query,
        product=args.type,
        max_results=args.max_results,
        save_to_file=not args.no_save
    )
    
    # 打印结果摘要
    if tweets:
        print("\n搜索结果摘要:")
        for i, tweet in enumerate(tweets[:5], 1):  # 只显示前5条
            print(f"\n{i}. 作者: @{tweet['user']['screen_name']}")
            print(f"   时间: {tweet['created_at']}")
            print(f"   内容: {tweet['text'][:100]}...")
            print(f"   互动: {tweet['favorite_count']}赞 {tweet['retweet_count']}转发")
            if tweet['media']:
                print(f"   媒体: {len(tweet['media'])}个附件")
        
        if len(tweets) > 5:
            print(f"\n...还有 {len(tweets) - 5} 条结果")
    else:
        print("\n未找到符合条件的推文")

if __name__ == "__main__":
    asyncio.run(main()) 