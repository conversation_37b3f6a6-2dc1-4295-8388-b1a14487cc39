import sys
import asyncio
from pathlib import Path

# 添加项目根目录到 Python 路径
if __name__ == "__main__":
    project_root = Path(__file__).resolve().parent.parent.parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))

from twikit import Client
from src.api.twitter_api_facade import smart_execute


async def test_account_restrictions(client: Client):
    try:
        lock_list = [
          "locked", "suspended", "restricted"
        ]
        user = await client.user()
        print(f"✅ 获取用户成功: {user.screen_name}")
        tweets = await user.get_tweets(tweet_type='Tweets')
        if tweets:
            print("✅ 获取时间线成功，账户可能未被限制")
            return True
        else:
            print("⚠️ 获取时间线失败，账户可能被限制")
            return False
    except Exception as e:
        error_message = str(e)
        print("="*80)
        print(f"❌ 操作失败：{error_message}, account_id: {getattr(client, '_account_id')} , proxy: {getattr(client, 'proxy')}")
        print("="*80)
        if any(lock_word in error_message.lower() for lock_word in lock_list):
            print("⚠️ 账户可能被冻结或限制\n")
        else:
            print("⚠️ 其他错误，可能与 API 限制或网络有关\n")
        return False


# 示例运行
async def main():
    from src.storage.database import Database
    from src.config import Config
    db = Database(Config())
    all_accounts = db.get_active_accounts()
    for _client in all_accounts:
        client = Client(language="en-US", proxy=_client.get("proxy"))
        client.set_cookies(_client["cookies"])
        client._account_id = _client.get("account_id") # type: ignore
        print(f"test account:{_client['account_id']} with proxy:{_client.get('proxy')}")
        await test_account_restrictions(client=client)

if __name__ == "__main__":
    asyncio.run(main())