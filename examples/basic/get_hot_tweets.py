import sys
import json
import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional, Literal, Tuple
from datetime import datetime, timedelta
from twikit import Client, Tweet
import signal

# 添加项目根目录到 Python 路径
if __name__ == "__main__":
    project_root = Path(__file__).resolve().parent.parent.parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))

from src.utils.logger import setup_logger
from src.utils.utils import tweet_to_dict
from src.api.twitter_api_facade import smart_execute
# 配置日志
logger = setup_logger(debug=True, module_name="get_hot_tweets")

class GracefulExit(Exception):
    pass

def signal_handler(signum, frame):
    raise GracefulExit()

def gen_query(query):
    if query:
        return query;
    search_terms = [
        "news", "breaking", "trending", "viral",
        "technology", "AI", "tech",
        "sports", "football", "basketball",
        "entertainment", "movie", "music",
        "politics", "world", "business",
        "elon musk","x", "twitter", 
        "twitter trends", "twitter trends today",
        "twitter trends this week",
        "twitter trends this month", "twitter trends this year"
    ]
    
    import random
    if query is None or query == '':
        selected_terms = random.sample(search_terms, random.randint(3, 5))
        query = " OR ".join(selected_terms)
        return query
async def get_hot_tweets(
    period: Literal["day", "week", "month"] = "day",
    min_likes: int = 100,
    min_retweets: int = 10,
    count: int = 20,
    save_to_file: bool = True,
    query: Optional[str] = None  # 新增 query 参数
) -> List[Dict[str, Any]]:
    """获取热门推文
    
    Args:
        period: 时间周期 (day: 当日, week: 本周, month: 本月)
        min_likes: 最小点赞数
        min_retweets: 最小转发数
        count: 每次请求的推文数量 (1-20)
        save_to_file: 是否保存结果到文件
        query: 自定义搜索 query
        
    Returns:
        符合条件的推文列表
    """
    logger.info(f"开始获取{period}热门推文，最小点赞数: {min_likes}, 最小转发数: {min_retweets}, 目标数量: {count}")
    
    # 使用热门话题和关键词来提高搜索效果
    query = gen_query(query)
    logger.info(f"搜索查询: {query}")
    
    # 使用 execute_with_retry 执行搜索
    logger.info("开始搜索推文...")
    all_tweets: List[Tweet] = []
    cursor = None
    page = 1
    
    while len(all_tweets) < count:
        # 定义带游标的搜索函数
        async def search_with_cursor(client: Client) -> Tuple[List[Tweet], Optional[str]]:
            nonlocal cursor
            tweets = await client.search_tweet(
                query=query or "",
                product="Top",
                count=min(count, 20),
                cursor=cursor
            )
            if not tweets:
                return [], None
            
            current_tweets = list(tweets)
            logger.info(f"获取第 {page} 页推文，当前已获取 {len(all_tweets) + len(current_tweets)} 条")
            
            next_cursor = None
            try:
                next_page = await tweets.next()
                if next_page:
                    next_cursor = tweets.next_cursor
            except Exception as e:
                logger.warning(f"获取下一页游标失败: {str(e)}")
            
            return current_tweets, next_cursor
        
        # 执行搜索
        current_tweets, next_cursor = await smart_execute(search_with_cursor, max_retries=10)
        if not current_tweets:
            break
        
        all_tweets.extend(current_tweets)
        cursor = next_cursor
        page += 1
        
        if not cursor:
            break
    
    if not all_tweets:
        logger.error("未找到任何推文")
        # raise Exception("No tweets found")
    logger.info(f"共获取 {len(all_tweets)} 条推文")
    
    # 按点赞数和转发数排序
    logger.info("开始按互动数排序...")
    sorted_tweets = sorted(
        all_tweets,
        key=lambda x: (x.favorite_count + x.retweet_count * 2),  # 转发权重更高
        reverse=True
    )
    
    # 过滤最小点赞数和转发数
    final_tweets = [
        tweet for tweet in sorted_tweets
        if tweet.favorite_count >= min_likes and tweet.retweet_count >= min_retweets
    ]
    logger.info(f"符合点赞和转发要求的推文共有 {len(final_tweets)} 条")
    
    # 转换为字典格式
    logger.info("开始处理推文数据...")
    result = []
    for tweet in final_tweets:
        result.append(tweet_to_dict(tweet))
    
    # 保存结果到文件
    if save_to_file:
        output_dir = Path("data/hot_tweets")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = output_dir / f"hot_tweets_{period}_{timestamp}.json"
        
        logger.info(f"正在保存推文到文件: {output_file}")
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, separators=(',',':'))
        logger.info(f"成功保存 {len(result)} 条推文到文件")
    
    return result

async def get_trends(
    category: str = "trending",
    count: int = 20,
    save_to_file: bool = True
) -> list:
    """获取 Twitter 热门趋势话题
    Args:
        category: 趋势类别（trending, for-you, news, sports, entertainment）
        count: 获取数量
        save_to_file: 是否保存到文件
    Returns:
        趋势列表
    """
    logger.info(f"开始获取 {category} 类别的 trends，数量: {count}")
    async def fetch_trends(client: Client, category, count: int):
        return await client.get_trends(category=category, count=count)
    trends = await smart_execute(fetch_trends, category=category, count=count, max_retries=10)
    logger.info(f"获取到 {len(trends)} 条 trends")
    # 转为 dict 便于保存
    trends_data = [{
        "name": trend.name,
        "tweets_count": trend.tweets_count,
        "domain_context": trend.domain_context,
        "grouped_trends": trend.grouped_trends
    } for trend in trends]
    if save_to_file:
        output_dir = Path("data/trends")
        output_dir.mkdir(parents=True, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = output_dir / f"trends_{category}_{timestamp}.json"
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(trends_data, f, ensure_ascii=False, separators=(',',':'))
        logger.info(f"已保存 trends 到文件: {output_file}")
    return trends_data

async def main():
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    init_query = ''
    try:
        trends_categories = ["trending", "news", "sports", "entertainment"]
        for category in trends_categories:
            if category in sys.argv:
                try:
                    await get_trends(category=category, count=20, save_to_file=True)
                except Exception as e:
                    logger.error(f"get_trends error: {str(e)}")
                return
        logger.info("开始获取热门推文...")
        # 固定查询条件
        base_query =  gen_query(init_query)
        periods: List[Literal["day", "week", "month"]] = ["day", "week", "month"]
        results = {}
        for period in periods:
            logger.info(f"获取{period}热门推文...")
            now = datetime.now()
            if period == "day":
                since = (now - timedelta(days=1)).strftime('%Y-%m-%d')
                until = now.strftime('%Y-%m-%d')
            elif period == "week":
                since = (now - timedelta(days=7)).strftime('%Y-%m-%d')
                until = now.strftime('%Y-%m-%d')
            elif period == "month":
                since = (now - timedelta(days=30)).strftime('%Y-%m-%d')
                until = now.strftime('%Y-%m-%d')
            period_query = f"{base_query} since:{since} until:{until}"
            logger.info(f"搜索查询: {period_query}")
            try:
                tweets = await get_hot_tweets(period=period, count=10, save_to_file=True, query=period_query)
                results[period] = tweets
                logger.info(f"{period}热门推文获取完成，共 {len(tweets)} 条")
            except Exception as e:
                logger.error(f"get_hot_tweets error: {str(e)}")
        logger.info("所有热门推文处理完成")

        # 获取并保存 trends 示例
        for category in trends_categories:
            try:
                await get_trends(category=category, count=20, save_to_file=True)
            except Exception as e:
                logger.error(f"get_trends error: {str(e)}")
        logger.info("所有 trends 获取并保存完成")
    
    except GracefulExit:
        logger.info("收到退出信号，正在关闭...")
    except Exception as e:
        logger.error(f"发生错误: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(main()) 