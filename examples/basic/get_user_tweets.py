import sys
import json
import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional, Literal
from datetime import datetime, timedelta, timezone
from twikit import Client, Tweet
from twikit.utils import Result
import signal

# 添加项目根目录到 Python 路径
if __name__ == "__main__":
    project_root = Path(__file__).resolve().parent.parent.parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))

from src.utils.logger import setup_logger
from src.utils.utils import tweet_to_dict

# 配置日志
logger = setup_logger(debug=True, module_name="basic/get_user_tweets")

class GracefulExit(Exception):
    pass

def handle_interrupt(signum, frame):
    logger.info("接收到中断信号，准备优雅退出...")
    raise GracefulExit()

def get_checkpoint_file(screen_name: str, tweet_type: str) -> Path:
    output_dir = Path("data/checkpoints")
    output_dir.mkdir(parents=True, exist_ok=True)
    return output_dir / f"{screen_name}_{tweet_type.lower()}_checkpoint.json"

def save_checkpoint(
    screen_name: str,
    tweet_type: str,
    tweets_data: List[Tweet],
    current_cursor: Optional[str],
    metadata: Dict[str, Any]
) -> None:
    checkpoint_file = get_checkpoint_file(screen_name, tweet_type)
    last_cursor, loaded_tweets,_ = load_checkpoint(screen_name, tweet_type)
    logger.debug(f'🕹️保存检查点: cursor: {last_cursor} -> {current_cursor}, 新增推文数={len(tweets_data)-len(loaded_tweets)}')
    if loaded_tweets:
        tweets_data = loaded_tweets + tweets_data
    seen = set()
    deduped_tweets: list[dict] = []
    # tweets_data mix Tweet and dict
    for tweet in tweets_data:
        tid = tweet.get('id') if isinstance(tweet, dict) else tweet.id
        d = tweet_to_dict(tweet) if isinstance(tweet, Tweet) else tweet
        if tid not in seen:
            seen.add(tid)
            deduped_tweets.append(d)

    checkpoint_data = {
        "screen_name": screen_name,
        "tweet_type": tweet_type,
        "last_cursor": current_cursor,
        "tweets": deduped_tweets,
        "metadata": metadata,
        "last_updated": datetime.now(timezone.utc).isoformat()
    }
    
    with open(checkpoint_file, 'w', encoding='utf-8') as f:
        json.dump(checkpoint_data, f, ensure_ascii=False, separators=(',', ':'))
    logger.info(f"检查点已保存到: {checkpoint_file}")

def load_checkpoint(screen_name: str, tweet_type: str) -> tuple[Optional[str], List[Tweet], Dict[str, Any]]:
    checkpoint_file = get_checkpoint_file(screen_name, tweet_type)
    if not checkpoint_file.exists():
        return None, [], {}
        
    try:
        with open(checkpoint_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data.get("last_cursor"), data.get("tweets", []), data.get("metadata", {})
    except Exception as e:
        logger.warning(f"读取检查点文件失败: {e}")
        return None, [], {}

async def get_user_tweets(
    screen_name: str,
    days: int = 7,
    tweet_type: Literal["Tweets", "Replies", "Media", "Likes"] = "Tweets",
    min_likes: int = 0,
    min_retweets: int = 0,
    save_to_file: bool = True,
    continue_from_checkpoint: bool = False
) -> List[Tweet]:
    """获取用户的推文
    
    Args:
        screen_name: 用户名
        days: 获取最近几天的推文
        tweet_type: 推文类型 (Tweets, Replies, Media, Likes)
        min_likes: 最小点赞数
        min_retweets: 最小转发数
        save_to_file: 是否保存结果到文件
        continue_from_checkpoint: 是否从上次的检查点继续
        
    Returns:
        符合条件的推文列表
    """
    try:
        max_retries = 3
        max_account_retries = 10
        from src.api.twitter_api_facade import smart_execute
        # 设置中断信号处理
        signal.signal(signal.SIGINT, handle_interrupt)
        signal.signal(signal.SIGTERM, handle_interrupt)
        
        async def get_user_id(client: Client, screen_name: str) -> str:
            user = await client.get_user_by_screen_name(screen_name)
            if not user:
                raise ValueError(f"找不到用户: {screen_name}")
            return user.id
        logger.info(f"🎬开始获取用户 {screen_name} 信息")
        user_id = await smart_execute(get_user_id, screen_name=screen_name, max_retries=max_account_retries)
        logger.info(f"✅获取用户 {screen_name} 信息成功")
        # 初始化或加载检查点
        current_cursor = None
        tweets_data: List[Tweet] = []
        metadata = {
            "start_time": datetime.now(timezone.utc).isoformat(),
            "params": {
                "days": days,
                "tweet_type": tweet_type,
                "min_likes": min_likes,
                "min_retweets": min_retweets
            }
        }
        
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days) if days > 0 else None
        retry_count = 0
        page_count = 0
        
        if continue_from_checkpoint:
            logger.debug(f"continue_from_checkpoint...\n")
            current_cursor, tweets_data, loaded_metadata = load_checkpoint(screen_name, tweet_type)
            page_count = loaded_metadata.get('last_page', 0)
            if current_cursor:
                logger.info(f"💾从检查点继续: cursor={current_cursor} page_count={page_count}, 已有推文数={len(tweets_data)}, loaded_metadata={loaded_metadata}")
                metadata.update(loaded_metadata)

        logger.info(f"开始获取用户 {screen_name} 的推文")
        logger.info(f"获取条件: 类型={tweet_type}, 最近{days}天, 最少{min_likes}个赞, 最少{min_retweets}个转发")
        
        try:
            while True:
                try:
                    async def fetch_page(client: Client, user_id: str, tweet_type: Literal['Tweets', 'Replies', 'Media', 'Likes'], cursor: Optional[str] = None) -> tuple[List[Tweet], Optional[str]]:
                        tweets_page = await client.get_user_tweets(
                            user_id, 
                            tweet_type=tweet_type, 
                            count=20,
                            cursor=cursor
                        )
                        
                        next_cursor = getattr(tweets_page, "next_cursor", None)
                        if not tweets_page and not next_cursor:
                            logger.debug(f"没有获取到推文{tweets_page}!")
                            return [], None
                        # 获取下一页的游标
                        return list(tweets_page), next_cursor

                    page_tweets, next_cursor = await smart_execute(
                        fetch_page,
                        user_id=user_id,
                        tweet_type=tweet_type,
                        cursor=current_cursor,
                        max_retries=max_account_retries
                    )
                    
                    page_count += 1
                    logger.info(f"获取第 {page_count} 页推文 (cursor: {current_cursor})(next_cursor： {next_cursor})")
                    
                    if not page_tweets:
                        logger.debug("没有获取到推文，结束获取")
                        break
                        
                    retry_count = 0  # 成功获取后重置重试计数
                    
                    # 处理当前页的推文
                    for tweet in page_tweets:
                        try:
                            # 检查推文时间
                            created_at = datetime.strptime(tweet.created_at, "%a %b %d %H:%M:%S %z %Y")
                            
                            if cutoff_date and created_at < cutoff_date:
                                logger.debug(f"达到时间限制（{created_at} < {cutoff_date}），停止获取更多推文")
                                raise StopIteration()
                            
                            # 检查互动数
                            favorite_count = getattr(tweet, "favorite_count", 0)
                            retweet_count = getattr(tweet, "retweet_count", 0)
                            
                            if favorite_count >= min_likes and retweet_count >= min_retweets:
                                tweets_data.append(tweet)
                                logger.debug(f"添加推文: {tweet.id}, 发布时间: {created_at}, 点赞: {favorite_count}, 转发: {retweet_count}")
                        except StopIteration:
                            raise
                        except ValueError as e:
                            logger.warning(f"处理推文时出错，跳过: {e}")
                            continue
                    
                    # 更新游标和保存检查点
                    current_cursor = next_cursor
                    metadata["last_page"] = page_count
                    metadata["last_processed_time"] = datetime.now(timezone.utc).isoformat()
                    
                    # 每获取一页就保存一次检查点
                    save_checkpoint(screen_name, tweet_type, tweets_data, current_cursor, metadata)
                    
                    if not current_cursor:
                        logger.info("没有下一页游标，结束获取")
                        break
                        
                except StopIteration:
                    break
                except Exception as e:
                    retry_count += 1
                    error_code = getattr(e, "code", None) or getattr(e, "status_code", None)
                    
                    # 检查是否达到最大重试次数
                    if retry_count >= max_retries:
                        logger.error(f"达到最大重试次数 ({max_retries})，结束获取")
                        break
                        
                    # 处理不同类型的错误
                    if str(403) in str(e) or str(429) in str(e):  # 速率限制或权限错误
                        wait_time = min(2 ** retry_count, 60)  # 最多等待60秒
                        logger.warning(f"获取推文时出错 (尝试 {retry_count}/{max_retries}): {str(e)} (code: {error_code})")
                        logger.warning(f"遇到速率限制或权限错误，等待 {wait_time} 秒后重试...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        logger.error(f"遇到未知错误: {str(e)}")
                        break
        
        except GracefulExit:
            logger.info("正在优雅退出，保存当前进度...")
            metadata["exit_reason"] = "user_interrupt"
        finally:
            # 保存最终结果
            metadata["end_time"] = datetime.now(timezone.utc).isoformat()
            metadata["total_tweets"] = len(tweets_data)
            save_checkpoint(screen_name, tweet_type, tweets_data, current_cursor, metadata)
            
            if save_to_file and tweets_data:
                output_dir = Path("data/tweets")
                output_dir.mkdir(parents=True, exist_ok=True)
                
                # 生成文件名
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{screen_name}_{tweet_type.lower()}_{timestamp}.json"
                output_file = output_dir / filename
                
                # 保存完整数据
                full_data = {
                    "metadata": metadata,
                    "last_cursor": current_cursor,
                    "tweets": tweets_data
                }
                
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(full_data, f, ensure_ascii=False, indent=2)
                logger.info(f"推文已保存到: {output_file}")
        
        return tweets_data
        
    except Exception as e:
        logger.error(f"获取推文时出错: {e}")
        return []

async def main():
    """主函数"""
    import argparse
    parser = argparse.ArgumentParser(description="获取Twitter用户的推文")
    parser.add_argument("screen_name", help="用户名")
    parser.add_argument("--days", type=int, default=7, help="获取最近几天的推文")
    parser.add_argument("--type", choices=["Tweets", "Replies", "Media", "Likes"], default="Tweets", help="推文类型")
    parser.add_argument("--min-likes", type=int, default=0, help="最小点赞数")
    parser.add_argument("--min-retweets", type=int, default=0, help="最小转发数")
    parser.add_argument("-d", "--debug", action="store_true", help="启用调试模式")
    parser.add_argument("--no-save", action="store_true", help="不保存结果到文件")
    parser.add_argument("-c", action="store_true", help="继续上次请求")
    parser.add_argument("--resume", action="store_true", help="继续上次请求")
    
    args = parser.parse_args()
    
    if args.debug:
        logger.setLevel("DEBUG")
    
    # 确保存储目录存在
    Path("data/tweets").mkdir(parents=True, exist_ok=True)
    
    # 获取推文
    tweets = await get_user_tweets(
        screen_name=args.screen_name,
        days=args.days,
        tweet_type=args.type,
        min_likes=args.min_likes,
        min_retweets=args.min_retweets,
        save_to_file=not args.no_save,
        continue_from_checkpoint=args.c or args.resume
    )
    
    # 打印结果摘要
    if tweets:
        print("\n获取到的推文:")
        for i, tweet in enumerate(tweets[:5], 1):  # 只显示前5条
            created_at = getattr(tweet, "created_at", "")
            text = getattr(tweet, "text", "")
            favorite_count = getattr(tweet, "favorite_count", 0)
            retweet_count = getattr(tweet, "retweet_count", 0)
            print(f"\n{i}. 发布时间: {created_at}")
            print(f"   内容: {text[:100]}...")
            print(f"   互动: {favorite_count}赞 {retweet_count}转发")
            if hasattr(tweet, "media"):
                print(f"   媒体: {len(tweet.media)}个附件")
        
        if len(tweets) > 5:
            print(f"\n...还有 {len(tweets) - 5} 条推文")
    else:
        print("\n未找到符合条件的推文")

if __name__ == "__main__":
    asyncio.run(main()) 