import sys
import json
import asyncio
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

# 添加项目根目录到 Python 路径
if __name__ == "__main__":
    project_root = Path(__file__).resolve().parent.parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))

from src.api.twitter_api_facade import smart_execute
from src.utils.logger import setup_logger

# 配置日志
logger = setup_logger(debug=True, module_name="monitor")

class MonitorConfig:
    """监控配置类"""
    def __init__(self):
        self.monitor_profile = True
        self.monitor_tweets = True
        self.monitor_relations = True
        self.monitor_mentions = True
        self.monitor_likes = False
        self.monitor_retweets = False
        self.interval = 300  # 监控间隔（秒）

class UserState:
    """用户状态类"""
    def __init__(self):
        self.profile: Optional[Dict[str, Any]] = None
        self.tweets = set()
        self.followers = set()
        self.following = set()
        self.mentions = set()
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "profile": self.profile,
            "tweets": list(self.tweets),
            "followers": list(self.followers),
            "following": list(self.following),
            "mentions": list(self.mentions),
            "timestamp": datetime.now().isoformat()
        }

async def collect_user_state(client, user_id: str, config: MonitorConfig) -> UserState:
    """收集用户状态"""
    state = UserState()
    
    # 获取用户资料
    if config.monitor_profile:
        user = await client.get_user_by_id(user_id)
        state.profile = {
            "id": user.id,
            "screen_name": user.screen_name,
            "name": user.name,
            "description": user.description,
            "followers_count": user.followers_count,
            "following_count": user.following_count,
            "tweet_count": user.tweet_count,
            "created_at": user.created_at.isoformat()
        }
    
    # 获取最新推文
    if config.monitor_tweets:
        tweets = await client.get_user_tweets(user_id, tweet_type="Tweets", count=100)
        state.tweets = {tweet.id for tweet in tweets}
    
    # 获取关注关系
    if config.monitor_relations:
        followers = await client.get_user_followers(user_id, count=100)
        following = await client.get_user_following(user_id, count=100)
        state.followers = {user.id for user in followers}
        state.following = {user.id for user in following}
    
    # 获取提及
    if config.monitor_mentions:
        if state.profile:  # 添加检查
            mentions = await client.search_tweets(f"@{state.profile['screen_name']}", tweet_type="Latest")
            state.mentions = {tweet.id for tweet in mentions}
        else:
            logger.warning("无法获取提及，因为用户资料不可用")
            state.mentions = set()
    
    return state

async def monitor_user(screen_name: str, config: MonitorConfig):
    """监控用户状态变化"""
    try:
        logger.info(f"开始监控用户: {screen_name}")
        
        # 使用 AccountManager 获取用户ID
        async def get_user_id(client, screen_name):
            user = await client.get_user_by_screen_name(screen_name)
            if not user:
                raise ValueError(f"找不到用户: {screen_name}")
            return user.id
            
        user_id = await smart_execute(get_user_id, priority="normal", screen_name=screen_name)
        
        # 创建输出目录
        output_dir = Path(f"data/monitor/{screen_name}")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        last_state = None
        while True:
            try:
                # 使用 AccountManager 收集用户状态
                async def fetch_state(client, user_id, config):
                    return await collect_user_state(client, user_id, config)
                    
                current_state = await smart_execute(
                    fetch_state,
                    priority="low",  # 监控任务使用低优先级
                    user_id=user_id,
                    config=config
                )
                
                # 检测变化
                if last_state:
                    changes = []
                    
                    # 检查个人资料变化
                    if config.monitor_profile and current_state.profile != last_state.profile:
                        changes.append("个人资料")
                    
                    # 检查新推文
                    if config.monitor_tweets:
                        new_tweets = current_state.tweets - last_state.tweets
                        if new_tweets:
                            changes.append(f"新推文({len(new_tweets)}条)")
                    
                    # 检查关注者变化
                    if config.monitor_relations:
                        new_followers = current_state.followers - last_state.followers
                        lost_followers = last_state.followers - current_state.followers
                        if new_followers or lost_followers:
                            changes.append(f"关注者变化(+{len(new_followers)}/-{len(lost_followers)})")
                    
                    # 检查新提及
                    if config.monitor_mentions:
                        new_mentions = current_state.mentions - last_state.mentions
                        if new_mentions:
                            changes.append(f"新提及({len(new_mentions)}条)")
                    
                    if changes:
                        logger.info(f"检测到变化: {', '.join(changes)}")
                        
                        # 保存状态快照
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        state_file = output_dir / f"state_{timestamp}.json"
                        
                        class SetEncoder(json.JSONEncoder):
                            def default(self, obj):
                                if isinstance(obj, set):
                                    return list(obj)
                                return json.JSONEncoder.default(self, obj)
                        
                        with open(state_file, 'w', encoding='utf-8') as f:
                            json.dump(current_state.to_dict(), f, ensure_ascii=False, indent=2, cls=SetEncoder)
                        logger.info(f"状态已保存到: {state_file}")
                
                last_state = current_state
                
            except Exception as e:
                logger.error(f"监控出错: {e}")
            
            await asyncio.sleep(config.interval)
            
    except Exception as e:
        logger.error(f"监控任务失败: {e}")

async def main():
    """主函数"""
    import argparse
    parser = argparse.ArgumentParser(description="监控Twitter用户")
    parser.add_argument("screen_name", help="用户名")
    parser.add_argument("--interval", type=int, default=300, help="监控间隔（秒）")
    parser.add_argument("--no-profile", action="store_true", help="不监控个人资料")
    parser.add_argument("--no-tweets", action="store_true", help="不监控推文")
    parser.add_argument("--no-relations", action="store_true", help="不监控关注关系")
    parser.add_argument("--no-mentions", action="store_true", help="不监控提及")
    
    args = parser.parse_args()
    
    # 创建配置
    config = MonitorConfig()
    config.interval = args.interval
    config.monitor_profile = not args.no_profile
    config.monitor_tweets = not args.no_tweets
    config.monitor_relations = not args.no_relations
    config.monitor_mentions = not args.no_mentions
    
    # 确保存储目录存在
    Path("data/monitor").mkdir(parents=True, exist_ok=True)
    
    # 开始监控
    await monitor_user(args.screen_name, config)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n监控已停止") 