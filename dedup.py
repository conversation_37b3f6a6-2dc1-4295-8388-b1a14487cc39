import sys
import json

if len(sys.argv) != 2:
    print("用法: python dedup.py <json文件路径>")
    sys.exit(1)

json_path = sys.argv[1]

with open(json_path, 'r', encoding='utf-8') as f:
    data = json.load(f)

tweets = data.get('tweets', [])
seen = set()
deduped = []
for tweet in tweets:
    tid = tweet.get('id')
    if tid not in seen:
        seen.add(tid)
        deduped.append(tweet)

data['tweets'] = deduped

with open(json_path, 'w', encoding='utf-8') as f:
    json.dump(data, f, ensure_ascii=False, indent=2)

print(f"去重完成，删除重复 {len(tweets) - len(deduped)}，剩余 {len(deduped)} 条 tweet，已保存到 {json_path}") 